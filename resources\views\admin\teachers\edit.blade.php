@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Edit Teacher"
        description="Update teacher profile and account information"
        :back-route="route('admin.teachers.show', $teacher)"
        back-label="Back to Profile">
    </x-page-header>

    <form method="POST" action="{{ route('admin.teachers.update', $teacher) }}" class="space-y-6">
        @csrf
        @method('PUT')
        
        <!-- Personal Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Personal Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Full Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input type="text" name="name" id="name" value="{{ old('name', $teacher->user->name) }}" 
                           class="form-input @error('name') border-red-500 @enderror" 
                           placeholder="Enter teacher's full name" required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Employee ID -->
                <div>
                    <label for="employee_id" class="block text-sm font-medium text-gray-700 mb-2">Employee ID</label>
                    <input type="text" name="employee_id" id="employee_id" value="{{ old('employee_id', $teacher->employee_id) }}" 
                           class="form-input @error('employee_id') border-red-500 @enderror" 
                           placeholder="e.g., TCH2024001" required>
                    @error('employee_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                    <input type="email" name="email" id="email" value="{{ old('email', $teacher->user->email) }}" 
                           class="form-input @error('email') border-red-500 @enderror" 
                           placeholder="<EMAIL>" required>
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Phone -->
                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                    <input type="text" name="phone" id="phone" value="{{ old('phone', $teacher->user->phone) }}" 
                           class="form-input @error('phone') border-red-500 @enderror" 
                           placeholder="+60123456789">
                    @error('phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Address -->
                <div class="md:col-span-2">
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                    <textarea name="address" id="address" rows="3" 
                              class="form-textarea @error('address') border-red-500 @enderror"
                              placeholder="Enter complete address">{{ old('address', $teacher->user->address) }}</textarea>
                    @error('address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Professional Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Professional Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Qualification -->
                <div>
                    <label for="qualification" class="block text-sm font-medium text-gray-700 mb-2">Qualification</label>
                    <input type="text" name="qualification" id="qualification" value="{{ old('qualification', $teacher->qualification) }}" 
                           class="form-input @error('qualification') border-red-500 @enderror" 
                           placeholder="e.g., Bachelor of Education, Master of Science">
                    @error('qualification')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Specialization -->
                <div>
                    <label for="specialization" class="block text-sm font-medium text-gray-700 mb-2">Specialization</label>
                    <input type="text" name="specialization" id="specialization" value="{{ old('specialization', $teacher->specialization) }}" 
                           class="form-input @error('specialization') border-red-500 @enderror" 
                           placeholder="e.g., Mathematics, Science, English">
                    @error('specialization')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Hire Date -->
                <div>
                    <label for="hire_date" class="block text-sm font-medium text-gray-700 mb-2">Hire Date</label>
                    <input type="date" name="hire_date" id="hire_date" value="{{ old('hire_date', $teacher->hire_date ? $teacher->hire_date->format('Y-m-d') : '') }}" 
                           class="form-input @error('hire_date') border-red-500 @enderror" 
                           max="{{ date('Y-m-d') }}">
                    @error('hire_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Salary -->
                <div>
                    <label for="salary" class="block text-sm font-medium text-gray-700 mb-2">Monthly Salary (RM)</label>
                    <input type="number" name="salary" id="salary" value="{{ old('salary', $teacher->salary) }}" 
                           class="form-input @error('salary') border-red-500 @enderror" 
                           placeholder="3500.00" step="0.01" min="0">
                    @error('salary')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Teaching Assignments -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Teaching Assignments</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Subjects -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Subjects</label>
                    <div id="subjects-container" class="space-y-2">
                        @if(!empty($teacher->subjects))
                            @foreach($teacher->subjects as $index => $subject)
                                <div class="flex items-center space-x-2">
                                    <input type="text" name="subjects[]" value="{{ $subject }}"
                                           class="form-input flex-1 @error('subjects') border-red-500 @enderror" 
                                           placeholder="Enter subject name">
                                    @if($index === 0)
                                        <button type="button" onclick="addSubjectField()" 
                                                class="px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                        </button>
                                    @else
                                        <button type="button" onclick="removeField(this)" 
                                                class="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    @endif
                                </div>
                            @endforeach
                        @else
                            <div class="flex items-center space-x-2">
                                <input type="text" name="subjects[]" 
                                       class="form-input flex-1 @error('subjects') border-red-500 @enderror" 
                                       placeholder="Enter subject name">
                                <button type="button" onclick="addSubjectField()" 
                                        class="px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </button>
                            </div>
                        @endif
                    </div>
                    @error('subjects')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Classes -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Classes</label>
                    <div id="classes-container" class="space-y-2">
                        @if(!empty($teacher->classes))
                            @foreach($teacher->classes as $index => $class)
                                <div class="flex items-center space-x-2">
                                    <input type="text" name="classes[]" value="{{ $class }}"
                                           class="form-input flex-1 @error('classes') border-red-500 @enderror" 
                                           placeholder="Enter class name">
                                    @if($index === 0)
                                        <button type="button" onclick="addClassField()" 
                                                class="px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                        </button>
                                    @else
                                        <button type="button" onclick="removeField(this)" 
                                                class="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    @endif
                                </div>
                            @endforeach
                        @else
                            <div class="flex items-center space-x-2">
                                <input type="text" name="classes[]" 
                                       class="form-input flex-1 @error('classes') border-red-500 @enderror" 
                                       placeholder="Enter class name">
                                <button type="button" onclick="addClassField()" 
                                        class="px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </button>
                            </div>
                        @endif
                    </div>
                    @error('classes')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Account Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Account Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Account Status -->
                <div class="md:col-span-2">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" 
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                               {{ old('is_active', $teacher->user->is_active) ? 'checked' : '' }}>
                        <span class="ml-2 text-sm text-gray-700">Account is active</span>
                    </label>
                    @error('is_active')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Password -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                    <input type="password" name="password" id="password" 
                           class="form-input @error('password') border-red-500 @enderror" 
                           placeholder="Leave blank to keep current password">
                    @error('password')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Confirm Password -->
                <div>
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                    <input type="password" name="password_confirmation" id="password_confirmation" 
                           class="form-input @error('password_confirmation') border-red-500 @enderror" 
                           placeholder="Confirm new password">
                    @error('password_confirmation')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-4">
            <a href="{{ route('admin.teachers.show', $teacher) }}" class="btn-secondary">Cancel</a>
            <button type="submit" class="btn-primary">Update Teacher</button>
        </div>
    </form>
</div>

@push('scripts')
<script>
function addSubjectField() {
    const container = document.getElementById('subjects-container');
    const div = document.createElement('div');
    div.className = 'flex items-center space-x-2';
    div.innerHTML = `
        <input type="text" name="subjects[]" 
               class="form-input flex-1" 
               placeholder="Enter subject name">
        <button type="button" onclick="removeField(this)" 
                class="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;
    container.appendChild(div);
}

function addClassField() {
    const container = document.getElementById('classes-container');
    const div = document.createElement('div');
    div.className = 'flex items-center space-x-2';
    div.innerHTML = `
        <input type="text" name="classes[]" 
               class="form-input flex-1" 
               placeholder="Enter class name">
        <button type="button" onclick="removeField(this)" 
                class="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;
    container.appendChild(div);
}

function removeField(button) {
    button.parentElement.remove();
}
</script>
@endpush
@endsection
