<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\Grade;
use App\Models\Subject;
use App\Models\AcademicTerm;
use App\Models\GradeCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GradeController extends Controller
{
    /**
     * Display student's grades
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $student = $user->student;

        if (!$student) {
            abort(403, 'Student profile not found.');
        }

        $search = $request->get('search', '');
        $subjectId = $request->get('subject_id', '');
        $termId = $request->get('academic_term_id', '');
        $categoryId = $request->get('category_id', '');

        // Build grades query for this student only
        $query = Grade::where('student_id', $student->id)
            ->where('is_published', true) // Only show published grades
            ->with([
                'subject',
                'teacher.user',
                'gradeCategory',
                'academicTerm'
            ]);

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('assignment_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('subject', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('gradeCategory', function ($cq) use ($search) {
                      $cq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Apply filters
        if ($subjectId) {
            $query->where('subject_id', $subjectId);
        }

        if ($termId) {
            $query->where('academic_term_id', $termId);
        }

        if ($categoryId) {
            $query->where('grade_category_id', $categoryId);
        }

        $grades = $query->latest('assignment_date')->paginate(25);

        // Get filter options (only for subjects this student is enrolled in)
        $subjects = Subject::whereHas('grades', function ($q) use ($student) {
            $q->where('student_id', $student->id)->where('is_published', true);
        })->orderBy('name')->get();

        $academicTerms = AcademicTerm::whereHas('grades', function ($q) use ($student) {
            $q->where('student_id', $student->id)->where('is_published', true);
        })->orderBy('name')->get();

        $gradeCategories = GradeCategory::whereHas('grades', function ($q) use ($student) {
            $q->where('student_id', $student->id)->where('is_published', true);
        })->orderBy('name')->get();

        // Calculate statistics
        $stats = [
            'total_grades' => Grade::where('student_id', $student->id)->where('is_published', true)->count(),
            'average_percentage' => Grade::where('student_id', $student->id)->where('is_published', true)->avg('percentage') ?? 0,
            'highest_score' => Grade::where('student_id', $student->id)->where('is_published', true)->max('percentage') ?? 0,
            'subjects_count' => $subjects->count(),
        ];

        // Calculate GPA
        $totalGpaPoints = Grade::where('student_id', $student->id)
            ->where('is_published', true)
            ->sum('gpa_points');
        $totalGrades = Grade::where('student_id', $student->id)
            ->where('is_published', true)
            ->count();
        
        $stats['gpa'] = $totalGrades > 0 ? $totalGpaPoints / $totalGrades : 0;

        return view('student.grades.index', compact(
            'grades',
            'subjects',
            'academicTerms',
            'gradeCategories',
            'stats',
            'search',
            'subjectId',
            'termId',
            'categoryId',
            'student'
        ));
    }

    /**
     * Display a specific grade
     */
    public function show(Grade $grade)
    {
        $user = Auth::user();
        $student = $user->student;

        if (!$student || $grade->student_id !== $student->id) {
            abort(403, 'Access denied.');
        }

        if (!$grade->is_published) {
            abort(404, 'Grade not found.');
        }

        $grade->load([
            'subject',
            'teacher.user',
            'gradeCategory',
            'academicTerm'
        ]);

        return view('student.grades.show', compact('grade', 'student'));
    }

    /**
     * Get grades by subject for student dashboard
     */
    public function getGradesBySubject()
    {
        $user = Auth::user();
        $student = $user->student;

        if (!$student) {
            return response()->json(['error' => 'Student profile not found'], 403);
        }

        $gradesBySubject = Grade::where('student_id', $student->id)
            ->where('is_published', true)
            ->with(['subject', 'gradeCategory'])
            ->get()
            ->groupBy('subject.name')
            ->map(function ($grades) {
                $totalPoints = $grades->sum('gpa_points');
                $count = $grades->count();
                $avgPercentage = $grades->avg('percentage');
                
                return [
                    'subject_name' => $grades->first()->subject->name,
                    'grade_count' => $count,
                    'average_percentage' => round($avgPercentage, 2),
                    'gpa' => $count > 0 ? round($totalPoints / $count, 2) : 0,
                    'latest_grade' => $grades->sortByDesc('assignment_date')->first(),
                ];
            });

        return response()->json($gradesBySubject);
    }

    /**
     * Export student's grades
     */
    public function export(Request $request, $format = 'csv')
    {
        $user = Auth::user();
        $student = $user->student;

        if (!$student) {
            abort(403, 'Student profile not found.');
        }

        $grades = Grade::where('student_id', $student->id)
            ->where('is_published', true)
            ->with([
                'subject',
                'teacher.user',
                'gradeCategory',
                'academicTerm'
            ])
            ->latest('assignment_date')
            ->get();

        $filename = 'my_grades_' . now()->format('Y_m_d_H_i_s') . '.' . $format;

        if ($format === 'csv') {
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];

            $callback = function() use ($grades) {
                $file = fopen('php://output', 'w');

                // CSV headers
                fputcsv($file, [
                    'Subject',
                    'Assignment Name',
                    'Score',
                    'Max Score',
                    'Percentage',
                    'Letter Grade',
                    'Category',
                    'Term',
                    'Teacher',
                    'Assignment Date',
                    'Comments'
                ]);

                // CSV data
                foreach ($grades as $grade) {
                    fputcsv($file, [
                        $grade->subject->name ?? '',
                        $grade->assignment_name,
                        $grade->score,
                        $grade->max_score,
                        round($grade->percentage, 2),
                        $grade->letter_grade,
                        $grade->gradeCategory->name ?? '',
                        $grade->academicTerm->name ?? '',
                        $grade->teacher->user->name ?? '',
                        $grade->assignment_date,
                        $grade->comments ?? ''
                    ]);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        }

        return back()->with('error', 'Export format not supported.');
    }
}
