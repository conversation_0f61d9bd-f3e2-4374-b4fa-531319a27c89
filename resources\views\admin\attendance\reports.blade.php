@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Attendance Reports</h1>
                <p class="text-gray-600">Generate and view attendance reports</p>
            </div>
            <a href="{{ route('admin.attendance.index') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Attendance
            </a>
        </div>
    </div>

    <!-- Report Filters -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Report Filters</h3>
        <div class="flex flex-wrap gap-4">
            <div class="flex-1 min-w-0">
                <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                <input type="date" id="start_date" name="start_date"
                       value="{{ $startDate }}" required
                       class="form-input w-full">
            </div>
            <div class="flex-1 min-w-0">
                <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                <input type="date" id="end_date" name="end_date"
                       value="{{ $endDate }}" required
                       class="form-input w-full">
            </div>
            <div class="flex-1 min-w-0">
                <label for="report_type" class="block text-sm font-medium text-gray-700 mb-1">Report Type</label>
                <select id="report_type" name="report_type"
                        class="form-select w-full">
                    <option value="summary" {{ $reportType === 'summary' ? 'selected' : '' }}>Summary</option>
                    <option value="detailed" {{ $reportType === 'detailed' ? 'selected' : '' }}>Detailed</option>
                </select>
            </div>
            <div class="flex-1 min-w-0">
                <label for="class" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
                <select id="class" name="class"
                        class="form-select w-full">
                    <option value="">All Classes</option>
                    @foreach($classes as $classOption)
                        <option value="{{ $classOption }}" {{ $class === $classOption ? 'selected' : '' }}>
                            {{ $classOption }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="flex-1 min-w-0">
                <label for="section" class="block text-sm font-medium text-gray-700 mb-1">Section</label>
                <select id="section" name="section"
                        class="form-select w-full">
                    <option value="">All Sections</option>
                    @foreach($sections as $sectionOption)
                        <option value="{{ $sectionOption }}" {{ $section === $sectionOption ? 'selected' : '' }}>
                            {{ $sectionOption }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="flex-shrink-0 flex items-end space-x-3">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">&nbsp;</label>
                    <button type="button" onclick="generateReport()"
                            class="inline-flex items-center bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 h-12 justify-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        Generate
                    </button>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">&nbsp;</label>
                    <button type="button" onclick="clearFilters()"
                            class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Clear All Filters
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if($reportType === 'summary')
        <!-- Summary Report -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Student Attendance Summary -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Student Attendance Summary</h3>
                    <p class="text-sm text-gray-500">{{ $reportData['period'] ?? '' }}</p>
                </div>
                <div class="p-6">
                    @if(isset($reportData['student_stats']))
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
                            <div class="bg-green-50 rounded-lg p-4">
                                <div class="text-2xl font-bold text-green-600">{{ $reportData['student_stats']['present'] }}</div>
                                <div class="text-sm text-gray-600">Present</div>
                            </div>
                            <div class="bg-red-50 rounded-lg p-4">
                                <div class="text-2xl font-bold text-red-600">{{ $reportData['student_stats']['absent'] }}</div>
                                <div class="text-sm text-gray-600">Absent</div>
                            </div>
                            <div class="bg-yellow-50 rounded-lg p-4">
                                <div class="text-2xl font-bold text-yellow-600">{{ $reportData['student_stats']['late'] ?? 0 }}</div>
                                <div class="text-sm text-gray-600">Late</div>
                            </div>
                            <div class="bg-blue-50 rounded-lg p-4">
                                <div class="text-2xl font-bold text-blue-600">{{ $reportData['student_stats']['excused'] ?? 0 }}</div>
                                <div class="text-sm text-gray-600">Excused</div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="text-2xl font-bold text-gray-600">{{ $reportData['student_stats']['sick'] ?? 0 }}</div>
                                <div class="text-sm text-gray-600">Sick</div>
                            </div>
                            <div class="bg-purple-50 rounded-lg p-4">
                                <div class="text-2xl font-bold text-purple-600">{{ $reportData['student_stats']['attendance_percentage'] }}%</div>
                                <div class="text-sm text-gray-600">Attendance Rate</div>
                            </div>
                        </div>
                    @else
                        <p class="text-gray-500 text-center py-8">No student attendance data available for the selected period.</p>
                    @endif
                </div>
            </div>
            <!-- Teacher Attendance Summary -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Teacher Attendance Summary</h3>
                    <p class="text-sm text-gray-500">{{ $reportData['period'] ?? '' }}</p>
                </div>
                <div class="p-6">
                    @if(isset($reportData['teacher_stats']))
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
                            <div class="bg-green-50 rounded-lg p-4">
                                <div class="text-2xl font-bold text-green-600">{{ $reportData['teacher_stats']['present'] }}</div>
                                <div class="text-sm text-gray-600">Present</div>
                            </div>
                            <div class="bg-red-50 rounded-lg p-4">
                                <div class="text-2xl font-bold text-red-600">{{ $reportData['teacher_stats']['absent'] }}</div>
                                <div class="text-sm text-gray-600">Absent</div>
                            </div>
                            <div class="bg-yellow-50 rounded-lg p-4">
                                <div class="text-2xl font-bold text-yellow-600">{{ $reportData['teacher_stats']['late'] ?? 0 }}</div>
                                <div class="text-sm text-gray-600">Late</div>
                            </div>
                            <div class="bg-blue-50 rounded-lg p-4">
                                <div class="text-2xl font-bold text-blue-600">{{ $reportData['teacher_stats']['official_duty'] ?? 0 }}</div>
                                <div class="text-sm text-gray-600">Official Duty</div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="text-2xl font-bold text-gray-600">{{ $reportData['teacher_stats']['sick_leave'] ?? 0 }}</div>
                                <div class="text-sm text-gray-600">Sick Leave</div>
                            </div>
                            <div class="bg-purple-50 rounded-lg p-4">
                                <div class="text-2xl font-bold text-purple-600">{{ $reportData['teacher_stats']['attendance_percentage'] }}%</div>
                                <div class="text-sm text-gray-600">Attendance Rate</div>
                            </div>
                        </div>
                    @else
                        <p class="text-gray-500 text-center py-8">No teacher attendance data available for the selected period.</p>
                    @endif
                </div>
            </div>
        </div>
    @elseif($reportType === 'detailed')
        <!-- Detailed Report -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Detailed Student Attendance Report</h3>
                @if($class || $section)
                    <p class="text-sm text-gray-500">
                        @if($class) Class: {{ $class }} @endif
                        @if($section) Section: {{ $section }} @endif
                    </p>
                @endif
            </div>
            <div class="p-6">
                @if(isset($reportData['students']) && $reportData['students']->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Present</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Absent</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Late</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Excused</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Sick</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance %</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($reportData['students'] as $studentData)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-8 w-8">
                                                    <div class="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                        <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium text-gray-900">{{ $studentData['student']->user->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $studentData['student']->student_id }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $studentData['student']->class }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $studentData['student']->section }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                {{ $studentData['stats']['present'] }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                {{ $studentData['stats']['absent'] }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                {{ $studentData['stats']['late'] ?? 0 }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ $studentData['stats']['excused'] ?? 0 }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                {{ $studentData['stats']['sick'] ?? 0 }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                {{ $studentData['stats']['attendance_percentage'] }}%
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-gray-500 text-center py-8">No student data available for the selected criteria.</p>
                @endif
            </div>
        </div>
    @endif
</div>

@push('scripts')
<script>
// Instant filtering functionality
function generateReport() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const reportType = document.getElementById('report_type').value;
    const classValue = document.getElementById('class').value;
    const section = document.getElementById('section').value;

    if (!startDate || !endDate) {
        showToast('Please select both start and end dates', 'error');
        return;
    }

    const params = new URLSearchParams();
    params.append('start_date', startDate);
    params.append('end_date', endDate);
    params.append('report_type', reportType);
    if (classValue) params.append('class', classValue);
    if (section) params.append('section', section);

    window.location.href = '{{ route("admin.attendance.reports") }}?' + params.toString();
}

function clearFilters() {
    window.location.href = '{{ route("admin.attendance.reports") }}';
}

// Add event listeners for instant filtering
document.addEventListener('DOMContentLoaded', function() {
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const reportTypeSelect = document.getElementById('report_type');
    const classSelect = document.getElementById('class');
    const sectionSelect = document.getElementById('section');

    // Auto-generate when filters change
    startDateInput.addEventListener('change', generateReport);
    endDateInput.addEventListener('change', generateReport);
    reportTypeSelect.addEventListener('change', generateReport);
    classSelect.addEventListener('change', generateReport);
    sectionSelect.addEventListener('change', generateReport);
});
</script>
@endpush
@endsection
