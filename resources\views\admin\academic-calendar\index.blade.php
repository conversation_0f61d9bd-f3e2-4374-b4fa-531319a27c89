@extends('layouts.app')

@section('title', 'Academic Calendar')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Academic Calendar"
        description="Manage academic schedules, events, and announcements"
        :back-route="route('admin.dashboard')"
        back-label="Back to Dashboard">
        <div class="flex items-center space-x-3">
            <!-- Add Event Button -->
            <button onclick="showAddEventModal()" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Event
            </button>

            <!-- Manage Dropdown -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-10">
                    Manage
                    <svg class="w-4 h-4 ml-2 transition-transform duration-200" :class="open ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                    <div class="py-1">
                        <a href="{{ route('admin.academic-years.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Academic Years</a>
                        <a href="{{ route('admin.academic-terms.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Academic Terms</a>
                        <a href="{{ route('admin.time-slots.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Time Slots</a>
                        <a href="{{ route('admin.schedules.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Schedules</a>
                        <a href="{{ route('admin.school-events.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Events</a>
                        <a href="{{ route('admin.announcements.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Announcements</a>
                    </div>
                </div>
            </div>
        </div>
    </x-page-header>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6" x-data="{
        searchQuery: '',
        selectedClass: '{{ $selectedClass }}',
        selectedSection: '{{ $selectedSection }}',
        selectedTerm: '{{ $selectedTerm }}',
        showAdvanced: false
    }">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            <!-- Search Input -->
            <div class="flex-1">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input
                        type="text"
                        x-model="searchQuery"
                        @input="filterSchedules()"
                        class="search-input"
                        placeholder="Search schedules, subjects, teachers, events..."
                    >
                </div>
            </div>

            <!-- Filter Toggle and Clear Buttons -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-10"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                    <svg class="w-4 h-4 ml-1 transition-transform duration-200" :class="showAdvanced ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <!-- Clear All Filters -->
                <button
                    @click="clearCalendarFilters()"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-10"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 transform translate-y-0" x-transition:leave-end="opacity-0 transform -translate-y-2" class="mt-4 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Class Filter -->
                <div>
                    <label for="class-filter" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
                    <select
                        id="class-filter"
                        x-model="selectedClass"
                        @change="updateFilters()"
                        class="form-select"
                    >
                        <option value="">All Classes</option>
                        @foreach($classes as $class)
                            <option value="{{ $class->id }}">{{ $class->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Section Filter -->
                <div>
                    <label for="section-filter" class="block text-sm font-medium text-gray-700 mb-1">Section</label>
                    <select
                        id="section-filter"
                        x-model="selectedSection"
                        @change="updateFilters()"
                        class="form-select"
                    >
                        <option value="">All Sections</option>
                        @foreach($sections as $section)
                            <option value="{{ $section->id }}">{{ $section->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Term Filter -->
                <div>
                    <label for="term-filter" class="block text-sm font-medium text-gray-700 mb-1">Academic Term</label>
                    <select
                        id="term-filter"
                        x-model="selectedTerm"
                        @change="updateFilters()"
                        class="form-select"
                    >
                        @foreach($academicTerms as $term)
                            <option value="{{ $term->id }}">{{ $term->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Classes</dt>
                            <dd class="stat-card-value">{{ $classes->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Time Slots</dt>
                            <dd class="stat-card-value">{{ $timeSlots->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Events</dt>
                            <dd class="stat-card-value">{{ $upcomingEvents->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Announcements</dt>
                            <dd class="stat-card-value">{{ $announcements->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Announcements & Events -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Announcements -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Recent Announcements</h3>
                    <a href="{{ route('admin.announcements.index') }}" class="text-sm text-blue-600 hover:text-blue-800">View all</a>
                </div>
            </div>
            <div class="p-6">
                @forelse($announcements as $announcement)
                    <div class="mb-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <div class="flex items-start justify-between">
                            <h4 class="text-sm font-medium text-gray-900">{{ $announcement->title }}</h4>
                            @if($announcement->is_pinned ?? false)
                                <svg class="w-4 h-4 text-yellow-500 flex-shrink-0 ml-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            @endif
                        </div>
                        <p class="text-xs text-gray-600 mt-2">{{ Str::limit($announcement->content ?? 'No content available', 100) }}</p>
                        <div class="flex items-center justify-between mt-3">
                            <span class="badge badge-blue">
                                {{ ucfirst($announcement->type ?? 'general') }}
                            </span>
                            <span class="text-xs text-gray-500">{{ $announcement->created_at->format('M d, Y') }}</span>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                        </svg>
                        <p class="text-sm text-gray-500 mt-2">No announcements available.</p>
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Upcoming Events -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Upcoming Events</h3>
                    <a href="{{ route('admin.school-events.index') }}" class="text-sm text-blue-600 hover:text-blue-800">View all</a>
                </div>
            </div>
            <div class="p-6">
                @forelse($upcomingEvents as $event)
                    <div class="mb-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <h4 class="text-sm font-medium text-gray-900">{{ $event->title }}</h4>
                        <div class="flex items-center justify-between mt-2">
                            <span class="text-xs text-gray-600">{{ $event->event_date->format('M d, Y') }}</span>
                            <span class="badge badge-purple">
                                {{ ucfirst($event->type ?? 'event') }}
                            </span>
                        </div>
                        @if($event->start_time && $event->end_time)
                            <p class="text-xs text-gray-500 mt-1">
                                {{ $event->start_time->format('g:i A') }} - {{ $event->end_time->format('g:i A') }}
                            </p>
                        @endif
                        @if($event->location)
                            <p class="text-xs text-gray-500 mt-1">
                                <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                {{ $event->location }}
                            </p>
                        @endif
                    </div>
                @empty
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <p class="text-sm text-gray-500 mt-2">No upcoming events.</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Live filtering functions for calendar
function filterSchedules() {
    const searchQuery = document.querySelector('[x-model="searchQuery"]').value.toLowerCase();

    // Get all filterable schedule items
    const scheduleItems = document.querySelectorAll('.schedule-item');

    scheduleItems.forEach(item => {
        const searchText = item.getAttribute('data-search-text') || item.textContent;
        const isVisible = !searchQuery || searchText.toLowerCase().includes(searchQuery);

        // Show/hide the parent time slot row if no schedules match
        const parentRow = item.closest('[data-filterable]');
        if (parentRow) {
            const visibleItems = parentRow.querySelectorAll('.schedule-item:not([style*="display: none"])');
            parentRow.style.display = (visibleItems.length > 0 || !searchQuery) ? '' : 'none';
        }

        item.style.display = isVisible ? '' : 'none';
    });
}

function updateFilters() {
    const selectedClass = document.querySelector('[x-model="selectedClass"]').value;
    const selectedSection = document.querySelector('[x-model="selectedSection"]').value;
    const selectedTerm = document.querySelector('[x-model="selectedTerm"]').value;

    // Build URL with current filters
    const url = new URL(window.location.href);
    url.searchParams.set('class_id', selectedClass);
    url.searchParams.set('section_id', selectedSection);
    url.searchParams.set('academic_term_id', selectedTerm);

    // Redirect to update the page
    window.location.href = url.toString();
}

function clearCalendarFilters() {
    // Reset form inputs
    const searchInput = document.querySelector('[x-model="searchQuery"]');
    const classInput = document.querySelector('[x-model="selectedClass"]');
    const sectionInput = document.querySelector('[x-model="selectedSection"]');
    const termInput = document.querySelector('[x-model="selectedTerm"]');

    if (searchInput) {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    }

    // Reset to default values and update
    if (classInput) classInput.value = '';
    if (sectionInput) sectionInput.value = '';
    if (termInput && termInput.options.length > 0) {
        termInput.selectedIndex = 0;
    }

    // Show all schedule items
    const scheduleItems = document.querySelectorAll('.schedule-item');
    const scheduleRows = document.querySelectorAll('[data-filterable]');

    scheduleItems.forEach(item => {
        item.style.display = '';
    });

    scheduleRows.forEach(row => {
        row.style.display = '';
    });

    // Update URL to clear filters
    updateFilters();
}

// Modal functions
function showTodayModal() {
    // Navigate to today's date
    const today = new Date().toISOString().split('T')[0];
    const params = new URLSearchParams(window.location.search);
    params.set('week', today);
    window.location.href = window.location.pathname + '?' + params.toString();
}

function showAddEventModal() {
    // Navigate to add event page
    window.location.href = '{{ route("admin.school-events.create") }}';
}

// Calendar navigation helpers
function navigateToDate(date) {
    const params = new URLSearchParams(window.location.search);
    params.set('week', date);
    window.location.href = window.location.pathname + '?' + params.toString();
}

// Initialize calendar functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for calendar events
    document.querySelectorAll('.calendar-event').forEach(event => {
        event.addEventListener('click', function() {
            const eventId = this.getAttribute('data-event-id');
            if (eventId) {
                // Show event details modal or navigate to event page
                window.location.href = `/admin/school-events/${eventId}`;
            }
        });
    });
});
</script>
@endpush
