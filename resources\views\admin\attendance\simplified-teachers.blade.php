@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Teacher Attendance"
        description="Mark and manage teacher attendance records">
        
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.attendance.reports') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Reports
            </a>
        </div>
    </x-page-header>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Present</dt>
                            <dd class="stat-card-value">{{ $attendanceStats['present'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-red-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Absent</dt>
                            <dd class="stat-card-value">{{ $attendanceStats['absent'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Teachers</dt>
                            <dd class="stat-card-value">{{ $attendanceStats['total_records'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Attendance Rate</dt>
                            <dd class="stat-card-value">{{ number_format($attendanceStats['attendance_percentage'], 1) }}%</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6"
         x-data="{
             searchQuery: '{{ $search }}',
             date: '{{ $date }}',
             status: '{{ $status }}',
             showAdvanced: {{ ($date || $status) ? 'true' : 'false' }}
         }">

        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
            <!-- Search Input -->
            <div class="flex-1">
                <input
                    type="text"
                    id="search"
                    value="{{ $search }}"
                    placeholder="Search teachers by name, employee ID, specialization..."
                    class="form-input w-full"
                >
            </div>

            <!-- Filter Toggle and Clear Buttons -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                </button>

                <button
                    onclick="clearFilters()"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6m0 0l6-6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input type="date" id="date" value="{{ $date }}"
                       class="form-input w-full">
            </div>

            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="status" class="form-select w-full">
                    <option value="">All Status</option>
                    <option value="present" {{ $status === 'present' ? 'selected' : '' }}>Present</option>
                    <option value="absent" {{ $status === 'absent' ? 'selected' : '' }}>Absent</option>
                    <option value="not_marked" {{ $status === 'not_marked' ? 'selected' : '' }}>Not Marked</option>
                </select>
            </div>
        </div>

        <!-- Bulk Selection Info -->
        <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-600">
                <span class="italic">Select teachers and use individual action buttons to mark attendance</span>
            </div>
            <div class="text-sm text-gray-500">
                <span id="selectedCount">0 selected</span>
            </div>
        </div>
    </div>

    <!-- Teachers Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Teachers - {{ \Carbon\Carbon::parse($date)->format('M d, Y') }}</h3>
        </div>

        @if($teachers->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="selectAllTable" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($teachers as $teacher)
                            @php
                                $attendance = $teacher->attendances->first();
                            @endphp
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="teacher-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                           value="{{ $teacher->id }}">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                                <span class="text-sm font-medium text-gray-600">
                                                    {{ substr($teacher->user->name, 0, 2) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $teacher->user->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $teacher->specialization }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $teacher->employee_id }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($attendance)
                                        <span class="badge badge-{{ $attendance->status_badge_color }}">
                                            {{ $attendance->full_status_display }}
                                        </span>
                                    @else
                                        <span class="badge badge-gray">Not Marked</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">
                                    {{ $attendance->notes ?? '-' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                    <div class="flex items-center justify-center space-x-2">
                                        @if($attendance)
                                            <button onclick="markAttendance({{ $teacher->id }}, '{{ $teacher->user->name }}', '{{ $attendance->status }}', '{{ $attendance->subcategory }}', '{{ $attendance->notes }}')"
                                                    class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition-colors duration-200 cursor-pointer"
                                                    title="Edit Attendance">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                Edit
                                            </button>
                                        @else
                                            <button onclick="markAttendance({{ $teacher->id }}, '{{ $teacher->user->name }}', '', '', '')"
                                                    class="inline-flex items-center px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition-colors duration-200 cursor-pointer"
                                                    title="Mark Attendance">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                </svg>
                                                Mark
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="px-6 py-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No teachers found</h3>
                <p class="mt-1 text-sm text-gray-500">No teachers match your current filters.</p>
            </div>
        @endif
    </div>
</div>

<!-- Individual Attendance Modal -->
<div id="attendanceModal" class="fixed inset-0 overflow-y-auto h-full w-full hidden z-50" style="background-color: rgba(17, 24, 39, 0.6);">
    <div class="relative top-20 mx-auto p-6 w-96 shadow-xl rounded-lg bg-white">
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-6" id="modalTitle">Mark Attendance</h3>
            <form id="attendanceForm">
                <input type="hidden" id="teacherId" name="teacher_id">
                <input type="hidden" name="date" value="{{ $date }}">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Teacher</label>
                    <p id="teacherName" class="text-sm text-gray-900 font-medium bg-gray-50 p-3 rounded"></p>
                </div>

                <div class="mb-4">
                    <label for="attendanceStatus" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="attendanceStatus" name="status" required class="form-select w-full">
                        <option value="present">Present</option>
                        <option value="absent">Absent</option>
                    </select>
                </div>

                <div class="mb-4" id="subcategoryDiv" style="display: none;">
                    <label for="attendanceSubcategory" class="block text-sm font-medium text-gray-700 mb-2">Subcategory</label>
                    <select id="attendanceSubcategory" name="subcategory"
                            class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none">
                        <option value="">Select subcategory (optional)</option>
                        <option value="Outstation">Outstation</option>
                        <option value="Medical Leave">Medical Leave</option>
                        <option value="Annual Leave">Annual Leave</option>
                        <option value="Emergency Leave">Emergency Leave</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <div class="mb-6">
                    <label for="attendanceNotes" class="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                    <textarea id="attendanceNotes" name="notes" rows="3"
                              class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none"
                              placeholder="Add any additional notes..."></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Save Attendance</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Attendance Modal -->
<div id="bulkAttendanceModal" class="fixed inset-0 overflow-y-auto h-full w-full hidden z-50" style="background-color: rgba(17, 24, 39, 0.6);">
    <div class="relative top-20 mx-auto p-6 w-96 shadow-xl rounded-lg bg-white">
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-6" id="bulkModalTitle">Bulk Mark Attendance</h3>
            <form id="bulkAttendanceForm">
                <input type="hidden" id="bulkDateHidden" name="date">
                <input type="hidden" id="bulkTeacherIds" name="teacher_ids">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Selected Teachers</label>
                    <p id="bulkTeacherCount" class="text-sm text-gray-900 font-medium">0 selected</p>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                    <p id="bulkDateDisplay" class="text-sm text-gray-900 font-medium"></p>
                </div>

                <div class="mb-4">
                    <label for="bulkStatusSelect" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="bulkStatusSelect" name="status" required class="form-select w-full">
                        <option value="present">Present</option>
                        <option value="absent">Absent</option>
                    </select>
                </div>

                <div class="mb-4" id="bulkSubcategoryDiv" style="display: none;">
                    <label for="bulkSubcategory" class="block text-sm font-medium text-gray-700 mb-2">Subcategory</label>
                    <select id="bulkSubcategory" name="subcategory"
                            class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none">
                        <option value="">Select subcategory (optional)</option>
                        <option value="Outstation">Outstation</option>
                        <option value="Medical Leave">Medical Leave</option>
                        <option value="Annual Leave">Annual Leave</option>
                        <option value="Emergency Leave">Emergency Leave</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <div class="mb-6">
                    <label for="bulkNotes" class="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                    <textarea id="bulkNotes" name="notes" rows="3"
                              class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none"
                              placeholder="Add notes for this bulk attendance marking..."></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeBulkModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Mark Attendance</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Checkbox management
document.getElementById('selectAllTable').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.teacher-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkButtons();
});

document.addEventListener('change', function(e) {
    if (e.target.classList.contains('teacher-checkbox')) {
        updateBulkButtons();

        // Update select all checkbox
        const allCheckboxes = document.querySelectorAll('.teacher-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.teacher-checkbox:checked');
        const selectAllCheckbox = document.getElementById('selectAllTable');

        if (checkedCheckboxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedCheckboxes.length === allCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }
});

function updateBulkButtons() {
    const checkedBoxes = document.querySelectorAll('.teacher-checkbox:checked');
    const selectedCount = document.getElementById('selectedCount');

    const count = checkedBoxes.length;
    selectedCount.textContent = `${count} selected`;
}

// Individual attendance marking
function markAttendance(teacherId, teacherName, currentStatus, currentSubcategory, currentNotes) {
    // Check if there are multiple teachers selected
    const checkedBoxes = document.querySelectorAll('.teacher-checkbox:checked');

    if (checkedBoxes.length > 1) {
        // Bulk operation
        const teacherIds = Array.from(checkedBoxes).map(cb => cb.value);
        const date = document.getElementById('date').value;

        if (!date) {
            showToast('Please select a date from the filter', 'error');
            return;
        }

        // Show bulk attendance modal
        document.getElementById('bulkDateHidden').value = date;
        document.getElementById('bulkTeacherIds').value = JSON.stringify(teacherIds);
        document.getElementById('bulkTeacherCount').textContent = `${teacherIds.length} teacher(s) selected`;
        document.getElementById('bulkDateDisplay').textContent = new Date(date).toLocaleDateString();
        document.getElementById('bulkModalTitle').textContent = 'Bulk Mark Attendance';
        document.getElementById('bulkStatusSelect').value = 'present';
        document.getElementById('bulkSubcategory').value = '';
        document.getElementById('bulkNotes').value = '';

        // Show/hide subcategory based on status
        toggleSubcategory('bulkStatusSelect', 'bulkSubcategoryDiv');

        document.getElementById('bulkAttendanceModal').classList.remove('hidden');
    } else {
        // Single operation
        document.getElementById('teacherId').value = teacherId;
        document.getElementById('teacherName').textContent = teacherName;
        document.getElementById('attendanceStatus').value = currentStatus || 'present';
        document.getElementById('attendanceSubcategory').value = currentSubcategory || '';
        document.getElementById('attendanceNotes').value = currentNotes || '';
        document.getElementById('modalTitle').textContent = currentStatus ? 'Edit Attendance' : 'Mark Attendance';

        // Show/hide subcategory based on status
        toggleSubcategory('attendanceStatus', 'subcategoryDiv');

        document.getElementById('attendanceModal').classList.remove('hidden');
    }
}

function closeModal() {
    document.getElementById('attendanceModal').classList.add('hidden');
}



function closeBulkModal() {
    document.getElementById('bulkAttendanceModal').classList.add('hidden');
}

// Toggle subcategory visibility based on status
function toggleSubcategory(statusElementId, subcategoryDivId) {
    const statusElement = document.getElementById(statusElementId);
    const subcategoryDiv = document.getElementById(subcategoryDivId);

    if (statusElement.value === 'present') {
        subcategoryDiv.style.display = 'block';
        // Update options for present status
        const subcategorySelect = subcategoryDiv.querySelector('select');
        subcategorySelect.innerHTML = `
            <option value="">Select subcategory (optional)</option>
            <option value="Outstation">Outstation</option>
        `;
    } else if (statusElement.value === 'absent') {
        subcategoryDiv.style.display = 'block';
        // Update options for absent status
        const subcategorySelect = subcategoryDiv.querySelector('select');
        subcategorySelect.innerHTML = `
            <option value="">Select subcategory (optional)</option>
            <option value="Medical Leave">Medical Leave</option>
            <option value="Annual Leave">Annual Leave</option>
            <option value="Emergency Leave">Emergency Leave</option>
            <option value="Other">Other</option>
        `;
    } else {
        subcategoryDiv.style.display = 'none';
    }
}

// Status change handlers
document.getElementById('attendanceStatus').addEventListener('change', function() {
    toggleSubcategory('attendanceStatus', 'subcategoryDiv');
});

document.getElementById('bulkStatusSelect').addEventListener('change', function() {
    toggleSubcategory('bulkStatusSelect', 'bulkSubcategoryDiv');
});

// Form submissions
document.getElementById('attendanceForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = Object.fromEntries(formData);

    try {
        console.log('Sending teacher attendance data:', data);

        const response = await fetch('{{ route("admin.attendance.mark-teacher") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify(data)
        });

        console.log('Response status:', response.status);
        const result = await response.json();
        console.log('Response data:', result);

        if (result.success) {
            showToast(result.message, 'success');
            closeModal();
            // Force reload to refresh attendance data
            setTimeout(() => {
                window.location.reload(true);
            }, 1000);
        } else {
            showToast(result.message || 'Error marking attendance', 'error');
        }
    } catch (error) {
        console.error('Teacher attendance error:', error);
        showToast('Error marking attendance: ' + error.message, 'error');
    }
});

document.getElementById('bulkAttendanceForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const teacherIds = JSON.parse(formData.get('teacher_ids'));
    const status = document.getElementById('bulkStatusSelect').value;
    const data = {
        teacher_ids: teacherIds,
        date: formData.get('date'),
        status: status,
        subcategory: formData.get('subcategory') || null,
        notes: formData.get('notes') || ''
    };

    try {
        console.log('Sending bulk teacher attendance data:', data);

        const response = await fetch('{{ route("admin.attendance.bulk-mark-teachers") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify(data)
        });

        console.log('Response status:', response.status);
        const result = await response.json();
        console.log('Response data:', result);

        if (result.success) {
            showToast(result.message, 'success');
            closeBulkModal();
            // Force reload to refresh attendance data
            setTimeout(() => {
                window.location.reload(true);
            }, 1000);
        } else {
            showToast(result.message || 'Error marking attendance', 'error');
            if (result.errors) {
                console.error('Validation errors:', result.errors);
            }
        }
    } catch (error) {
        console.error('Bulk teacher attendance error:', error);
        showToast('Error marking attendance: ' + error.message, 'error');
    }
});

// Filtering functionality
function applyFilters() {
    const search = document.getElementById('search').value;
    const date = document.getElementById('date').value;
    const status = document.getElementById('status').value;

    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (date) params.append('date', date);
    if (status) params.append('status', status);

    window.location.href = '{{ route("admin.attendance.teachers") }}?' + params.toString();
}

function clearFilters() {
    window.location.href = '{{ route("admin.attendance.teachers") }}';
}

// Event listeners for instant filtering
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    const dateInput = document.getElementById('date');
    const statusSelect = document.getElementById('status');

    let searchTimeout;

    // Search with debounce
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(applyFilters, 500);
    });

    // Other filters change instantly
    dateInput.addEventListener('change', applyFilters);
    statusSelect.addEventListener('change', applyFilters);

    // Close modals when clicking outside
    document.getElementById('attendanceModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    document.getElementById('bulkAttendanceModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeBulkModal();
        }
    });
});
</script>
@endpush
@endsection
