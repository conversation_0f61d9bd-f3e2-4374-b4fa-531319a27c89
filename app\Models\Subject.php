<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subject extends Model
{
    use HasFactory;

    protected $fillable = [
        'subject_code',
        'name',
        'description',
        'category',
        'credits',
        'is_active',
        'grade_levels',
        'prerequisites',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'grade_levels' => 'array',
        'prerequisites' => 'array',
        'credits' => 'integer',
    ];

    /**
     * Get the classes that offer this subject
     */
    public function classes()
    {
        return $this->belongsToMany(ClassModel::class, 'class_subjects', 'subject_id', 'class_id')
                    ->withPivot('is_mandatory', 'hours_per_week')
                    ->withTimestamps();
    }

    /**
     * Get the teachers assigned to this subject
     */
    public function teachers()
    {
        return $this->belongsToMany(Teacher::class, 'teacher_subjects', 'subject_id', 'teacher_id')
                    ->withPivot('class_id', 'section_id', 'is_primary')
                    ->withTimestamps();
    }

    /**
     * Get students enrolled in this subject
     */
    public function students()
    {
        return $this->belongsToMany(Student::class, 'student_subjects', 'subject_id', 'student_id')
                    ->withPivot('class_id', 'section_id', 'enrollment_date', 'end_date', 'is_active')
                    ->withTimestamps();
    }

    /**
     * Get active students for this subject
     */
    public function activeStudents()
    {
        return $this->students()->wherePivot('is_active', true);
    }

    /**
     * Get prerequisite subjects
     */
    public function prerequisiteSubjects()
    {
        if (!$this->prerequisites) {
            return collect();
        }
        
        return self::whereIn('id', $this->prerequisites)->get();
    }

    /**
     * Get subjects that have this as a prerequisite
     */
    public function dependentSubjects()
    {
        return self::whereJsonContains('prerequisites', $this->id)->get();
    }

    /**
     * Scope to filter active subjects
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by category
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to filter by grade level
     */
    public function scopeForGradeLevel($query, $gradeLevel)
    {
        return $query->whereJsonContains('grade_levels', $gradeLevel);
    }

    /**
     * Get the badge color for category
     */
    public function getCategoryBadgeColorAttribute()
    {
        return match($this->category) {
            'Core' => 'blue',
            'Elective' => 'green',
            'Extra-curricular' => 'purple',
            default => 'gray'
        };
    }

    /**
     * Get the status badge color
     */
    public function getStatusBadgeColorAttribute()
    {
        return $this->is_active ? 'green' : 'red';
    }

    /**
     * Get the status display text
     */
    public function getStatusDisplayAttribute()
    {
        return $this->is_active ? 'Active' : 'Inactive';
    }

    /**
     * Get formatted grade levels
     */
    public function getFormattedGradeLevelsAttribute()
    {
        if (!$this->grade_levels) {
            return 'All Grades';
        }
        
        return implode(', ', $this->grade_levels);
    }
}
