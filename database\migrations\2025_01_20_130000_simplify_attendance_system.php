<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we need to recreate the tables due to column constraints
        // First, backup existing data
        $studentAttendances = \DB::table('student_attendances')->get();
        $teacherAttendances = \DB::table('teacher_attendances')->get();

        // Drop and recreate student_attendances table
        Schema::dropIfExists('student_attendances');
        Schema::create('student_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->foreignId('marked_by')->constrained('users')->onDelete('cascade');
            $table->date('date');
            $table->enum('status', ['present', 'absent'])->default('present');
            $table->string('subcategory')->nullable();
            $table->text('notes')->nullable();
            $table->string('class')->nullable();
            $table->string('section')->nullable();
            $table->timestamps();

            $table->unique(['student_id', 'date']);
            $table->index(['date', 'status']);
            $table->index(['class', 'section', 'date']);
        });

        // Drop and recreate teacher_attendances table
        Schema::dropIfExists('teacher_attendances');
        Schema::create('teacher_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('teacher_id')->constrained()->onDelete('cascade');
            $table->foreignId('marked_by')->constrained('users')->onDelete('cascade');
            $table->date('date');
            $table->enum('status', ['present', 'absent'])->default('present');
            $table->string('subcategory')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['teacher_id', 'date']);
            $table->index(['date', 'status']);
        });

        // Restore data with simplified status mapping
        foreach ($studentAttendances as $attendance) {
            $status = in_array($attendance->status, ['present', 'late']) ? 'present' : 'absent';
            $subcategory = null;

            // Map old status to subcategory
            if ($attendance->status === 'sick') {
                $subcategory = 'Medical Leave';
            } elseif ($attendance->status === 'excused') {
                $subcategory = 'Family Matters';
            }

            \DB::table('student_attendances')->insert([
                'id' => $attendance->id,
                'student_id' => $attendance->student_id,
                'marked_by' => $attendance->marked_by,
                'date' => $attendance->date,
                'status' => $status,
                'subcategory' => $subcategory,
                'notes' => $attendance->notes,
                'class' => $attendance->class,
                'section' => $attendance->section,
                'created_at' => $attendance->created_at,
                'updated_at' => $attendance->updated_at,
            ]);
        }

        foreach ($teacherAttendances as $attendance) {
            $status = in_array($attendance->status, ['present', 'official_duty']) ? 'present' : 'absent';
            $subcategory = null;

            // Map old status to subcategory
            if ($attendance->status === 'official_duty') {
                $subcategory = 'Outstation';
            } elseif ($attendance->status === 'sick_leave') {
                $subcategory = 'Medical Leave';
            } elseif ($attendance->status === 'personal_leave') {
                $subcategory = 'Annual Leave';
            }

            \DB::table('teacher_attendances')->insert([
                'id' => $attendance->id,
                'teacher_id' => $attendance->teacher_id,
                'marked_by' => $attendance->marked_by,
                'date' => $attendance->date,
                'status' => $status,
                'subcategory' => $subcategory,
                'notes' => $attendance->notes,
                'created_at' => $attendance->created_at,
                'updated_at' => $attendance->updated_at,
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore student_attendances table
        Schema::table('student_attendances', function (Blueprint $table) {
            $table->dropColumn(['status', 'subcategory']);
        });

        Schema::table('student_attendances', function (Blueprint $table) {
            $table->enum('status', ['present', 'absent', 'late', 'excused', 'sick'])->default('present');
            $table->time('check_in_time')->nullable();
            $table->time('check_out_time')->nullable();
        });

        // Restore teacher_attendances table
        Schema::table('teacher_attendances', function (Blueprint $table) {
            $table->dropColumn(['status', 'subcategory']);
        });

        Schema::table('teacher_attendances', function (Blueprint $table) {
            $table->enum('status', ['present', 'absent', 'late', 'sick_leave', 'personal_leave', 'official_duty'])->default('present');
            $table->time('check_in_time')->nullable();
            $table->time('check_out_time')->nullable();
            $table->decimal('hours_worked', 5, 2)->nullable();
        });
    }
};
