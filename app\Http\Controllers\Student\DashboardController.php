<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Payment;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $student = auth()->user()->student;

        if (!$student) {
            return redirect()->route('login')->with('error', 'Student profile not found.');
        }

        $stats = [
            'pending_invoices' => Invoice::where('student_id', $student->id)->where('status', 'sent')->count(),
            'paid_invoices' => Invoice::where('student_id', $student->id)->where('status', 'paid')->count(),
            'overdue_invoices' => Invoice::where('student_id', $student->id)->where('status', 'overdue')->count(),
            'total_amount_due' => Invoice::where('student_id', $student->id)->where('status', 'sent')->sum('total_amount'),
        ];

        $recent_invoices = Invoice::where('student_id', $student->id)
            ->latest()
            ->take(5)
            ->get();

        // Get attendance statistics for current month
        $attendanceStats = \App\Models\StudentAttendance::getStudentStats(
            $student->id,
            now()->startOfMonth()->format('Y-m-d'),
            now()->format('Y-m-d')
        );

        // Get recent attendance records
        $recentAttendance = $student->attendances()
            ->with('markedBy')
            ->latest()
            ->take(10)
            ->get();

        // Load academic relationships for subjects and teachers
        $student->load([
            'currentEnrollment.class',
            'currentEnrollment.section',
            'activeSubjects.teachers' => function($query) use ($student) {
                $query->whereHas('classSubjects', function($q) use ($student) {
                    $q->where('class_id', $student->currentEnrollment->class_id ?? null)
                      ->where('section_id', $student->currentEnrollment->section_id ?? null);
                });
            }
        ]);

        return view('student.dashboard', compact('stats', 'recent_invoices', 'student', 'attendanceStats', 'recentAttendance'));
    }
}
