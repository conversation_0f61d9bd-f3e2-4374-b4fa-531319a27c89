<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ExamType;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ExamTypeController extends Controller
{
    public function index()
    {
        $examTypes = ExamType::orderBy('name')->paginate(15);
        return view('admin.exam-types.index', compact('examTypes'));
    }

    public function create()
    {
        return view('admin.exam-types.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:exam_types',
            'description' => 'nullable|string',
            'weight_percentage' => 'required|numeric|min:0|max:100',
        ]);

        try {
            DB::beginTransaction();

            $examType = ExamType::create([
                'name' => $request->name,
                'description' => $request->description,
                'weight_percentage' => $request->weight_percentage,
                'is_active' => true,
            ]);

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'created',
                'description' => "Created exam type: {$examType->name}",
                'model_type' => ExamType::class,
                'model_id' => $examType->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.exam-types.index')
                            ->with('success', 'Exam type created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to create exam type: ' . $e->getMessage());
        }
    }

    public function show(ExamType $examType)
    {
        $examType->load('exams');
        return view('admin.exam-types.show', compact('examType'));
    }

    public function edit(ExamType $examType)
    {
        return view('admin.exam-types.edit', compact('examType'));
    }

    public function update(Request $request, ExamType $examType)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:exam_types,name,' . $examType->id,
            'description' => 'nullable|string',
            'weight_percentage' => 'required|numeric|min:0|max:100',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $examType->update([
                'name' => $request->name,
                'description' => $request->description,
                'weight_percentage' => $request->weight_percentage,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'updated',
                'description' => "Updated exam type: {$examType->name}",
                'model_type' => ExamType::class,
                'model_id' => $examType->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.exam-types.index')
                            ->with('success', 'Exam type updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to update exam type: ' . $e->getMessage());
        }
    }

    public function destroy(ExamType $examType)
    {
        if ($examType->exams()->count() > 0) {
            return redirect()->route('admin.exam-types.index')
                            ->with('error', 'Cannot delete exam type that has associated exams.');
        }

        try {
            DB::beginTransaction();

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'deleted',
                'description' => "Deleted exam type: {$examType->name}",
                'model_type' => ExamType::class,
                'model_id' => $examType->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            $examType->delete();

            DB::commit();

            return redirect()->route('admin.exam-types.index')
                            ->with('success', 'Exam type deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('admin.exam-types.index')
                            ->with('error', 'Failed to delete exam type: ' . $e->getMessage());
        }
    }
}
