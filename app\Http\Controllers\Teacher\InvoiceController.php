<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class InvoiceController extends Controller
{
    /**
     * Display a listing of invoices created by the teacher
     */
    public function index(Request $request)
    {
        $query = Invoice::with(['student.user', 'creator', 'payments'])
                       ->where('created_by', auth()->id());

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Search by invoice number or student name
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%")
                  ->orWhereHas('student.user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $invoices = $query->latest()->paginate(15);

        $stats = [
            'total' => Invoice::where('created_by', auth()->id())->count(),
            'draft' => Invoice::where('created_by', auth()->id())->where('status', 'draft')->count(),
            'sent' => Invoice::where('created_by', auth()->id())->where('status', 'sent')->count(),
            'paid' => Invoice::where('created_by', auth()->id())->where('status', 'paid')->count(),
            'overdue' => Invoice::where('created_by', auth()->id())->where('status', 'overdue')->count(),
            'cancelled' => Invoice::where('created_by', auth()->id())->where('status', 'cancelled')->count(),
        ];

        return view('teacher.invoices.index', compact('invoices', 'stats'));
    }

    /**
     * Show the form for creating a new invoice
     */
    public function create()
    {
        // Teachers can only create invoices for students in their classes
        // TODO: Implement class-student relationship to filter students
        $students = Student::with('user')->get();
        
        $invoiceTypes = [
            'tuition' => 'Tuition Fee',
            'exam' => 'Exam Fee',
            'transport' => 'Transport Fee',
            'library' => 'Library Fee',
            'sports' => 'Sports Fee',
            'other' => 'Other'
        ];

        return view('teacher.invoices.create', compact('students', 'invoiceTypes'));
    }

    /**
     * Store a newly created invoice
     */
    public function store(Request $request)
    {
        $request->validate([
            'student_id' => 'required|exists:students,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'due_date' => 'required|date|after:today',
            'type' => 'required|in:tuition,exam,transport,library,sports,other',
            'line_items' => 'nullable|array',
            'line_items.*.description' => 'required_with:line_items|string',
            'line_items.*.amount' => 'required_with:line_items|numeric|min:0',
        ]);

        // TODO: Check if teacher has permission to create invoice for this student
        // For now, allow all students

        // Calculate line items total
        $lineItemsTotal = 0;
        if ($request->line_items) {
            foreach ($request->line_items as $item) {
                $lineItemsTotal += floatval($item['amount'] ?? 0);
            }
        }

        $totalAmount = $request->amount + $lineItemsTotal - ($request->discount ?? 0);

        $invoice = Invoice::create([
            'invoice_number' => $this->generateInvoiceNumber(),
            'student_id' => $request->student_id,
            'created_by' => auth()->id(),
            'title' => $request->title,
            'description' => $request->description,
            'amount' => $request->amount,
            'discount' => $request->discount ?? 0,
            'total_amount' => $totalAmount,
            'due_date' => $request->due_date,
            'type' => $request->type,
            'line_items' => $request->line_items,
            'status' => 'draft',
        ]);

        return redirect()->route('teacher.invoices.show', $invoice)
                        ->with('success', 'Invoice created successfully.');
    }

    /**
     * Display the specified invoice
     */
    public function show(Invoice $invoice)
    {
        // Check if this invoice was created by the authenticated teacher
        if ($invoice->created_by !== auth()->id()) {
            abort(403, 'Unauthorized access to this invoice.');
        }

        $invoice->load(['student.user', 'creator', 'payments']);
        return view('teacher.invoices.show', compact('invoice'));
    }

    /**
     * Show the form for editing the specified invoice
     */
    public function edit(Invoice $invoice)
    {
        // Check if this invoice was created by the authenticated teacher
        if ($invoice->created_by !== auth()->id()) {
            abort(403, 'Unauthorized access to this invoice.');
        }

        // Only allow editing draft invoices
        if ($invoice->status !== 'draft') {
            return redirect()->route('teacher.invoices.show', $invoice)
                            ->with('error', 'Only draft invoices can be edited.');
        }

        $students = Student::with('user')->get();
        $invoiceTypes = [
            'tuition' => 'Tuition Fee',
            'exam' => 'Exam Fee',
            'transport' => 'Transport Fee',
            'library' => 'Library Fee',
            'sports' => 'Sports Fee',
            'other' => 'Other'
        ];

        return view('teacher.invoices.edit', compact('invoice', 'students', 'invoiceTypes'));
    }

    /**
     * Update the specified invoice
     */
    public function update(Request $request, Invoice $invoice)
    {
        // Check if this invoice was created by the authenticated teacher
        if ($invoice->created_by !== auth()->id()) {
            abort(403, 'Unauthorized access to this invoice.');
        }

        // Only allow updating draft invoices
        if ($invoice->status !== 'draft') {
            return redirect()->route('teacher.invoices.show', $invoice)
                            ->with('error', 'Only draft invoices can be updated.');
        }

        $request->validate([
            'student_id' => 'required|exists:students,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'due_date' => 'required|date|after:today',
            'type' => 'required|in:tuition,exam,transport,library,sports,other',
            'line_items' => 'nullable|array',
            'line_items.*.description' => 'required_with:line_items|string',
            'line_items.*.amount' => 'required_with:line_items|numeric|min:0',
        ]);

        $totalAmount = $request->amount - ($request->discount ?? 0);

        $invoice->update([
            'student_id' => $request->student_id,
            'title' => $request->title,
            'description' => $request->description,
            'amount' => $request->amount,
            'discount' => $request->discount ?? 0,
            'total_amount' => $totalAmount,
            'due_date' => $request->due_date,
            'type' => $request->type,
            'line_items' => $request->line_items,
        ]);

        return redirect()->route('teacher.invoices.show', $invoice)
                        ->with('success', 'Invoice updated successfully.');
    }

    /**
     * Remove the specified invoice
     */
    public function destroy(Invoice $invoice)
    {
        // Check if this invoice was created by the authenticated teacher
        if ($invoice->created_by !== auth()->id()) {
            abort(403, 'Unauthorized access to this invoice.');
        }

        // Only allow deleting draft invoices
        if ($invoice->status !== 'draft') {
            return redirect()->route('teacher.invoices.index')
                            ->with('error', 'Only draft invoices can be deleted.');
        }

        $invoice->delete();

        return redirect()->route('teacher.invoices.index')
                        ->with('success', 'Invoice deleted successfully.');
    }

    /**
     * Send invoice to guardian
     */
    public function send(Invoice $invoice)
    {
        // Check if this invoice was created by the authenticated teacher
        if ($invoice->created_by !== auth()->id()) {
            abort(403, 'Unauthorized access to this invoice.');
        }

        if ($invoice->status !== 'draft') {
            return redirect()->route('teacher.invoices.show', $invoice)
                            ->with('error', 'Only draft invoices can be sent.');
        }

        $invoice->update(['status' => 'sent']);

        // TODO: Send email notification to guardian

        return redirect()->route('teacher.invoices.show', $invoice)
                        ->with('success', 'Invoice sent successfully.');
    }

    /**
     * Generate unique invoice number
     */
    private function generateInvoiceNumber()
    {
        $prefix = 'INV-' . date('Y') . '-';
        $lastInvoice = Invoice::where('invoice_number', 'like', $prefix . '%')
                             ->orderBy('invoice_number', 'desc')
                             ->first();

        if ($lastInvoice) {
            $lastNumber = (int) substr($lastInvoice->invoice_number, strlen($prefix));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }
}
