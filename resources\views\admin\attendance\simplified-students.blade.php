@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Student Attendance"
        description="Mark and manage student attendance records">
        
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.attendance.reports') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Reports
            </a>
        </div>
    </x-page-header>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Present</dt>
                            <dd class="stat-card-value">{{ $attendanceStats['present'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-red-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Absent</dt>
                            <dd class="stat-card-value">{{ $attendanceStats['absent'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Students</dt>
                            <dd class="stat-card-value">{{ $attendanceStats['total_students'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Attendance Rate</dt>
                            <dd class="stat-card-value">{{ number_format($attendanceStats['attendance_percentage'], 1) }}%</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6"
         x-data="{
             searchQuery: '{{ $search }}',
             date: '{{ $date }}',
             class: '{{ $class }}',
             section: '{{ $section }}',
             status: '{{ $status }}',
             showAdvanced: {{ ($date || $class || $section || $status) ? 'true' : 'false' }}
         }">

        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
            <!-- Search Input -->
            <div class="flex-1">
                <input
                    type="text"
                    id="search"
                    value="{{ $search }}"
                    placeholder="Search students by name, student ID, class, section..."
                    class="form-input w-full"
                >
            </div>

            <!-- Filter Toggle and Clear Buttons -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                </button>

                <button
                    onclick="clearFilters()"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6m0 0l6-6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input type="date" id="date" value="{{ $date }}"
                       class="form-input w-full">
            </div>

            <div>
                <label for="class" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
                <select id="class" class="form-select w-full">
                    <option value="">All Classes</option>
                    @foreach($classes as $classOption)
                        <option value="{{ $classOption }}" {{ $class === $classOption ? 'selected' : '' }}>
                            {{ $classOption }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div>
                <label for="section" class="block text-sm font-medium text-gray-700 mb-1">Section</label>
                <select id="section" class="form-select w-full">
                    <option value="">All Sections</option>
                    @foreach($sections as $sectionOption)
                        <option value="{{ $sectionOption }}" {{ $section === $sectionOption ? 'selected' : '' }}>
                            {{ $sectionOption }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="status" class="form-select w-full">
                    <option value="">All Status</option>
                    <option value="present" {{ $status === 'present' ? 'selected' : '' }}>Present</option>
                    <option value="absent" {{ $status === 'absent' ? 'selected' : '' }}>Absent</option>
                    <option value="not_marked" {{ $status === 'not_marked' ? 'selected' : '' }}>Not Marked</option>
                </select>
            </div>
        </div>

        <!-- Bulk Selection Info -->
        <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-600">
                <span class="italic">Select students and use individual action buttons to mark attendance</span>
            </div>
            <div class="text-sm text-gray-500">
                <span id="selectedCount">0 selected</span>
            </div>
        </div>
    </div>

    <!-- Students Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Students - {{ \Carbon\Carbon::parse($date)->format('M d, Y') }}</h3>
        </div>

        @if($students->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="selectAllTable" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($students as $student)
                            @php
                                $attendance = $student->attendances->first();
                            @endphp
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="student-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                           value="{{ $student->id }}">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                                <span class="text-sm font-medium text-gray-600">
                                                    {{ substr($student->user->name, 0, 2) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $student->user->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $student->student_id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $student->class_section }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($attendance)
                                        <span class="badge badge-{{ $attendance->status_badge_color }}">
                                            {{ $attendance->full_status_display }}
                                        </span>
                                    @else
                                        <span class="badge badge-gray">Not Marked</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">
                                    {{ $attendance->notes ?? '-' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                    <div class="flex items-center justify-center space-x-2">
                                        @if($attendance)
                                            <button onclick="markAttendance({{ $student->id }}, '{{ $student->user->name }}', '{{ $attendance->status }}', '{{ $attendance->subcategory }}', '{{ $attendance->notes }}')"
                                                    class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition-colors duration-200 cursor-pointer"
                                                    title="Edit Attendance">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                Edit
                                            </button>
                                        @else
                                            <button onclick="markAttendance({{ $student->id }}, '{{ $student->user->name }}', '', '', '')"
                                                    class="inline-flex items-center px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition-colors duration-200 cursor-pointer"
                                                    title="Mark Attendance">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                </svg>
                                                Mark
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="px-6 py-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No students found</h3>
                <p class="mt-1 text-sm text-gray-500">No students match your current filters.</p>
            </div>
        @endif
    </div>
</div>

<!-- Individual Attendance Modal -->
<div id="attendanceModal" class="fixed inset-0 overflow-y-auto h-full w-full hidden z-50" style="background-color: rgba(17, 24, 39, 0.6);">
    <div class="relative top-20 mx-auto p-6 w-96 shadow-xl rounded-lg bg-white">
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-6" id="modalTitle">Mark Attendance</h3>
            <form id="attendanceForm">
                <input type="hidden" id="studentId" name="student_id">
                <input type="hidden" name="date" value="{{ $date }}">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Student</label>
                    <p id="studentName" class="text-sm text-gray-900 font-medium bg-gray-50 p-3 rounded"></p>
                </div>

                <div class="mb-4">
                    <label for="attendanceStatus" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="attendanceStatus" name="status" required class="form-select w-full">
                        <option value="present">Present</option>
                        <option value="absent">Absent</option>
                    </select>
                </div>

                <div class="mb-4" id="subcategoryDiv" style="display: none;">
                    <label for="attendanceSubcategory" class="block text-sm font-medium text-gray-700 mb-2">Subcategory</label>
                    <select id="attendanceSubcategory" name="subcategory"
                            class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none">
                        <option value="">Select subcategory (optional)</option>
                        <option value="Medical Leave">Medical Leave</option>
                        <option value="Family Matters">Family Matters</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <div class="mb-6">
                    <label for="attendanceNotes" class="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                    <textarea id="attendanceNotes" name="notes" rows="3"
                              class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none"
                              placeholder="Add any additional notes..."></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Save Attendance</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Attendance Modal -->
<div id="bulkAttendanceModal" class="fixed inset-0 overflow-y-auto h-full w-full hidden z-50" style="background-color: rgba(17, 24, 39, 0.6);">
    <div class="relative top-20 mx-auto p-6 w-96 shadow-xl rounded-lg bg-white">
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-6" id="bulkModalTitle">Bulk Mark Attendance</h3>
            <form id="bulkAttendanceForm">
                <input type="hidden" id="bulkDateHidden" name="date">
                <input type="hidden" id="bulkStudentIds" name="student_ids">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Selected Students</label>
                    <p id="bulkStudentCount" class="text-sm text-gray-900 font-medium">0 selected</p>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                    <p id="bulkDateDisplay" class="text-sm text-gray-900 font-medium"></p>
                </div>

                <div class="mb-4">
                    <label for="bulkStatusSelect" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="bulkStatusSelect" name="status" required class="form-select w-full">
                        <option value="present">Present</option>
                        <option value="absent">Absent</option>
                    </select>
                </div>

                <div class="mb-4" id="bulkSubcategoryDiv" style="display: none;">
                    <label for="bulkSubcategory" class="block text-sm font-medium text-gray-700 mb-2">Subcategory</label>
                    <select id="bulkSubcategory" name="subcategory"
                            class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none">
                        <option value="">Select subcategory (optional)</option>
                        <option value="Medical Leave">Medical Leave</option>
                        <option value="Family Matters">Family Matters</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                <div class="mb-6">
                    <label for="bulkNotes" class="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                    <textarea id="bulkNotes" name="notes" rows="3"
                              class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none"
                              placeholder="Add notes for this bulk attendance marking..."></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeBulkModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Mark Attendance</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Checkbox management
document.getElementById('selectAllTable').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkButtons();
});

document.addEventListener('change', function(e) {
    if (e.target.classList.contains('student-checkbox')) {
        updateBulkButtons();

        // Update select all checkbox
        const allCheckboxes = document.querySelectorAll('.student-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
        const selectAllCheckbox = document.getElementById('selectAllTable');

        if (checkedCheckboxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedCheckboxes.length === allCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }
});

function updateBulkButtons() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    const selectedCount = document.getElementById('selectedCount');

    const count = checkedBoxes.length;
    selectedCount.textContent = `${count} selected`;
}

// Individual attendance marking
function markAttendance(studentId, studentName, currentStatus, currentSubcategory, currentNotes) {
    // Check if there are multiple students selected
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');

    if (checkedBoxes.length > 1) {
        // Bulk operation
        const studentIds = Array.from(checkedBoxes).map(cb => cb.value);
        const date = document.getElementById('date').value;

        if (!date) {
            showToast('Please select a date from the filter', 'error');
            return;
        }

        // Show bulk attendance modal
        document.getElementById('bulkDateHidden').value = date;
        document.getElementById('bulkStudentIds').value = JSON.stringify(studentIds);
        document.getElementById('bulkStudentCount').textContent = `${studentIds.length} student(s) selected`;
        document.getElementById('bulkDateDisplay').textContent = new Date(date).toLocaleDateString();
        document.getElementById('bulkModalTitle').textContent = 'Bulk Mark Attendance';
        document.getElementById('bulkStatusSelect').value = 'present';
        document.getElementById('bulkSubcategory').value = '';
        document.getElementById('bulkNotes').value = '';

        // Show/hide subcategory based on status
        toggleSubcategory('bulkStatusSelect', 'bulkSubcategoryDiv');

        document.getElementById('bulkAttendanceModal').classList.remove('hidden');
    } else {
        // Single operation
        document.getElementById('studentId').value = studentId;
        document.getElementById('studentName').textContent = studentName;
        document.getElementById('attendanceStatus').value = currentStatus || 'present';
        document.getElementById('attendanceSubcategory').value = currentSubcategory || '';
        document.getElementById('attendanceNotes').value = currentNotes || '';
        document.getElementById('modalTitle').textContent = currentStatus ? 'Edit Attendance' : 'Mark Attendance';

        // Show/hide subcategory based on status
        toggleSubcategory('attendanceStatus', 'subcategoryDiv');

        document.getElementById('attendanceModal').classList.remove('hidden');
    }
}

function closeModal() {
    document.getElementById('attendanceModal').classList.add('hidden');
}



function closeBulkModal() {
    document.getElementById('bulkAttendanceModal').classList.add('hidden');
}

// Toggle subcategory visibility based on status
function toggleSubcategory(statusElementId, subcategoryDivId) {
    const statusElement = document.getElementById(statusElementId);
    const subcategoryDiv = document.getElementById(subcategoryDivId);
    const subcategorySelect = subcategoryDiv.querySelector('select');

    if (statusElement.value === 'absent') {
        subcategoryDiv.style.display = 'block';
    } else {
        subcategoryDiv.style.display = 'none';
        // Clear subcategory value when status is not absent (e.g., present)
        if (subcategorySelect) {
            subcategorySelect.value = '';
        }
    }
}

// Status change handlers
document.getElementById('attendanceStatus').addEventListener('change', function() {
    toggleSubcategory('attendanceStatus', 'subcategoryDiv');
});

document.getElementById('bulkStatusSelect').addEventListener('change', function() {
    toggleSubcategory('bulkStatusSelect', 'bulkSubcategoryDiv');
});

// Form submissions
document.getElementById('attendanceForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = Object.fromEntries(formData);

    try {
        console.log('Sending attendance data:', data);

        const response = await fetch('{{ route("admin.attendance.mark-student") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify(data)
        });

        console.log('Response status:', response.status);
        const result = await response.json();
        console.log('Response data:', result);

        if (result.success) {
            showToast(result.message, 'success');
            closeModal();
            // Force reload to refresh attendance data
            setTimeout(() => {
                window.location.reload(true);
            }, 1000);
        } else {
            showToast(result.message || 'Error marking attendance', 'error');
        }
    } catch (error) {
        console.error('Attendance error:', error);
        showToast('Error marking attendance: ' + error.message, 'error');
    }
});

document.getElementById('bulkAttendanceForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const studentIds = JSON.parse(formData.get('student_ids'));
    const status = document.getElementById('bulkStatusSelect').value;
    const data = {
        student_ids: studentIds,
        date: formData.get('date'),
        status: status,
        subcategory: formData.get('subcategory') || null,
        notes: formData.get('notes') || ''
    };

    try {
        console.log('Sending bulk attendance data:', data);

        const response = await fetch('{{ route("admin.attendance.bulk-mark-students") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify(data)
        });

        console.log('Response status:', response.status);
        const result = await response.json();
        console.log('Response data:', result);

        if (result.success) {
            showToast(result.message, 'success');
            closeBulkModal();
            // Force reload to refresh attendance data
            setTimeout(() => {
                window.location.reload(true);
            }, 1000);
        } else {
            showToast(result.message || 'Error marking attendance', 'error');
            if (result.errors) {
                console.error('Validation errors:', result.errors);
            }
        }
    } catch (error) {
        console.error('Bulk attendance error:', error);
        showToast('Error marking attendance: ' + error.message, 'error');
    }
});

// Filtering functionality
function applyFilters() {
    const search = document.getElementById('search').value;
    const date = document.getElementById('date').value;
    const classValue = document.getElementById('class').value;
    const section = document.getElementById('section').value;
    const status = document.getElementById('status').value;

    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (date) params.append('date', date);
    if (classValue) params.append('class', classValue);
    if (section) params.append('section', section);
    if (status) params.append('status', status);

    window.location.href = '{{ route("admin.attendance.students") }}?' + params.toString();
}

function clearFilters() {
    window.location.href = '{{ route("admin.attendance.students") }}';
}

// Event listeners for instant filtering
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    const dateInput = document.getElementById('date');
    const classSelect = document.getElementById('class');
    const sectionSelect = document.getElementById('section');
    const statusSelect = document.getElementById('status');

    let searchTimeout;

    // Search with debounce
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(applyFilters, 500);
    });

    // Other filters change instantly
    dateInput.addEventListener('change', applyFilters);
    classSelect.addEventListener('change', applyFilters);
    sectionSelect.addEventListener('change', applyFilters);
    statusSelect.addEventListener('change', applyFilters);

    // Close modals when clicking outside
    document.getElementById('attendanceModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    document.getElementById('bulkAttendanceModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeBulkModal();
        }
    });
});
</script>
@endpush
@endsection
