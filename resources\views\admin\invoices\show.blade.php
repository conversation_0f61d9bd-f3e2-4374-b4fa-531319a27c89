@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Invoice Details"
        description="View and manage invoice information"
        :back-route="route('admin.invoices.index')"
        back-label="Back to Invoices">
        
        <div class="flex items-center space-x-3">
            @if($invoice->status === 'draft')
                <a href="{{ route('admin.invoices.edit', $invoice) }}" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                    </svg>
                    Edit Invoice
                </a>
                
                <form id="send-invoice-form" method="POST" action="{{ route('admin.invoices.send', $invoice) }}" class="inline">
                    @csrf
                    <button type="button" class="btn-primary" onclick="sendInvoice('{{ $invoice->invoice_number }}')">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                        Send Invoice
                    </button>
                </form>
            @endif

            @if(in_array($invoice->status, ['draft', 'sent']))
                <button type="button"
                        onclick="cancelInvoice('{{ $invoice->id }}', '{{ $invoice->invoice_number }}')"
                        class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Cancel
                </button>
            @endif
        </div>
    </x-page-header>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Invoice Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Invoice Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-start mb-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">{{ $invoice->title }}</h3>
                        <p class="text-sm text-gray-500">Invoice #{{ $invoice->invoice_number }}</p>
                    </div>
                    <div class="text-right">
                        @php
                            $statusColors = [
                                'draft' => 'bg-gray-100 text-gray-800',
                                'sent' => 'bg-yellow-100 text-yellow-800',
                                'paid' => 'bg-green-100 text-green-800',
                                'overdue' => 'bg-red-100 text-red-800',
                                'cancelled' => 'bg-purple-100 text-purple-800',
                            ];
                        @endphp
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full {{ $statusColors[$invoice->status] ?? 'bg-gray-100 text-gray-800' }}">
                            {{ ucfirst($invoice->status) }}
                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Student Information</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <p><strong>Name:</strong> {{ $invoice->student->user->name ?? 'N/A' }}</p>
                            <p><strong>Student ID:</strong> {{ $invoice->student->student_id ?? 'N/A' }}</p>
                            <p><strong>Class:</strong> {{ $invoice->student->class ?? 'N/A' }}</p>
                            <p><strong>Email:</strong> {{ $invoice->student->user->email ?? 'N/A' }}</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Invoice Details</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <p><strong>Type:</strong> {{ ucfirst($invoice->type) }}</p>
                            <p><strong>Created by:</strong> {{ $invoice->creator->name ?? 'N/A' }}</p>
                            <p><strong>Created:</strong> {{ $invoice->created_at->format('M d, Y H:i') }}</p>
                            <p><strong>Due Date:</strong> 
                                <span class="{{ $invoice->due_date->isPast() && $invoice->status !== 'paid' ? 'text-red-600 font-medium' : '' }}">
                                    {{ $invoice->due_date->format('M d, Y') }}
                                    @if($invoice->due_date->isPast() && $invoice->status !== 'paid')
                                        (Overdue)
                                    @endif
                                </span>
                            </p>
                        </div>
                    </div>
                </div>

                @if($invoice->description)
                    <div class="mt-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Description</h4>
                        <p class="text-sm text-gray-600">{{ $invoice->description }}</p>
                    </div>
                @endif
            </div>

            <!-- Line Items -->
            @if($invoice->line_items && count($invoice->line_items) > 0)
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Line Items</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($invoice->line_items as $item)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item['description'] }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">RM {{ number_format($item['amount'], 2) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif

            <!-- Payment History -->
            @if($invoice->payments->count() > 0)
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Payment History</h3>
                    <div class="space-y-4">
                        @foreach($invoice->payments as $payment)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $payment->payment_reference }}</p>
                                        <p class="text-sm text-gray-500">Paid by: {{ $payment->payer->name ?? 'N/A' }}</p>
                                        <p class="text-sm text-gray-500">
                                            @if($payment->paid_at)
                                                Paid on: {{ $payment->paid_at->format('M d, Y H:i') }}
                                            @else
                                                Created: {{ $payment->created_at->format('M d, Y H:i') }}
                                            @endif
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-medium text-gray-900">RM {{ number_format($payment->amount, 2) }}</p>
                                        @php
                                            $paymentStatusColors = [
                                                'pending' => 'bg-yellow-100 text-yellow-800',
                                                'paid' => 'bg-green-100 text-green-800',
                                                'failed' => 'bg-red-100 text-red-800',
                                                'refunded' => 'bg-purple-100 text-purple-800',
                                            ];
                                        @endphp
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $paymentStatusColors[$payment->status] ?? 'bg-gray-100 text-gray-800' }}">
                                            {{ ucfirst($payment->status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Amount Summary -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Amount Summary</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Subtotal:</span>
                        <span class="text-sm font-medium text-gray-900">RM {{ number_format($invoice->amount, 2) }}</span>
                    </div>
                    @if($invoice->discount > 0)
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Discount:</span>
                            <span class="text-sm font-medium text-red-600">-RM {{ number_format($invoice->discount, 2) }}</span>
                        </div>
                    @endif
                    <div class="border-t border-gray-200 pt-3">
                        <div class="flex justify-between">
                            <span class="text-base font-medium text-gray-900">Total:</span>
                            <span class="text-base font-bold text-blue-600">RM {{ number_format($invoice->total_amount, 2) }}</span>
                        </div>
                    </div>
                    
                    @php
                        $totalPaid = $invoice->payments->where('status', 'paid')->sum('amount');
                        $remainingAmount = $invoice->total_amount - $totalPaid;
                    @endphp
                    
                    @if($totalPaid > 0)
                        <div class="border-t border-gray-200 pt-3 space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Paid:</span>
                                <span class="text-sm font-medium text-green-600">RM {{ number_format($totalPaid, 2) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900">Remaining:</span>
                                <span class="text-sm font-bold {{ $remainingAmount > 0 ? 'text-red-600' : 'text-green-600' }}">
                                    RM {{ number_format($remainingAmount, 2) }}
                                </span>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div class="flex flex-col space-y-3">
                    <a href="{{ route('admin.payments.index', ['search' => $invoice->invoice_number]) }}"
                       class="btn-secondary text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        View Payments
                    </a>

                    @if($invoice->status === 'sent' || $invoice->status === 'overdue')
                        <button class="btn-secondary" onclick="sendReminder()">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2z"></path>
                            </svg>
                            Send Reminder
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Store the base route URL for JavaScript use
const cancelRouteBase = '{{ route('admin.invoices.index') }}';

async function cancelInvoice(invoiceId, invoiceNumber) {
    const confirmed = await confirmAction('Cancel Invoice', `Are you sure you want to cancel invoice ${invoiceNumber}? This action cannot be undone.`);

    if (confirmed) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `${cancelRouteBase}/${invoiceId}/cancel`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}

async function sendInvoice(invoiceNumber) {
    const confirmed = await confirmAction('Send Invoice', `Are you sure you want to send invoice ${invoiceNumber}?`);

    if (confirmed) {
        document.getElementById('send-invoice-form').submit();
    }
}

async function sendReminder() {
    await confirmModal({
        title: 'Feature Coming Soon',
        message: 'Send reminder functionality will be available in a future update.',
        confirmText: 'OK',
        cancelText: '',
        type: 'info'
    });
}
</script>
@endpush
