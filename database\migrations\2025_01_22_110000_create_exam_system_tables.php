<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Exam Types (Midterm, Final, Quiz, etc.)
        Schema::create('exam_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('weight_percentage', 5, 2)->default(100.00); // Weight in final grade
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Exams
        Schema::create('exams', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('exam_type_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_term_id')->constrained()->onDelete('cascade');
            $table->date('start_date');
            $table->date('end_date');
            $table->enum('status', ['draft', 'scheduled', 'ongoing', 'completed', 'cancelled'])->default('draft');
            $table->json('instructions')->nullable(); // Exam instructions
            $table->boolean('is_published')->default(false);
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            $table->index(['academic_term_id', 'status']);
            $table->index(['start_date', 'end_date']);
        });

        // Exam Subjects (Which subjects are included in an exam)
        Schema::create('exam_subjects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exam_id')->constrained()->onDelete('cascade');
            $table->foreignId('subject_id')->constrained()->onDelete('cascade');
            $table->date('exam_date');
            $table->time('start_time');
            $table->time('end_time');
            $table->integer('duration_minutes');
            $table->decimal('max_marks', 8, 2);
            $table->decimal('pass_marks', 8, 2);
            $table->string('exam_hall')->nullable();
            $table->json('instructions')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->unique(['exam_id', 'subject_id']);
            $table->index(['exam_date', 'start_time']);
        });

        // Exam Enrollments (Which students are enrolled for which exams)
        Schema::create('exam_enrollments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exam_id')->constrained()->onDelete('cascade');
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->enum('status', ['enrolled', 'absent', 'completed', 'disqualified'])->default('enrolled');
            $table->text('notes')->nullable();
            $table->timestamp('enrolled_at')->useCurrent();
            $table->timestamps();

            $table->unique(['exam_id', 'student_id']);
            $table->index(['exam_id', 'status']);
        });

        // Exam Results
        Schema::create('exam_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exam_subject_id')->constrained()->onDelete('cascade');
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->decimal('marks_obtained', 8, 2);
            $table->decimal('percentage', 5, 2)->nullable(); // Will be calculated in the model
            $table->string('grade', 5)->nullable();
            $table->decimal('gpa_points', 3, 2)->nullable();
            $table->enum('status', ['present', 'absent', 'disqualified'])->default('present');
            $table->text('remarks')->nullable();
            $table->foreignId('entered_by')->constrained('users')->onDelete('cascade');
            $table->timestamp('entered_at')->useCurrent();
            $table->timestamps();

            $table->unique(['exam_subject_id', 'student_id']);
            $table->index(['student_id', 'status']);
        });

        // Exam Invigilators
        Schema::create('exam_invigilators', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exam_subject_id')->constrained()->onDelete('cascade');
            $table->foreignId('teacher_id')->constrained('users')->onDelete('cascade');
            $table->enum('role', ['chief', 'assistant', 'observer'])->default('assistant');
            $table->string('hall_assignment')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['exam_subject_id', 'teacher_id']);
        });

        // Exam Halls
        Schema::create('exam_halls', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('location')->nullable();
            $table->integer('capacity');
            $table->json('facilities')->nullable(); // AC, Projector, etc.
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_halls');
        Schema::dropIfExists('exam_invigilators');
        Schema::dropIfExists('exam_results');
        Schema::dropIfExists('exam_enrollments');
        Schema::dropIfExists('exam_subjects');
        Schema::dropIfExists('exams');
        Schema::dropIfExists('exam_types');
    }
};
