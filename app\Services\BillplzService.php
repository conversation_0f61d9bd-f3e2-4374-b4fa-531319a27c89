<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BillplzService
{
    private $apiKey;
    private $collectionId;
    private $xSignatureKey;
    private $apiUrl;
    private $sandbox;

    public function __construct()
    {
        $this->apiKey = config('services.billplz.api_key');
        $this->collectionId = config('services.billplz.collection_id');
        $this->xSignatureKey = config('services.billplz.x_signature_key');
        $this->apiUrl = config('services.billplz.api_url');
        $this->sandbox = config('services.billplz.sandbox');
    }

    /**
     * Create a bill in Billplz
     */
    public function createBill($data)
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, '')
                ->post($this->apiUrl . '/v3/bills', [
                    'collection_id' => $this->collectionId,
                    'email' => $data['email'],
                    'mobile' => $data['mobile'] ?? null,
                    'name' => $data['name'],
                    'amount' => $data['amount'] * 100, // Convert to cents
                    'description' => $data['description'],
                    'callback_url' => $data['callback_url'],
                    'redirect_url' => $data['redirect_url'],
                    'reference_1_label' => 'Invoice ID',
                    'reference_1' => $data['invoice_id'],
                ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Billplz API Error', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Billplz Service Error', [
                'message' => $e->getMessage(),
                'data' => $data
            ]);
            return null;
        }
    }

    /**
     * Get bill information
     */
    public function getBill($billId)
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, '')
                ->get($this->apiUrl . '/v3/bills/' . $billId);

            if ($response->successful()) {
                return $response->json();
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Billplz Get Bill Error', [
                'message' => $e->getMessage(),
                'bill_id' => $billId
            ]);
            return null;
        }
    }

    /**
     * Verify X-Signature for webhook
     */
    public function verifySignature($data, $signature)
    {
        $calculatedSignature = hash_hmac('sha256', http_build_query($data), $this->xSignatureKey);
        return hash_equals($calculatedSignature, $signature);
    }

    /**
     * Delete a bill
     */
    public function deleteBill($billId)
    {
        try {
            $response = Http::withBasicAuth($this->apiKey, '')
                ->delete($this->apiUrl . '/v3/bills/' . $billId);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Billplz Delete Bill Error', [
                'message' => $e->getMessage(),
                'bill_id' => $billId
            ]);
            return false;
        }
    }
}
