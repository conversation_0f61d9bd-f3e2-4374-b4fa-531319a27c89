<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Schedule;
use App\Models\AcademicYear;
use App\Models\AcademicTerm;
use App\Models\ClassModel;
use App\Models\Section;
use App\Models\Subject;
use App\Models\User;
use App\Models\TimeSlot;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ScheduleController extends Controller
{
    public function index(Request $request)
    {
        $currentYear = AcademicYear::current();
        $currentTerm = AcademicTerm::current();

        $academicYears = AcademicYear::where('is_active', true)->get();
        $academicTerms = AcademicTerm::where('is_active', true)->get();
        $classes = ClassModel::where('is_active', true)->get();
        $sections = Section::where('is_active', true)->get();

        $selectedYear = $request->academic_year_id ?? $currentYear?->id;
        $selectedTerm = $request->academic_term_id ?? $currentTerm?->id;
        $selectedClass = $request->class_id;
        $selectedSection = $request->section_id;

        // Build the query for schedules
        $query = Schedule::where('is_active', true)
                        ->with(['subject', 'teacher', 'timeSlot', 'class', 'section']);

        // Apply filters if provided
        if ($selectedTerm) {
            $query->where('academic_term_id', $selectedTerm);
        }
        if ($selectedClass) {
            $query->where('class_id', $selectedClass);
        }
        if ($selectedSection) {
            $query->where('section_id', $selectedSection);
        }

        // Get paginated schedules for the table view
        $schedules = $query->orderBy('day_of_week')
                          ->orderBy('time_slot_id')
                          ->paginate(15);

        // Get filter data from all schedules (not just current page)
        $allSchedules = Schedule::where('is_active', true)
                               ->with(['subject', 'class'])
                               ->get();

        $timeSlots = TimeSlot::classSlots()->get();
        $weekDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

        return view('admin.schedules.index', compact(
            'schedules', 'allSchedules', 'timeSlots', 'weekDays', 'academicYears', 'academicTerms',
            'classes', 'sections', 'selectedYear', 'selectedTerm', 'selectedClass', 'selectedSection'
        ));
    }

    public function create(Request $request)
    {
        $academicYears = AcademicYear::where('is_active', true)->get();
        $academicTerms = AcademicTerm::where('is_active', true)->get();
        $classes = ClassModel::where('is_active', true)->get();
        $sections = Section::where('is_active', true)->get();
        $subjects = Subject::where('is_active', true)->get();
        $teachers = User::where('role', 'teacher')->where('is_active', true)->get();
        $timeSlots = TimeSlot::classSlots()->get();

        $weekDays = [
            'monday' => 'Monday',
            'tuesday' => 'Tuesday', 
            'wednesday' => 'Wednesday',
            'thursday' => 'Thursday',
            'friday' => 'Friday',
            'saturday' => 'Saturday'
        ];

        return view('admin.schedules.create', compact(
            'academicYears', 'academicTerms', 'classes', 'sections',
            'subjects', 'teachers', 'timeSlots', 'weekDays'
        ));
    }

    public function show(Schedule $schedule)
    {
        $schedule->load(['class', 'section', 'subject', 'teacher', 'timeSlot', 'academicYear', 'academicTerm']);

        return view('admin.schedules.show', compact('schedule'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'class_id' => 'required|exists:school_classes,id',
            'section_id' => 'required|exists:sections,id',
            'subject_id' => 'required|exists:subjects,id',
            'teacher_id' => 'required|exists:users,id',
            'time_slot_id' => 'required|exists:time_slots,id',
            'day_of_week' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'room_number' => 'nullable|string|max:50',
        ]);

        // Check for conflicts
        $conflict = Schedule::where('class_id', $request->class_id)
                          ->where('section_id', $request->section_id)
                          ->where('day_of_week', $request->day_of_week)
                          ->where('time_slot_id', $request->time_slot_id)
                          ->where('academic_term_id', $request->academic_term_id)
                          ->exists();

        if ($conflict) {
            return back()->withErrors(['conflict' => 'A schedule already exists for this class, section, day, and time slot.']);
        }

        Schedule::create($request->all());

        return redirect()->route('admin.schedules.index')
                        ->with('success', 'Schedule created successfully.');
    }

    public function edit(Schedule $schedule)
    {
        $academicYears = AcademicYear::where('is_active', true)->get();
        $academicTerms = AcademicTerm::where('is_active', true)->get();
        $classes = ClassModel::where('is_active', true)->get();
        $sections = Section::where('is_active', true)->get();
        $subjects = Subject::where('is_active', true)->get();
        $teachers = User::where('role', 'teacher')->where('is_active', true)->get();
        $timeSlots = TimeSlot::classSlots()->get();

        $weekDays = [
            'monday' => 'Monday',
            'tuesday' => 'Tuesday', 
            'wednesday' => 'Wednesday',
            'thursday' => 'Thursday',
            'friday' => 'Friday',
            'saturday' => 'Saturday'
        ];

        return view('admin.schedules.edit', compact(
            'schedule', 'academicYears', 'academicTerms', 'classes', 'sections', 
            'subjects', 'teachers', 'timeSlots', 'weekDays'
        ));
    }

    public function update(Request $request, Schedule $schedule)
    {
        $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'class_id' => 'required|exists:school_classes,id',
            'section_id' => 'required|exists:sections,id',
            'subject_id' => 'required|exists:subjects,id',
            'teacher_id' => 'required|exists:users,id',
            'time_slot_id' => 'required|exists:time_slots,id',
            'day_of_week' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'room_number' => 'nullable|string|max:50',
        ]);

        // Check for conflicts (excluding current schedule)
        $conflict = Schedule::where('class_id', $request->class_id)
                          ->where('section_id', $request->section_id)
                          ->where('day_of_week', $request->day_of_week)
                          ->where('time_slot_id', $request->time_slot_id)
                          ->where('academic_term_id', $request->academic_term_id)
                          ->where('id', '!=', $schedule->id)
                          ->exists();

        if ($conflict) {
            return back()->withErrors(['conflict' => 'A schedule already exists for this class, section, day, and time slot.']);
        }

        $schedule->update($request->all());

        return redirect()->route('admin.schedules.index')
                        ->with('success', 'Schedule updated successfully.');
    }

    public function destroy(Schedule $schedule)
    {
        $schedule->delete();

        return redirect()->route('admin.schedules.index')
                        ->with('success', 'Schedule deleted successfully.');
    }
}
