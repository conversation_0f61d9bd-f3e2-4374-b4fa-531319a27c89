@props(['placeholder' => 'Search...', 'showDateFilter' => true, 'showStatusFilter' => false, 'statusOptions' => []])

<div class="bg-white shadow rounded-lg p-6 mb-6" x-data="{ 
    searchQuery: '', 
    dateFrom: '', 
    dateTo: '', 
    status: '',
    showAdvanced: false 
}">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
        <!-- Search Input -->
        <div class="flex-1">
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <input
                    type="text"
                    x-model="searchQuery"
                    @input="filterResults()"
                    class="search-input"
                    placeholder="{{ $placeholder }}"
                >
            </div>
        </div>

        <!-- Filter Toggle Button -->
        <div class="flex items-center space-x-3">
            <button 
                @click="showAdvanced = !showAdvanced"
                class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
            >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                </svg>
                Filters
                <svg class="w-4 h-4 ml-1 transition-transform duration-200" :class="showAdvanced ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>

            <!-- Clear Filters -->
            <button 
                @click="clearFilters()"
                class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
            >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Clear All Filters
            </button>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div x-show="showAdvanced" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 transform translate-y-0" x-transition:leave-end="opacity-0 transform -translate-y-2" class="mt-4 pt-4 border-t border-gray-200">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            @if($showDateFilter)
            <!-- Date From -->
            <div>
                <label for="date-from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                <input
                    type="date"
                    id="date-from"
                    x-model="dateFrom"
                    @change="filterResults()"
                    class="form-input"
                >
            </div>

            <!-- Date To -->
            <div>
                <label for="date-to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                <input
                    type="date"
                    id="date-to"
                    x-model="dateTo"
                    @change="filterResults()"
                    class="form-input"
                >
            </div>
            @endif

            @if($showStatusFilter && count($statusOptions) > 0)
            <!-- Status Filter -->
            <div>
                <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                    id="status-filter"
                    x-model="status"
                    @change="filterResults()"
                    class="form-select"
                >
                    <option value="">All Status</option>
                    @foreach($statusOptions as $value => $label)
                        <option value="{{ $value }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>
            @endif
        </div>
    </div>
</div>

<script>
function filterResults() {
    const searchQuery = document.querySelector('[x-model="searchQuery"]').value.toLowerCase();
    const dateFrom = document.querySelector('[x-model="dateFrom"]')?.value;
    const dateTo = document.querySelector('[x-model="dateTo"]')?.value;
    const status = document.querySelector('[x-model="status"]')?.value;
    
    // Get all filterable items (you can customize the selector based on your needs)
    const items = document.querySelectorAll('[data-filterable]');
    
    items.forEach(item => {
        let showItem = true;
        
        // Text search
        if (searchQuery) {
            const searchableText = item.getAttribute('data-search-text') || item.textContent;
            if (!searchableText.toLowerCase().includes(searchQuery)) {
                showItem = false;
            }
        }
        
        // Date filtering
        if (dateFrom || dateTo) {
            const itemDate = item.getAttribute('data-date');
            if (itemDate) {
                const itemDateObj = new Date(itemDate);
                if (dateFrom && itemDateObj < new Date(dateFrom)) {
                    showItem = false;
                }
                if (dateTo && itemDateObj > new Date(dateTo)) {
                    showItem = false;
                }
            }
        }
        
        // Status filtering
        if (status) {
            const itemStatus = item.getAttribute('data-status');
            if (itemStatus !== status) {
                showItem = false;
            }
        }
        
        // Show/hide item
        item.style.display = showItem ? '' : 'none';
    });
    
    // Update results count if element exists
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        const visibleItems = document.querySelectorAll('[data-filterable]:not([style*="display: none"])');
        resultsCount.textContent = visibleItems.length;
    }
}

function clearFilters() {
    // Reset form inputs first
    const searchInput = document.querySelector('[x-model="searchQuery"]');
    const dateFromInput = document.querySelector('[x-model="dateFrom"]');
    const dateToInput = document.querySelector('[x-model="dateTo"]');
    const statusInput = document.querySelector('[x-model="status"]');

    if (searchInput) {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    }
    if (dateFromInput) {
        dateFromInput.value = '';
        dateFromInput.dispatchEvent(new Event('change'));
    }
    if (dateToInput) {
        dateToInput.value = '';
        dateToInput.dispatchEvent(new Event('change'));
    }
    if (statusInput) {
        statusInput.value = '';
        statusInput.dispatchEvent(new Event('change'));
    }

    // Show all items
    const items = document.querySelectorAll('[data-filterable]');
    items.forEach(item => {
        item.style.display = '';
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = items.length;
    }
}
</script>
