@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Guardian Management"
        description="Manage parents and guardians information"
        :back-route="route('admin.dashboard')"
        back-label="Back to Dashboard">
        <a href="{{ route('admin.guardians.create') }}" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Guardian
        </a>
    </x-page-header>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6"
         x-data="{
             searchQuery: '{{ request('search') }}',
             relationship: '{{ request('relationship') }}',
             occupation: '{{ request('occupation') }}',
             status: '{{ request('status') }}',
             incomeMin: '{{ request('income_min') }}',
             incomeMax: '{{ request('income_max') }}',
             showAdvanced: false
         }">

        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
            <!-- Search Input -->
            <div class="flex-1">
                <input
                    type="text"
                    x-model="searchQuery"
                    @input="filterGuardians()"
                    placeholder="Search guardians by name, email, phone, address, relationship, occupation, workplace, emergency contact, income, or children names..."
                    class="form-input w-full"
                >
            </div>

            <!-- Filter Toggle and Clear -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                    <svg class="ml-2 h-4 w-4 transition-transform duration-200" :class="{ 'rotate-180': showAdvanced }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <button
                    @click="clearGuardianFilters()"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition class="mt-6 pt-6 border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <!-- Relationship Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Relationship</label>
                    <select x-model="relationship" @change="filterGuardians()" class="form-select">
                        <option value="">All Relationships</option>
                        @foreach($relationships as $rel)
                            <option value="{{ $rel }}">{{ $rel }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Occupation Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Occupation</label>
                    <select x-model="occupation" @change="filterGuardians()" class="form-select">
                        <option value="">All Occupations</option>
                        @foreach($occupations as $occ)
                            <option value="{{ $occ }}">{{ $occ }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select x-model="status" @change="filterGuardians()" class="form-select">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>

                <!-- Income Min -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Min Income (RM)</label>
                    <input type="number" x-model="incomeMin" @change="filterGuardians()" class="form-input" placeholder="0">
                </div>

                <!-- Income Max -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Max Income (RM)</label>
                    <input type="number" x-model="incomeMax" @change="filterGuardians()" class="form-input" placeholder="50000">
                </div>
            </div>
        </div>

        <!-- Results Count -->
        <div class="mt-4 flex items-center justify-between">
            <span class="text-sm text-gray-500">
                <span data-results-count>{{ $stats['total'] }}</span> guardians found
            </span>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Guardians</dt>
                            <dd class="stat-card-value">{{ $stats['total'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Active</dt>
                            <dd class="stat-card-value">{{ $stats['active'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">With Children</dt>
                            <dd class="stat-card-value">{{ $stats['with_children'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Avg Income</dt>
                            <dd class="stat-card-value">RM {{ number_format($stats['avg_income'] ?? 0, 0) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Guardians -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Current Guardians</h3>
                <span class="text-sm text-gray-500">
                    <span data-results-count>{{ $stats['total'] }}</span> guardians found
                </span>
            </div>
        </div>
        <div class="p-6">
            @if($guardians->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($guardians as $guardian)
                        <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200 card-hover relative"
                             data-filterable
                             data-search-text="{{ $guardian->user->name }} {{ $guardian->user->email }} {{ $guardian->user->phone ?? '' }} {{ $guardian->user->address ?? '' }} {{ $guardian->relationship }} {{ $guardian->occupation }} {{ $guardian->workplace ?? '' }} {{ $guardian->emergency_contact ?? '' }} {{ $guardian->monthly_income ?? '' }} {{ $guardian->user->is_active ? 'active' : 'inactive' }} @foreach($guardian->students as $student) {{ $student->user->name }} @endforeach"
                             data-relationship="{{ $guardian->relationship }}"
                             data-occupation="{{ $guardian->occupation }}"
                             data-income="{{ $guardian->monthly_income }}"
                             data-status="{{ $guardian->user->is_active ? 'active' : 'inactive' }}">

                            <!-- 3-dot menu -->
                            <div class="absolute top-2 right-2 z-30" style="right: 8px; top: 8px;">
                                <button class="three-dot-menu p-1 rounded-full hover:bg-gray-100 transition-colors duration-200" onclick="toggleCardMenu(this)">
                                    <svg class="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                                    </svg>
                                </button>
                                <!-- Dropdown menu (hidden by default) -->
                                <div class="card-menu absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-40 hidden">
                                    <div class="py-1">
                                        <a href="{{ route('admin.guardians.show', $guardian) }}" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            View
                                        </a>
                                        <a href="{{ route('admin.guardians.edit', $guardian) }}" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                            Edit
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-14 h-14 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center shadow-md">
                                        <span class="text-white font-bold text-lg">
                                            {{ substr($guardian->user->name, 0, 2) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4 flex-1">
                                    <h4 class="text-lg font-semibold text-gray-900">{{ $guardian->user->name }}</h4>
                                    <p class="text-sm text-gray-600 mt-1">{{ $guardian->user->email }}</p>
                                    <div class="flex items-center mt-2 space-x-2">
                                        <span class="badge badge-blue">
                                            {{ ucfirst($guardian->relationship) }}
                                        </span>
                                        @if($guardian->occupation)
                                            <span class="badge badge-green">
                                                {{ $guardian->occupation }}
                                            </span>
                                        @endif
                                        <span class="badge {{ $guardian->user->is_active ? 'badge-green' : 'badge-red' }}">
                                            {{ $guardian->user->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 pt-4 border-t border-gray-100">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-700">Children</p>
                                        <div class="mt-1">
                                            @if($guardian->students->count() > 0)
                                                <div class="flex flex-wrap gap-1">
                                                    @foreach($guardian->students as $student)
                                                        <span class="badge badge-purple">
                                                            {{ $student->user->name }}
                                                        </span>
                                                    @endforeach
                                                </div>
                                            @else
                                                <span class="text-sm text-gray-500">No children assigned</span>
                                            @endif
                                        </div>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-700">Contact</p>
                                        <p class="text-sm text-gray-600 mt-1">{{ $guardian->user->phone ?? 'No phone number' }}</p>
                                    </div>
                                </div>
                            </div>


                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-center text-gray-500 py-8">No guardians found</p>
            @endif
        </div>
    </div>

</div>

@push('scripts')
<script>
// Toggle card menu function
function toggleCardMenu(button) {
    const menu = button.nextElementSibling;
    const allMenus = document.querySelectorAll('.card-menu');

    // Close all other menus
    allMenus.forEach(m => {
        if (m !== menu) {
            m.classList.add('hidden');
        }
    });

    // Toggle current menu
    menu.classList.toggle('hidden');
}

// Close menus when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.three-dot-menu') && !event.target.closest('.card-menu')) {
        const allMenus = document.querySelectorAll('.card-menu');
        allMenus.forEach(menu => {
            menu.classList.add('hidden');
        });
    }
});

// Guardian filtering functionality
function filterGuardians() {
    const searchQuery = document.querySelector('[x-model="searchQuery"]').value.toLowerCase();
    const relationshipFilter = document.querySelector('[x-model="relationship"]').value;
    const occupationFilter = document.querySelector('[x-model="occupation"]').value;
    const statusFilter = document.querySelector('[x-model="status"]').value;
    const incomeMinFilter = document.querySelector('[x-model="incomeMin"]').value;
    const incomeMaxFilter = document.querySelector('[x-model="incomeMax"]').value;

    const guardians = document.querySelectorAll('[data-filterable]');
    let visibleCount = 0;

    guardians.forEach(guardian => {
        const searchText = guardian.getAttribute('data-search-text').toLowerCase();
        const guardianRelationship = guardian.getAttribute('data-relationship');
        const guardianOccupation = guardian.getAttribute('data-occupation');
        const guardianStatus = guardian.getAttribute('data-status');
        const guardianIncome = parseFloat(guardian.getAttribute('data-income')) || 0;

        let isVisible = true;

        // Search filter
        if (searchQuery && !searchText.includes(searchQuery)) {
            isVisible = false;
        }

        // Relationship filter
        if (relationshipFilter && guardianRelationship !== relationshipFilter) {
            isVisible = false;
        }

        // Occupation filter
        if (occupationFilter && (!guardianOccupation || !guardianOccupation.toLowerCase().includes(occupationFilter.toLowerCase()))) {
            isVisible = false;
        }

        // Status filter
        if (statusFilter && guardianStatus !== statusFilter) {
            isVisible = false;
        }

        // Income range filter
        if (incomeMinFilter && guardianIncome < parseFloat(incomeMinFilter)) {
            isVisible = false;
        }

        if (incomeMaxFilter && guardianIncome > parseFloat(incomeMaxFilter)) {
            isVisible = false;
        }

        // Show/hide guardian
        guardian.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = visibleCount;
    }
}

// Clear all filters
function clearGuardianFilters() {
    // Reset form inputs and trigger events
    const searchInput = document.querySelector('[x-model="searchQuery"]');
    const relationshipInput = document.querySelector('[x-model="relationship"]');
    const occupationInput = document.querySelector('[x-model="occupation"]');
    const statusInput = document.querySelector('[x-model="status"]');
    const incomeMinInput = document.querySelector('[x-model="incomeMin"]');
    const incomeMaxInput = document.querySelector('[x-model="incomeMax"]');

    if (searchInput) {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    }
    if (relationshipInput) {
        relationshipInput.value = '';
        relationshipInput.dispatchEvent(new Event('change'));
    }
    if (occupationInput) {
        occupationInput.value = '';
        occupationInput.dispatchEvent(new Event('change'));
    }
    if (statusInput) {
        statusInput.value = '';
        statusInput.dispatchEvent(new Event('change'));
    }
    if (incomeMinInput) {
        incomeMinInput.value = '';
        incomeMinInput.dispatchEvent(new Event('change'));
    }
    if (incomeMaxInput) {
        incomeMaxInput.value = '';
        incomeMaxInput.dispatchEvent(new Event('change'));
    }

    // Show all guardians
    const guardians = document.querySelectorAll('[data-filterable]');
    guardians.forEach(guardian => {
        guardian.style.display = '';
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = guardians.length;
    }
}
</script>
@endpush
@endsection
