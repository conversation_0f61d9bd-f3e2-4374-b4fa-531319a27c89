<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Guardian;
use App\Models\Student;
use App\Models\Teacher;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class ExtendedSchoolDataSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create more teachers
        $this->createTeachers();
        
        // Create families with multiple children and guardians
        $this->createFamilies();
        
        // Create single parent families
        $this->createSingleParentFamilies();
        
        // Create guardian-student relationships
        $this->createGuardianStudentRelationships();
    }

    private function createTeachers()
    {
        $teachers = [
            [
                'name' => 'Ms. <PERSON>riya <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+60123456701',
                'employee_id' => 'TCH003',
                'qualification' => 'Master of Science in Mathematics',
                'specialization' => 'Mathematics',
                'subjects' => ['Mathematics', 'Additional Mathematics'],
                'classes' => ['Form 4A', 'Form 4B', 'Form 5A'],
                'salary' => 4800.00,
            ],
            [
                'name' => 'Mr. <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+60123456702',
                'employee_id' => 'TCH004',
                'qualification' => 'Bachelor of Arts in English Literature',
                'specialization' => 'English Language',
                'subjects' => ['English', 'Literature'],
                'classes' => ['Form 1A', 'Form 2A', 'Form 3A'],
                'salary' => 4200.00,
            ],
            [
                'name' => 'Dr. Fatimah Abdullah',
                'email' => '<EMAIL>',
                'phone' => '+60123456703',
                'employee_id' => 'TCH005',
                'qualification' => 'PhD in Chemistry',
                'specialization' => 'Chemistry',
                'subjects' => ['Chemistry', 'Science'],
                'classes' => ['Form 4A', 'Form 5A', 'Form 5B'],
                'salary' => 5500.00,
            ],
            [
                'name' => 'Mr. Raj Kumar',
                'email' => '<EMAIL>',
                'phone' => '+60123456704',
                'employee_id' => 'TCH006',
                'qualification' => 'Master of Education in Physical Education',
                'specialization' => 'Physical Education',
                'subjects' => ['Physical Education', 'Health Education'],
                'classes' => ['Form 1A', 'Form 1B', 'Form 2A', 'Form 2B'],
                'salary' => 3800.00,
            ],
            [
                'name' => 'Ms. Lisa Wong',
                'email' => '<EMAIL>',
                'phone' => '+60123456705',
                'employee_id' => 'TCH007',
                'qualification' => 'Bachelor of Science in Computer Science',
                'specialization' => 'Information Technology',
                'subjects' => ['Computer Science', 'Information Technology'],
                'classes' => ['Form 3A', 'Form 4A', 'Form 5A'],
                'salary' => 4600.00,
            ],
        ];

        foreach ($teachers as $teacherData) {
            // Check if user already exists
            if (User::where('email', $teacherData['email'])->exists()) {
                continue;
            }

            $user = User::create([
                'name' => $teacherData['name'],
                'email' => $teacherData['email'],
                'phone' => $teacherData['phone'],
                'password' => Hash::make('password123'),
                'user_type' => 'teacher',
                'address' => 'Teacher Quarters, School Campus',
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            Teacher::create([
                'user_id' => $user->id,
                'employee_id' => $teacherData['employee_id'],
                'qualification' => $teacherData['qualification'],
                'specialization' => $teacherData['specialization'],
                'hire_date' => now()->subMonths(rand(6, 36)),
                'salary' => $teacherData['salary'],
                'subjects' => $teacherData['subjects'],
                'classes' => $teacherData['classes'],
            ]);
        }
    }

    private function createFamilies()
    {
        // Family 1: The Lim Family (Both parents, 3 children)
        $this->createFamily([
            'father' => [
                'name' => 'Mr. Lim Chong Wei',
                'email' => '<EMAIL>',
                'phone' => '+***********',
                'relationship' => 'Father',
                'occupation' => 'Accountant',
                'workplace' => 'KL Accounting Firm',
                'monthly_income' => 7500.00,
            ],
            'mother' => [
                'name' => 'Mrs. Lim Mei Ling',
                'email' => '<EMAIL>',
                'phone' => '+***********',
                'relationship' => 'Mother',
                'occupation' => 'Nurse',
                'workplace' => 'Pantai Hospital',
                'monthly_income' => 5200.00,
            ],
            'children' => [
                [
                    'name' => 'Lim Wei Jun',
                    'email' => '<EMAIL>',
                    'student_id' => 'STU010',
                    'date_of_birth' => now()->subYears(17),
                    'gender' => 'male',
                    'class' => 'Form 5A',
                    'section' => 'Science',
                    'roll_number' => '010',
                    'blood_group' => 'B+',
                ],
                [
                    'name' => 'Lim Wei Ling',
                    'email' => '<EMAIL>',
                    'student_id' => 'STU011',
                    'date_of_birth' => now()->subYears(15),
                    'gender' => 'female',
                    'class' => 'Form 3A',
                    'section' => 'Science',
                    'roll_number' => '011',
                    'blood_group' => 'B+',
                ],
                [
                    'name' => 'Lim Wei Ming',
                    'email' => '<EMAIL>',
                    'student_id' => 'STU012',
                    'date_of_birth' => now()->subYears(13),
                    'gender' => 'male',
                    'class' => 'Form 1A',
                    'section' => 'General',
                    'roll_number' => '012',
                    'blood_group' => 'A+',
                ],
            ],
        ]);

        // Family 2: The Rahman Family (Both parents, 2 children)
        $this->createFamily([
            'father' => [
                'name' => 'Mr. Rahman Ismail',
                'email' => '<EMAIL>',
                'phone' => '+60123456720',
                'relationship' => 'Father',
                'occupation' => 'Engineer',
                'workplace' => 'Petronas',
                'monthly_income' => 9500.00,
            ],
            'mother' => [
                'name' => 'Mrs. Siti Rahman',
                'email' => '<EMAIL>',
                'phone' => '+60123456721',
                'relationship' => 'Mother',
                'occupation' => 'Teacher',
                'workplace' => 'SMK Bandar Utama',
                'monthly_income' => 4800.00,
            ],
            'children' => [
                [
                    'name' => 'Ahmad Rahman',
                    'email' => '<EMAIL>',
                    'student_id' => 'STU013',
                    'date_of_birth' => now()->subYears(16),
                    'gender' => 'male',
                    'class' => 'Form 4A',
                    'section' => 'Science',
                    'roll_number' => '013',
                    'blood_group' => 'O+',
                ],
                [
                    'name' => 'Fatimah Rahman',
                    'email' => '<EMAIL>',
                    'student_id' => 'STU014',
                    'date_of_birth' => now()->subYears(14),
                    'gender' => 'female',
                    'class' => 'Form 2A',
                    'section' => 'General',
                    'roll_number' => '014',
                    'blood_group' => 'O+',
                ],
            ],
        ]);
    }

    private function createSingleParentFamilies()
    {
        // Single Mother Family
        // Check if user already exists
        if (User::where('email', '<EMAIL>')->exists()) {
            return; // Skip if already exists
        }

        $motherUser = User::create([
            'name' => 'Ms. Jennifer Lee',
            'email' => '<EMAIL>',
            'phone' => '+60123456730',
            'password' => Hash::make('password123'),
            'user_type' => 'guardian',
            'address' => '789 Single Parent Street, Kuala Lumpur',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $mother = Guardian::create([
            'user_id' => $motherUser->id,
            'relationship' => 'Mother',
            'occupation' => 'Graphic Designer',
            'workplace' => 'Creative Agency Sdn Bhd',
            'emergency_contact' => '+60123456731',
            'monthly_income' => 4500.00,
        ]);

        // Create child for single mother
        $childUser = User::create([
            'name' => 'Marcus Lee',
            'email' => '<EMAIL>',
            'phone' => '+60123456732',
            'password' => Hash::make('password123'),
            'user_type' => 'student',
            'address' => '789 Single Parent Street, Kuala Lumpur',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $child = Student::create([
            'user_id' => $childUser->id,
            'student_id' => 'STU015',
            'date_of_birth' => now()->subYears(15),
            'gender' => 'male',
            'class' => 'Form 3B',
            'section' => 'Arts',
            'roll_number' => '015',
            'admission_date' => now()->subYears(3),
            'blood_group' => 'AB+',
            'medical_conditions' => 'Asthma',
            'emergency_contact' => '+60123456731',
            'guardian_ids' => [$mother->id],
        ]);

        // Attach guardian to student
        $mother->students()->attach($child->id, ['relationship_type' => 'Primary']);
    }

    private function createFamily($familyData)
    {
        $guardians = [];

        // Create father if provided
        if (isset($familyData['father'])) {
            // Check if user already exists
            if (User::where('email', $familyData['father']['email'])->exists()) {
                return; // Skip this family if father already exists
            }

            $fatherUser = User::create([
                'name' => $familyData['father']['name'],
                'email' => $familyData['father']['email'],
                'phone' => $familyData['father']['phone'],
                'password' => Hash::make('password123'),
                'user_type' => 'guardian',
                'address' => 'Family Home Address',
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            $father = Guardian::create([
                'user_id' => $fatherUser->id,
                'relationship' => $familyData['father']['relationship'],
                'occupation' => $familyData['father']['occupation'],
                'workplace' => $familyData['father']['workplace'],
                'emergency_contact' => $familyData['father']['phone'],
                'monthly_income' => $familyData['father']['monthly_income'],
            ]);

            $guardians['father'] = $father;
        }

        // Create mother if provided
        if (isset($familyData['mother'])) {
            // Check if user already exists
            if (User::where('email', $familyData['mother']['email'])->exists()) {
                return; // Skip this family if mother already exists
            }

            $motherUser = User::create([
                'name' => $familyData['mother']['name'],
                'email' => $familyData['mother']['email'],
                'phone' => $familyData['mother']['phone'],
                'password' => Hash::make('password123'),
                'user_type' => 'guardian',
                'address' => 'Family Home Address',
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            $mother = Guardian::create([
                'user_id' => $motherUser->id,
                'relationship' => $familyData['mother']['relationship'],
                'occupation' => $familyData['mother']['occupation'],
                'workplace' => $familyData['mother']['workplace'],
                'emergency_contact' => $familyData['mother']['phone'],
                'monthly_income' => $familyData['mother']['monthly_income'],
            ]);

            $guardians['mother'] = $mother;
        }

        // Create children
        foreach ($familyData['children'] as $childData) {
            $childUser = User::create([
                'name' => $childData['name'],
                'email' => $childData['email'],
                'phone' => '+60123456799', // Default phone for students
                'password' => Hash::make('password123'),
                'user_type' => 'student',
                'address' => 'Family Home Address',
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            $guardianIds = array_map(function($guardian) {
                return $guardian->id;
            }, $guardians);

            $child = Student::create([
                'user_id' => $childUser->id,
                'student_id' => $childData['student_id'],
                'date_of_birth' => $childData['date_of_birth'],
                'gender' => $childData['gender'],
                'class' => $childData['class'],
                'section' => $childData['section'],
                'roll_number' => $childData['roll_number'],
                'admission_date' => now()->subYears(rand(1, 5)),
                'blood_group' => $childData['blood_group'],
                'medical_conditions' => $childData['medical_conditions'] ?? null,
                'emergency_contact' => $guardians['father']->user->phone ?? $guardians['mother']->user->phone,
                'guardian_ids' => $guardianIds,
            ]);

            // Attach guardians to student with relationship types
            if (isset($guardians['father'])) {
                $guardians['father']->students()->attach($child->id, ['relationship_type' => 'Primary']);
            }
            if (isset($guardians['mother'])) {
                $guardians['mother']->students()->attach($child->id, ['relationship_type' => 'Primary']);
            }
        }
    }

    private function createGuardianStudentRelationships()
    {
        // Create some additional guardian relationships (like grandparents, uncles, etc.)

        // Get some existing students
        $students = Student::take(3)->get();

        if ($students->count() > 0) {
            // Create a grandparent guardian
            if (!User::where('email', '<EMAIL>')->exists()) {
                $grandparentUser = User::create([
                    'name' => 'Mrs. Lim Ah Moi',
                    'email' => '<EMAIL>',
                    'phone' => '+60123456740',
                    'password' => Hash::make('password123'),
                    'user_type' => 'guardian',
                    'address' => 'Grandparent House, Old Town',
                    'is_active' => true,
                    'email_verified_at' => now(),
                ]);

                $grandparent = Guardian::create([
                    'user_id' => $grandparentUser->id,
                    'relationship' => 'Grandmother',
                    'occupation' => 'Retired',
                    'workplace' => 'N/A',
                    'emergency_contact' => '+60123456741',
                    'monthly_income' => 2000.00,
                ]);

                // Attach grandparent as emergency contact to first student
                if ($students->first()) {
                    $grandparent->students()->attach($students->first()->id, ['relationship_type' => 'Emergency']);
                }
            }

            // Create an uncle guardian
            if (!User::where('email', '<EMAIL>')->exists()) {
                $uncleUser = User::create([
                    'name' => 'Mr. Ahmad Zulkifli',
                    'email' => '<EMAIL>',
                    'phone' => '+***********',
                    'password' => Hash::make('password123'),
                    'user_type' => 'guardian',
                    'address' => 'Uncle House, Nearby Area',
                    'is_active' => true,
                    'email_verified_at' => now(),
                ]);

                $uncle = Guardian::create([
                    'user_id' => $uncleUser->id,
                    'relationship' => 'Uncle',
                    'occupation' => 'Business Owner',
                    'workplace' => 'Family Business Sdn Bhd',
                    'emergency_contact' => '+***********',
                    'monthly_income' => 6000.00,
                ]);

                // Attach uncle as secondary guardian to second student
                if ($students->count() > 1) {
                    $uncle->students()->attach($students->get(1)->id, ['relationship_type' => 'Secondary']);
                }
            }
        }
    }
}
