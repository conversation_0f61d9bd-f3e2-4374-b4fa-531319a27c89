<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\StudentAttendance;
use App\Models\TeacherAttendance;
use App\Models\AttendanceSettings;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AttendanceSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create attendance settings
        AttendanceSettings::create([
            'school_start_time' => '08:00:00',
            'school_end_time' => '15:00:00',
            'late_threshold' => '08:15:00',
            'grace_period_minutes' => 15,
            'auto_mark_absent' => true,
            'working_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            'holidays' => [],
            'allow_retroactive_marking' => true,
            'retroactive_days_limit' => 7,
        ]);

        // Get all active students and teachers
        $students = Student::whereHas('user', function($q) {
            $q->where('is_active', true);
        })->get();

        $teachers = Teacher::whereHas('user', function($q) {
            $q->where('is_active', true);
        })->get();

        // Get admin user for marking attendance
        $adminUser = \App\Models\User::where('user_type', 'admin')->first();
        
        if (!$adminUser) {
            $this->command->warn('No admin user found. Skipping attendance seeding.');
            return;
        }

        // Seed attendance for the last 30 days
        $startDate = Carbon::now()->subDays(30);
        $endDate = Carbon::now();

        $this->command->info('Seeding student attendance...');
        
        // Seed student attendance
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            // Skip weekends
            if ($date->isWeekend()) {
                continue;
            }

            foreach ($students as $student) {
                // 85% chance of being present
                $rand = rand(1, 100);
                
                if ($rand <= 85) {
                    $status = 'present';
                    $checkInTime = '08:' . str_pad(rand(0, 10), 2, '0', STR_PAD_LEFT);
                } elseif ($rand <= 90) {
                    $status = 'late';
                    $checkInTime = '08:' . str_pad(rand(15, 30), 2, '0', STR_PAD_LEFT);
                } elseif ($rand <= 95) {
                    $status = 'sick';
                    $checkInTime = null;
                } elseif ($rand <= 98) {
                    $status = 'excused';
                    $checkInTime = null;
                } else {
                    $status = 'absent';
                    $checkInTime = null;
                }

                StudentAttendance::create([
                    'student_id' => $student->id,
                    'marked_by' => $adminUser->id,
                    'date' => $date->format('Y-m-d'),
                    'status' => $status,
                    'check_in_time' => $checkInTime,
                    'class' => $student->class,
                    'section' => $student->section,
                    'notes' => $status === 'sick' ? 'Reported sick' : ($status === 'excused' ? 'Family emergency' : null),
                ]);
            }
        }

        $this->command->info('Seeding teacher attendance...');

        // Seed teacher attendance
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            // Skip weekends
            if ($date->isWeekend()) {
                continue;
            }

            foreach ($teachers as $teacher) {
                // 90% chance of being present
                $rand = rand(1, 100);
                
                if ($rand <= 90) {
                    $status = 'present';
                    $checkInTime = '07:' . str_pad(rand(45, 59), 2, '0', STR_PAD_LEFT);
                    $checkOutTime = '15:' . str_pad(rand(0, 30), 2, '0', STR_PAD_LEFT);
                } elseif ($rand <= 93) {
                    $status = 'late';
                    $checkInTime = '08:' . str_pad(rand(15, 45), 2, '0', STR_PAD_LEFT);
                    $checkOutTime = '15:' . str_pad(rand(0, 30), 2, '0', STR_PAD_LEFT);
                } elseif ($rand <= 96) {
                    $status = 'sick_leave';
                    $checkInTime = null;
                    $checkOutTime = null;
                } elseif ($rand <= 98) {
                    $status = 'personal_leave';
                    $checkInTime = null;
                    $checkOutTime = null;
                } elseif ($rand <= 99) {
                    $status = 'official_duty';
                    $checkInTime = '08:00';
                    $checkOutTime = '15:00';
                } else {
                    $status = 'absent';
                    $checkInTime = null;
                    $checkOutTime = null;
                }

                $attendance = TeacherAttendance::create([
                    'teacher_id' => $teacher->id,
                    'marked_by' => $adminUser->id,
                    'date' => $date->format('Y-m-d'),
                    'status' => $status,
                    'check_in_time' => $checkInTime,
                    'check_out_time' => $checkOutTime,
                    'notes' => $status === 'sick_leave' ? 'Medical leave' : 
                              ($status === 'personal_leave' ? 'Personal matter' : 
                              ($status === 'official_duty' ? 'Training workshop' : null)),
                ]);

                // Calculate hours worked
                if ($checkInTime && $checkOutTime) {
                    $attendance->calculateHoursWorked();
                }
            }
        }

        $this->command->info('Attendance seeding completed successfully!');
    }
}
