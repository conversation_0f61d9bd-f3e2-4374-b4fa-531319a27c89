@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Class Details"
        description="View and manage class information"
        :back-route="route('admin.academic.classes.index')"
        back-label="Back to Classes">
        
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.academic.classes.edit', $class) }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Class
            </a>
        </div>
    </x-page-header>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Class Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="card">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    <div class="flex items-center space-x-2">
                        <span class="badge badge-{{ $class->level_badge_color }}">
                            {{ $class->level }}
                        </span>
                        <span class="badge badge-{{ $class->status_badge_color }}">
                            {{ $class->status_display }}
                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Class Name</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $class->name }}</dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Level</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $class->level }}</dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Sort Order</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $class->sort_order }}</dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Created</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $class->created_at->format('M j, Y') }}</dd>
                    </div>
                </div>

                @if($class->description)
                    <div class="mt-6">
                        <dt class="text-sm font-medium text-gray-500">Description</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $class->description }}</dd>
                    </div>
                @endif
            </div>
        </div>

        <!-- Statistics -->
        <div class="space-y-6">
            <!-- Statistics -->
            <div class="card">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Total Sections</span>
                        <span class="text-sm font-medium text-gray-900">{{ $class->total_sections }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Total Students</span>
                        <span class="text-sm font-medium text-gray-900">{{ $class->total_students }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Total Subjects</span>
                        <span class="text-sm font-medium text-gray-900">{{ $class->total_subjects }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
