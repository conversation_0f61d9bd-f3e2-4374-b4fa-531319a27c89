@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Create New Exam"
        description="Fill in the details to schedule a new exam"
        :back-route="route('admin.exams.index')"
        back-label="Back to Exams">
        <!-- Slot for top-right button (optional, can add more buttons here if needed) -->
    </x-page-header>

    <!-- Exam Creation Form -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="POST" action="{{ route('admin.exams.store') }}" class="space-y-6">
            @csrf
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Exam Name <span class="text-red-500">*</span></label>
                    <input type="text" name="name" id="name" required value="{{ old('name') }}"
                           class="form-input">
                </div>
                <div>
                    <label for="exam_type_id" class="block text-sm font-medium text-gray-700 mb-1">Exam Type <span class="text-red-500">*</span></label>
                    <select name="exam_type_id" id="exam_type_id" required
                            class="form-select">
                        <option value="">Select type...</option>
                        @foreach($examTypes as $type)
                            <option value="{{ $type->id }}" {{ old('exam_type_id') == $type->id ? 'selected' : '' }}>{{ $type->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="academic_term_id" class="block text-sm font-medium text-gray-700 mb-1">Academic Term <span class="text-red-500">*</span></label>
                    <select name="academic_term_id" id="academic_term_id" required
                            class="form-select">
                        <option value="">Select term...</option>
                        @foreach($academicTerms as $term)
                            <option value="{{ $term->id }}" {{ old('academic_term_id') == $term->id ? 'selected' : '' }}>{{ $term->name }} ({{ $term->academicYear->name }})</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea name="description" id="description" rows="2"
                              class="form-textarea">{{ old('description') }}</textarea>
                </div>
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date <span class="text-red-500">*</span></label>
                    <input type="date" name="start_date" id="start_date" required value="{{ old('start_date') }}"
                           class="form-input">
                </div>
                <div>
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date <span class="text-red-500">*</span></label>
                    <input type="date" name="end_date" id="end_date" required value="{{ old('end_date') }}"
                           class="form-input">
                </div>
            </div>
            <div>
                <label for="instructions" class="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
                <textarea name="instructions[]" id="instructions" rows="2" placeholder="Add instructions (one per line)"
                          class="form-textarea">{{ old('instructions.0') }}</textarea>
            </div>
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.exams.index') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Cancel
                </a>
                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Exam
                </button>
            </div>
        </form>
    </div>
</div>
@endsection 