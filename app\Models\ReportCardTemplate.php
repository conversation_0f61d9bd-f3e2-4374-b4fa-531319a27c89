<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ReportCardTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'layout_config',
        'grade_components',
        'calculation_rules',
        'include_attendance',
        'include_remarks',
        'is_default',
        'is_active',
    ];

    protected $casts = [
        'layout_config' => 'array',
        'grade_components' => 'array',
        'calculation_rules' => 'array',
        'include_attendance' => 'boolean',
        'include_remarks' => 'boolean',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the report cards using this template.
     */
    public function reportCards(): HasMany
    {
        return $this->hasMany(ReportCard::class, 'template_id');
    }

    /**
     * Get the default template.
     */
    public static function default()
    {
        return static::where('is_default', true)->where('is_active', true)->first();
    }

    /**
     * Get active templates.
     */
    public static function active()
    {
        return static::where('is_active', true)->orderBy('name');
    }

    /**
     * Set this template as default.
     */
    public function setAsDefault()
    {
        // Remove default from all other templates
        static::where('is_default', true)->update(['is_default' => false]);
        
        // Set this template as default
        $this->update(['is_default' => true]);
    }
}
