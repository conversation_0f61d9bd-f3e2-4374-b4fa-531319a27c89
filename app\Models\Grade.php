<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Grade extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'subject_id',
        'teacher_id',
        'grade_category_id',
        'academic_term_id',
        'assignment_name',
        'description',
        'score',
        'max_score',
        'letter_grade',
        'gpa_points',
        'assignment_date',
        'due_date',
        'comments',
        'is_published',
    ];

    protected $casts = [
        'score' => 'decimal:2',
        'max_score' => 'decimal:2',
        'percentage' => 'decimal:2',
        'gpa_points' => 'decimal:2',
        'assignment_date' => 'date',
        'due_date' => 'date',
        'is_published' => 'boolean',
    ];

    // Note: percentage is a computed column in the database

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    public function gradeCategory(): BelongsTo
    {
        return $this->belongsTo(GradeCategory::class);
    }

    public function academicTerm(): BelongsTo
    {
        return $this->belongsTo(AcademicTerm::class);
    }

    public function updateLetterGrade()
    {
        // Refresh to get the computed percentage value
        $this->refresh();
        $this->letter_grade = $this->calculateLetterGrade($this->percentage);
        $this->gpa_points = $this->calculateGpaPoints($this->percentage);
        $this->save();
    }

    protected function calculateLetterGrade($percentage)
    {
        if ($percentage >= 90) return 'A';
        if ($percentage >= 80) return 'B';
        if ($percentage >= 70) return 'C';
        if ($percentage >= 60) return 'D';
        return 'F';
    }

    protected function calculateGpaPoints($percentage)
    {
        if ($percentage >= 90) return 4.0;
        if ($percentage >= 80) return 3.0;
        if ($percentage >= 70) return 2.0;
        if ($percentage >= 60) return 1.0;
        return 0.0;
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeForStudent($query, $studentId)
    {
        return $query->where('student_id', $studentId);
    }

    public function scopeForSubject($query, $subjectId)
    {
        return $query->where('subject_id', $subjectId);
    }

    public function scopeForTerm($query, $termId)
    {
        return $query->where('academic_term_id', $termId);
    }

    /**
     * Get the calculated percentage attribute
     * This is an alias for the percentage column for backward compatibility
     */
    public function getPercentageCalculatedAttribute()
    {
        return $this->percentage;
    }
}
