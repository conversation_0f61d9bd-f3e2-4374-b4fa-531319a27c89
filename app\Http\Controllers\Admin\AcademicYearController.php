<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AcademicYear;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AcademicYearController extends Controller
{
    public function index()
    {
        $academicYears = AcademicYear::withCount('academicTerms')
                                   ->orderBy('start_date', 'desc')
                                   ->paginate(10);

        return view('admin.academic-years.index', compact('academicYears'));
    }

    public function create()
    {
        return view('admin.academic-years.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:academic_years',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'is_current' => 'boolean',
        ]);

        DB::transaction(function () use ($request) {
            if ($request->is_current) {
                AcademicYear::where('is_current', true)->update(['is_current' => false]);
            }

            AcademicYear::create($request->all());
        });

        return redirect()->route('admin.academic-years.index')
                        ->with('success', 'Academic year created successfully.');
    }

    public function show(AcademicYear $academicYear)
    {
        $academicYear->load(['academicTerms' => function ($query) {
            $query->orderBy('sort_order');
        }]);

        return view('admin.academic-years.show', compact('academicYear'));
    }

    public function edit(AcademicYear $academicYear)
    {
        return view('admin.academic-years.edit', compact('academicYear'));
    }

    public function update(Request $request, AcademicYear $academicYear)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:academic_years,name,' . $academicYear->id,
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'is_current' => 'boolean',
        ]);

        DB::transaction(function () use ($request, $academicYear) {
            if ($request->is_current && !$academicYear->is_current) {
                AcademicYear::where('is_current', true)->update(['is_current' => false]);
            }

            $academicYear->update($request->all());
        });

        return redirect()->route('admin.academic-years.index')
                        ->with('success', 'Academic year updated successfully.');
    }

    public function destroy(AcademicYear $academicYear)
    {
        if ($academicYear->is_current) {
            return redirect()->route('admin.academic-years.index')
                           ->with('error', 'Cannot delete the current academic year.');
        }

        $academicYear->delete();

        return redirect()->route('admin.academic-years.index')
                        ->with('success', 'Academic year deleted successfully.');
    }

    public function setCurrent(AcademicYear $academicYear)
    {
        DB::transaction(function () use ($academicYear) {
            AcademicYear::where('is_current', true)->update(['is_current' => false]);
            $academicYear->update(['is_current' => true]);
        });

        return redirect()->route('admin.academic-years.index')
                        ->with('success', 'Academic year set as current successfully.');
    }
}
