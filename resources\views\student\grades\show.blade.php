@extends('layouts.app')

@section('title', 'Grade Details')

@section('content')
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Grade Details</h1>
                <p class="text-gray-600">View assignment grade information</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('student.grades.index') }}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                    Back to Grades
                </a>
                <a href="{{ route('student.dashboard') }}" 
                   class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                    Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Grade Information Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">{{ $grade->assignment_name }}</h2>
                <p class="text-sm text-gray-600">{{ $grade->description ?? 'No description provided' }}</p>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Subject Information -->
                    <div class="space-y-4">
                        <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Subject Information</h3>
                        <div class="space-y-2">
                            <div>
                                <span class="text-sm text-gray-500">Subject:</span>
                                <p class="font-medium">{{ $grade->subject->name }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Teacher:</span>
                                <p class="font-medium">{{ $grade->teacher->user->name }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Category:</span>
                                <p class="font-medium">{{ $grade->gradeCategory->name }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Term:</span>
                                <p class="font-medium">{{ $grade->academicTerm->name }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Grade Information -->
                    <div class="space-y-4">
                        <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Grade Information</h3>
                        <div class="space-y-2">
                            <div>
                                <span class="text-sm text-gray-500">Score:</span>
                                <p class="font-medium text-lg">{{ $grade->score }} / {{ $grade->max_score }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Percentage:</span>
                                <p class="font-medium text-lg">{{ number_format($grade->percentage, 2) }}%</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Letter Grade:</span>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    @if($grade->letter_grade === 'A' || $grade->letter_grade === 'A+') bg-green-100 text-green-800
                                    @elseif($grade->letter_grade === 'B' || $grade->letter_grade === 'B+') bg-blue-100 text-blue-800
                                    @elseif($grade->letter_grade === 'C' || $grade->letter_grade === 'C+') bg-yellow-100 text-yellow-800
                                    @elseif($grade->letter_grade === 'D' || $grade->letter_grade === 'D+') bg-orange-100 text-orange-800
                                    @else bg-red-100 text-red-800
                                    @endif">
                                    {{ $grade->letter_grade }}
                                </span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">GPA Points:</span>
                                <p class="font-medium">{{ number_format($grade->gpa_points, 2) }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Assignment Details -->
                    <div class="space-y-4">
                        <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Assignment Details</h3>
                        <div class="space-y-2">
                            <div>
                                <span class="text-sm text-gray-500">Assignment Date:</span>
                                <p class="font-medium">{{ $grade->assignment_date ? $grade->assignment_date->format('M d, Y') : 'Not set' }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Due Date:</span>
                                <p class="font-medium">{{ $grade->due_date ? $grade->due_date->format('M d, Y') : 'Not set' }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Submitted:</span>
                                <p class="font-medium">{{ $grade->created_at->format('M d, Y') }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Indicator -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-4">Performance</h3>
                    <div class="w-full bg-gray-200 rounded-full h-4">
                        <div class="h-4 rounded-full transition-all duration-300
                            @if($grade->percentage >= 90) bg-green-500
                            @elseif($grade->percentage >= 80) bg-blue-500
                            @elseif($grade->percentage >= 70) bg-yellow-500
                            @elseif($grade->percentage >= 60) bg-orange-500
                            @else bg-red-500
                            @endif" 
                            style="width: {{ min($grade->percentage, 100) }}%">
                        </div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-500 mt-2">
                        <span>0%</span>
                        <span class="font-medium">{{ number_format($grade->percentage, 1) }}%</span>
                        <span>100%</span>
                    </div>
                </div>

                <!-- Comments -->
                @if($grade->comments)
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-2">Teacher Comments</h3>
                    <div class="bg-blue-50 rounded-lg p-4">
                        <p class="text-gray-700">{{ $grade->comments }}</p>
                    </div>
                </div>
                @endif

                <!-- Grade Context -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-4">Grade Context</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-gray-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-gray-900">{{ $grade->gradeCategory->weight }}%</div>
                            <div class="text-sm text-gray-500">Category Weight</div>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-gray-900">{{ number_format($grade->gpa_points, 1) }}</div>
                            <div class="text-sm text-gray-500">GPA Points</div>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold 
                                @if($grade->percentage >= 90) text-green-600
                                @elseif($grade->percentage >= 80) text-blue-600
                                @elseif($grade->percentage >= 70) text-yellow-600
                                @elseif($grade->percentage >= 60) text-orange-600
                                @else text-red-600
                                @endif">
                                @if($grade->percentage >= 90) Excellent
                                @elseif($grade->percentage >= 80) Good
                                @elseif($grade->percentage >= 70) Satisfactory
                                @elseif($grade->percentage >= 60) Needs Improvement
                                @else Unsatisfactory
                                @endif
                            </div>
                            <div class="text-sm text-gray-500">Performance Level</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
