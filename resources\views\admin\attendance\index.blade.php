@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Attendance Management</h1>
                <p class="text-gray-600">Track and manage student and teacher attendance • <span class="text-sm text-gray-500">{{ $today->format('l, F j, Y') }}</span></p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('admin.attendance.reports') }}" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Reports
                </a>
                <a href="{{ route('admin.attendance.students') }}" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                    </svg>
                    Student Attendance
                </a>
                <a href="{{ route('admin.attendance.teachers') }}" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    Teacher Attendance
                </a>
            </div>
        </div>
    </div>

    <!-- Today's Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Student Present -->
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Students Present</dt>
                            <dd class="stat-card-value">{{ $studentStats['present_today'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Student Absent -->
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-red-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Students Absent</dt>
                            <dd class="stat-card-value">{{ $studentStats['absent_today'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Teacher Present -->
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Teachers Present</dt>
                            <dd class="stat-card-value">{{ $teacherStats['present_today'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Teacher Absent -->
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-orange-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Teachers Absent</dt>
                            <dd class="stat-card-value">{{ $teacherStats['absent_today'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Student Attendance Activity -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900">Student Attendance Activity</h3>
                </div>
            </div>
            <div class="divide-y divide-gray-200">
                @php
                    $studentActivities = $recentActivities->where('type', 'student')->take(5);
                @endphp
                @forelse($studentActivities as $activity)
                    <div class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $activity['name'] }}</p>
                                <p class="text-sm text-gray-500">
                                    Marked as
                                    <span class="badge badge-{{ $activity['status'] === 'present' ? 'green' : ($activity['status'] === 'absent' ? 'red' : 'yellow') }}">
                                        {{ ucfirst($activity['status']) }}
                                    </span>
                                    by {{ $activity['marked_by'] }}
                                </p>
                            </div>
                            <div class="text-sm text-gray-500">
                                {{ $activity['time']->format('M j, g:i A') }}
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="px-6 py-8 text-center">
                        <p class="text-sm text-gray-500">No recent student attendance activity</p>
                    </div>
                @endforelse
                @if($studentActivities->count() > 0)
                    <div class="px-6 py-3 bg-gray-50 text-center">
                        <a href="{{ route('admin.attendance.students') }}" class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                            View All Student Attendance →
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Teacher Attendance Activity -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900">Teacher Attendance Activity</h3>
                </div>
            </div>
            <div class="divide-y divide-gray-200">
                @php
                    $teacherActivities = $recentActivities->where('type', 'teacher')->take(5);
                @endphp
                @forelse($teacherActivities as $activity)
                    <div class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $activity['name'] }}</p>
                                <p class="text-sm text-gray-500">
                                    Marked as
                                    <span class="badge badge-{{ $activity['status'] === 'present' ? 'green' : ($activity['status'] === 'absent' ? 'red' : 'yellow') }}">
                                        {{ ucfirst($activity['status']) }}
                                    </span>
                                    by {{ $activity['marked_by'] }}
                                </p>
                            </div>
                            <div class="text-sm text-gray-500">
                                {{ $activity['time']->format('M j, g:i A') }}
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="px-6 py-8 text-center">
                        <p class="text-sm text-gray-500">No recent teacher attendance activity</p>
                    </div>
                @endforelse
                @if($teacherActivities->count() > 0)
                    <div class="px-6 py-3 bg-gray-50 text-center">
                        <a href="{{ route('admin.attendance.teachers') }}" class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                            View All Teacher Attendance →
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
