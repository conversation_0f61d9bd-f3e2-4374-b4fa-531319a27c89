<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('schedules', function (Blueprint $table) {
            $table->text('notes')->nullable()->after('room_number');
            $table->boolean('is_substitution')->default(false)->after('notes');
            $table->foreignId('original_schedule_id')->nullable()->constrained('schedules')->onDelete('set null')->after('is_substitution');
            
            // Add indexes for better performance
            $table->index(['teacher_id', 'day_of_week', 'time_slot_id']);
            $table->index(['room_number', 'day_of_week', 'time_slot_id']);
            $table->index(['is_substitution', 'original_schedule_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('schedules', function (Blueprint $table) {
            $table->dropIndex(['teacher_id', 'day_of_week', 'time_slot_id']);
            $table->dropIndex(['room_number', 'day_of_week', 'time_slot_id']);
            $table->dropIndex(['is_substitution', 'original_schedule_id']);
            
            $table->dropForeign(['original_schedule_id']);
            $table->dropColumn(['notes', 'is_substitution', 'original_schedule_id']);
        });
    }
};
