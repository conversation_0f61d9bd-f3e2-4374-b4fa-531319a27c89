<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Guardian extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'relationship',
        'occupation',
        'workplace',
        'emergency_contact',
        'monthly_income',
    ];

    protected $casts = [
        'monthly_income' => 'decimal:2',
    ];

    /**
     * Get the user that owns the guardian profile
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the students under this guardian
     */
    public function students()
    {
        return $this->belongsToMany(Student::class, 'guardian_student');
    }

    /**
     * Get the invoices for this guardian's children
     */
    public function invoices()
    {
        return $this->hasManyThrough(Invoice::class, Student::class, 'id', 'student_id', 'id', 'id')
                    ->whereIn('students.id', $this->students()->pluck('students.id'));
    }

    /**
     * Get the payments made by this guardian
     */
    public function payments()
    {
        return $this->hasMany(Payment::class, 'paid_by', 'user_id');
    }

    /**
     * Get the full name of the guardian
     */
    public function getFullNameAttribute()
    {
        return $this->user->name;
    }

    /**
     * Get the guardian's children count
     */
    public function getChildrenCountAttribute()
    {
        return $this->students()->count();
    }

    /**
     * Get the guardian's total amount paid
     */
    public function getTotalAmountPaidAttribute()
    {
        return $this->payments()->where('status', 'paid')->sum('amount');
    }

    /**
     * Get the guardian's total amount due
     */
    public function getTotalAmountDueAttribute()
    {
        $studentIds = $this->students->pluck('id');
        return Invoice::whereIn('student_id', $studentIds)
                     ->whereIn('status', ['sent', 'overdue'])
                     ->sum('total_amount');
    }

    /**
     * Check if guardian is active
     */
    public function getIsActiveAttribute()
    {
        return $this->user->is_active;
    }

    /**
     * Get formatted monthly income
     */
    public function getFormattedIncomeAttribute()
    {
        return $this->monthly_income ? 'RM ' . number_format($this->monthly_income, 2) : 'Not specified';
    }

    /**
     * Scope to filter active guardians
     */
    public function scopeActive($query)
    {
        return $query->whereHas('user', function ($q) {
            $q->where('is_active', true);
        });
    }

    /**
     * Scope to filter by relationship
     */
    public function scopeByRelationship($query, $relationship)
    {
        return $query->where('relationship', $relationship);
    }

    /**
     * Scope to filter by occupation
     */
    public function scopeByOccupation($query, $occupation)
    {
        return $query->where('occupation', 'like', "%{$occupation}%");
    }

    /**
     * Scope to search guardians
     */
    public function scopeSearch($query, $search)
    {
        return $query->whereHas('user', function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%");
        })->orWhere('relationship', 'like', "%{$search}%")
          ->orWhere('occupation', 'like', "%{$search}%")
          ->orWhere('workplace', 'like', "%{$search}%");
    }

    /**
     * Scope to filter guardians with children
     */
    public function scopeWithChildren($query)
    {
        return $query->whereHas('students');
    }

    /**
     * Scope to filter by income range
     */
    public function scopeByIncomeRange($query, $min = null, $max = null)
    {
        if ($min !== null) {
            $query->where('monthly_income', '>=', $min);
        }
        if ($max !== null) {
            $query->where('monthly_income', '<=', $max);
        }
        return $query;
    }
}
