<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Invoice;
use App\Services\BillplzService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    protected $billplzService;

    public function __construct(BillplzService $billplzService)
    {
        $this->billplzService = $billplzService;
    }

    /**
     * Display a listing of payments
     */
    public function index(Request $request)
    {
        $query = Payment::with(['invoice.student.user', 'payer']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by payment reference, invoice number, or other fields
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('payment_reference', 'like', "%{$search}%")
                  ->orWhere('billplz_bill_id', 'like', "%{$search}%")
                  ->orWhere('amount', 'like', "%{$search}%")
                  ->orWhere('status', 'like', "%{$search}%")
                  ->orWhere('payment_method', 'like', "%{$search}%")
                  ->orWhereHas('invoice', function ($invoiceQuery) use ($search) {
                      $invoiceQuery->where('invoice_number', 'like', "%{$search}%")
                                   ->orWhere('title', 'like', "%{$search}%");
                  })
                  ->orWhereHas('payer', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%")
                                ->orWhere('phone', 'like', "%{$search}%");
                  })
                  ->orWhereHas('invoice.student.user', function ($studentQuery) use ($search) {
                      $studentQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $payments = $query->latest()->paginate(15);

        $stats = [
            'total' => Payment::count(),
            'pending' => Payment::where('status', 'pending')->count(),
            'paid' => Payment::where('status', 'paid')->count(),
            'failed' => Payment::where('status', 'failed')->count(),
            'refunded' => Payment::where('status', 'refunded')->count(),
            'total_amount' => Payment::where('status', 'paid')->sum('amount'),
            'pending_amount' => Payment::where('status', 'pending')->sum('amount'),
        ];

        return view('admin.payments.index', compact('payments', 'stats'));
    }

    /**
     * Display the specified payment
     */
    public function show(Payment $payment)
    {
        $payment->load(['invoice.student.user', 'payer']);

        // Get Billplz bill information if available
        $billplzInfo = null;
        if ($payment->billplz_bill_id) {
            $billplzInfo = $this->billplzService->getBill($payment->billplz_bill_id);
        }

        return view('admin.payments.show', compact('payment', 'billplzInfo'));
    }

    /**
     * Process refund for a payment
     */
    public function refund(Request $request, Payment $payment)
    {
        if ($payment->status !== 'paid') {
            return redirect()->route('admin.payments.show', $payment)
                            ->with('error', 'Only paid payments can be refunded.');
        }

        $request->validate([
            'refund_reason' => 'required|string|max:255',
        ]);

        try {
            // Update payment status
            $payment->update([
                'status' => 'refunded',
                'billplz_response' => array_merge(
                    $payment->billplz_response ?? [],
                    [
                        'refund_reason' => $request->refund_reason,
                        'refunded_at' => now(),
                        'refunded_by' => auth()->id(),
                    ]
                ),
            ]);

            // Update invoice status back to sent if this was the only payment
            $invoice = $payment->invoice;
            $remainingPayments = $invoice->payments()
                                        ->where('status', 'paid')
                                        ->where('id', '!=', $payment->id)
                                        ->sum('amount');

            if ($remainingPayments == 0) {
                $invoice->update(['status' => 'sent']);
            }

            Log::info('Payment refunded', [
                'payment_id' => $payment->id,
                'invoice_id' => $invoice->id,
                'amount' => $payment->amount,
                'reason' => $request->refund_reason,
                'refunded_by' => auth()->id(),
            ]);

            return redirect()->route('admin.payments.show', $payment)
                            ->with('success', 'Payment refunded successfully.');

        } catch (\Exception $e) {
            Log::error('Payment refund failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('admin.payments.show', $payment)
                            ->with('error', 'Failed to process refund. Please try again.');
        }
    }

    /**
     * Export payments to CSV
     */
    public function export(Request $request)
    {
        $query = Payment::with(['invoice.student.user', 'payer']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $payments = $query->get();

        $filename = 'payments_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($payments) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Payment ID',
                'Invoice Number',
                'Student Name',
                'Payer Name',
                'Amount',
                'Status',
                'Payment Method',
                'Payment Reference',
                'Billplz Bill ID',
                'Paid At',
                'Created At',
            ]);

            // CSV data
            foreach ($payments as $payment) {
                fputcsv($file, [
                    $payment->id,
                    $payment->invoice->invoice_number,
                    $payment->invoice->student->user->name,
                    $payment->payer->name,
                    $payment->amount,
                    $payment->status,
                    $payment->payment_method,
                    $payment->payment_reference,
                    $payment->billplz_bill_id,
                    $payment->paid_at ? $payment->paid_at->format('Y-m-d H:i:s') : '',
                    $payment->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get payment statistics for dashboard
     */
    public function getStats()
    {
        $today = now()->startOfDay();
        $thisMonth = now()->startOfMonth();
        $thisYear = now()->startOfYear();

        return [
            'today' => [
                'count' => Payment::where('status', 'paid')->whereDate('paid_at', $today)->count(),
                'amount' => Payment::where('status', 'paid')->whereDate('paid_at', $today)->sum('amount'),
            ],
            'this_month' => [
                'count' => Payment::where('status', 'paid')->where('paid_at', '>=', $thisMonth)->count(),
                'amount' => Payment::where('status', 'paid')->where('paid_at', '>=', $thisMonth)->sum('amount'),
            ],
            'this_year' => [
                'count' => Payment::where('status', 'paid')->where('paid_at', '>=', $thisYear)->count(),
                'amount' => Payment::where('status', 'paid')->where('paid_at', '>=', $thisYear)->sum('amount'),
            ],
            'pending' => [
                'count' => Payment::where('status', 'pending')->count(),
                'amount' => Payment::where('status', 'pending')->sum('amount'),
            ],
        ];
    }
}
