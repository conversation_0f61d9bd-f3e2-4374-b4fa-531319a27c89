@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Create Academic Term"
        description="Add a new academic term or semester"
        :back-route="route('admin.academic-terms.index')"
        back-label="Back to Academic Terms">
    </x-page-header>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Academic Term Information</h3>
        </div>
        <form method="POST" action="{{ route('admin.academic-terms.store') }}" class="p-6">
            @csrf
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Academic Year -->
                <div>
                    <label for="academic_year_id" class="block text-sm font-medium text-gray-700">Academic Year *</label>
                    <select id="academic_year_id" name="academic_year_id" class="form-select mt-1" required>
                        <option value="">Select Academic Year</option>
                        @foreach($academicYears as $year)
                            <option value="{{ $year->id }}" {{ old('academic_year_id', request('academic_year_id')) == $year->id ? 'selected' : '' }}>
                                {{ $year->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('academic_year_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">Term Name *</label>
                    <input type="text" id="name" name="name" value="{{ old('name') }}" 
                           class="form-input mt-1" placeholder="e.g., First Semester, Term 1" required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Start Date -->
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date *</label>
                    <input type="date" id="start_date" name="start_date" value="{{ old('start_date') }}" 
                           class="form-input mt-1" required>
                    @error('start_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- End Date -->
                <div>
                    <label for="end_date" class="block text-sm font-medium text-gray-700">End Date *</label>
                    <input type="date" id="end_date" name="end_date" value="{{ old('end_date') }}" 
                           class="form-input mt-1" required>
                    @error('end_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Sort Order -->
                <div>
                    <label for="sort_order" class="block text-sm font-medium text-gray-700">Sort Order *</label>
                    <input type="number" id="sort_order" name="sort_order" value="{{ old('sort_order', 1) }}" 
                           class="form-input mt-1" min="0" required>
                    <p class="mt-1 text-sm text-gray-500">Lower numbers appear first</p>
                    @error('sort_order')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status Options -->
                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="is_active" name="is_active" value="1" 
                               {{ old('is_active', true) ? 'checked' : '' }} class="form-checkbox">
                        <label for="is_active" class="ml-2 block text-sm text-gray-700">
                            Active
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="is_current" name="is_current" value="1" 
                               {{ old('is_current') ? 'checked' : '' }} class="form-checkbox">
                        <label for="is_current" class="ml-2 block text-sm text-gray-700">
                            Set as Current Term
                        </label>
                    </div>
                    <p class="text-sm text-gray-500">Setting as current will unset any other current term</p>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-8 flex items-center justify-end space-x-4">
                <a href="{{ route('admin.academic-terms.index') }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Academic Term
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Validate end date is after start date
document.getElementById('start_date').addEventListener('change', function() {
    const startDate = this.value;
    const endDateInput = document.getElementById('end_date');
    
    if (startDate) {
        endDateInput.min = startDate;
        
        // If end date is before start date, clear it
        if (endDateInput.value && endDateInput.value <= startDate) {
            endDateInput.value = '';
        }
    }
});

document.getElementById('end_date').addEventListener('change', function() {
    const endDate = this.value;
    const startDate = document.getElementById('start_date').value;
    
    if (startDate && endDate && endDate <= startDate) {
        alert('End date must be after start date');
        this.value = '';
    }
});
</script>
@endpush
