<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExamResult extends Model
{
    use HasFactory;

    protected $fillable = [
        'exam_subject_id',
        'student_id',
        'marks_obtained',
        'grade',
        'gpa_points',
        'status',
        'remarks',
        'entered_by',
        'entered_at',
    ];

    protected $casts = [
        'marks_obtained' => 'decimal:2',
        'percentage' => 'decimal:2',
        'gpa_points' => 'decimal:2',
        'entered_at' => 'datetime',
    ];

    /**
     * Get the exam subject.
     */
    public function examSubject(): BelongsTo
    {
        return $this->belongsTo(ExamSubject::class);
    }

    /**
     * Get the student.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the user who entered the result.
     */
    public function enteredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'entered_by');
    }

    /**
     * Calculate grade based on percentage.
     */
    public function calculateGrade(): string
    {
        $percentage = $this->percentage;

        if ($percentage >= 90) return 'A+';
        if ($percentage >= 80) return 'A';
        if ($percentage >= 70) return 'B+';
        if ($percentage >= 60) return 'B';
        if ($percentage >= 50) return 'C+';
        if ($percentage >= 40) return 'C';
        if ($percentage >= 30) return 'D';
        return 'F';
    }

    /**
     * Calculate GPA points based on percentage.
     */
    public function calculateGpaPoints(): float
    {
        $percentage = $this->percentage;

        if ($percentage >= 90) return 4.0;
        if ($percentage >= 80) return 3.7;
        if ($percentage >= 70) return 3.3;
        if ($percentage >= 60) return 3.0;
        if ($percentage >= 50) return 2.7;
        if ($percentage >= 40) return 2.3;
        if ($percentage >= 30) return 2.0;
        return 0.0;
    }

    /**
     * Check if student passed.
     */
    public function isPassed(): bool
    {
        return $this->marks_obtained >= $this->examSubject->pass_marks;
    }

    /**
     * Get results for a specific student and exam.
     */
    public static function forStudentExam($studentId, $examId)
    {
        return static::whereHas('examSubject', function ($query) use ($examId) {
                        $query->where('exam_id', $examId);
                    })
                    ->where('student_id', $studentId)
                    ->with(['examSubject.subject']);
    }
}
