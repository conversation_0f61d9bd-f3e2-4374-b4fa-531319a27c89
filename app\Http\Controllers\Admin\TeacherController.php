<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Teacher;
use App\Models\User;
use App\Models\Student;
use App\Models\Invoice;
use App\Models\Grade;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class TeacherController extends Controller
{
    /**
     * Display a listing of teachers
     */
    public function index(Request $request)
    {
        $query = Teacher::with(['user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                              ->orWhere('email', 'like', "%{$search}%")
                              ->orWhere('phone', 'like', "%{$search}%")
                              ->orWhere('address', 'like', "%{$search}%");
                })->orWhere('employee_id', 'like', "%{$search}%")
                  ->orWhere('specialization', 'like', "%{$search}%")
                  ->orWhere('qualification', 'like', "%{$search}%")
                  ->orWhere('department', 'like', "%{$search}%")
                  ->orWhere('experience_years', 'like', "%{$search}%")
                  ->orWhere('salary', 'like', "%{$search}%");
            });
        }

        // Filter by specialization
        if ($request->filled('specialization')) {
            $query->where('specialization', 'like', "%{$request->specialization}%");
        }

        // Filter by qualification
        if ($request->filled('qualification')) {
            $query->where('qualification', 'like', "%{$request->qualification}%");
        }

        // Filter by status
        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->whereHas('user', function ($q) use ($isActive) {
                $q->where('is_active', $isActive);
            });
        }

        // Filter by hire date range
        if ($request->filled('date_from')) {
            $query->whereDate('hire_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('hire_date', '<=', $request->date_to);
        }

        $teachers = $query->latest()->paginate(20);

        // Get filter options
        $specializations = Teacher::distinct()->pluck('specialization')->filter()->sort();
        $qualifications = Teacher::distinct()->pluck('qualification')->filter()->sort();

        $stats = [
            'total' => Teacher::count(),
            'active' => Teacher::whereHas('user', function ($q) {
                $q->where('is_active', true);
            })->count(),
            'inactive' => Teacher::whereHas('user', function ($q) {
                $q->where('is_active', false);
            })->count(),
            'new_this_month' => Teacher::whereMonth('hire_date', now()->month)
                                     ->whereYear('hire_date', now()->year)
                                     ->count(),
        ];

        return view('admin.teachers.index', compact('teachers', 'specializations', 'qualifications', 'stats'));
    }

    /**
     * Show the form for creating a new teacher
     */
    public function create()
    {
        $specializations = Teacher::distinct()->pluck('specialization')->filter()->sort();
        $qualifications = Teacher::distinct()->pluck('qualification')->filter()->sort();
        $classes = Student::distinct()->pluck('class')->filter()->sort();
        
        return view('admin.teachers.create', compact('specializations', 'qualifications', 'classes'));
    }

    /**
     * Store a newly created teacher
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'employee_id' => 'required|string|unique:teachers',
            'qualification' => 'nullable|string|max:255',
            'specialization' => 'nullable|string|max:255',
            'hire_date' => 'nullable|date',
            'salary' => 'nullable|numeric|min:0',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'subjects' => 'nullable|array',
            'subjects.*' => 'string|max:100',
            'classes' => 'nullable|array',
            'classes.*' => 'string|max:100',
        ]);

        try {
            DB::beginTransaction();

            // Create user account
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => 'teacher',
                'is_active' => true,
                'address' => $request->address,
                'phone' => $request->phone,
            ]);

            // Create teacher profile
            $teacher = Teacher::create([
                'user_id' => $user->id,
                'employee_id' => $request->employee_id,
                'qualification' => $request->qualification,
                'specialization' => $request->specialization,
                'hire_date' => $request->hire_date,
                'salary' => $request->salary,
                'subjects' => $request->subjects ?? [],
                'classes' => $request->classes ?? [],
            ]);

            DB::commit();

            return redirect()->route('admin.teachers.index')
                           ->with('success', 'Teacher created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to create teacher. Please try again.');
        }
    }

    /**
     * Display the specified teacher
     */
    public function show(Teacher $teacher)
    {
        $teacher->load([
            'user',
            'teachingAssignments.subject',
            'teachingAssignments.class',
            'teachingAssignments.section',
            'assignedSubjects',
            'assignedClasses',
            'grades' => function ($query) {
                $query->with(['student.user', 'subject', 'gradeCategory', 'academicTerm'])
                      ->where('is_published', true)
                      ->latest('assignment_date')
                      ->take(10);
            }
        ]);

        // Get teacher's invoice statistics
        $invoiceStats = [
            'total_invoices' => Invoice::where('created_by', $teacher->user_id)->count(),
            'pending_invoices' => Invoice::where('created_by', $teacher->user_id)->where('status', 'sent')->count(),
            'paid_invoices' => Invoice::where('created_by', $teacher->user_id)->where('status', 'paid')->count(),
            'draft_invoices' => Invoice::where('created_by', $teacher->user_id)->where('status', 'draft')->count(),
            'total_amount_invoiced' => Invoice::where('created_by', $teacher->user_id)->sum('total_amount'),
        ];

        // Get recent invoices created by this teacher
        $recentInvoices = Invoice::with(['student.user'])
                                ->where('created_by', $teacher->user_id)
                                ->latest()
                                ->take(10)
                                ->get();

        // Get students assigned to this teacher through their teaching assignments
        $assignedStudents = $teacher->students()->with('user')->take(10)->get();

        // Get teacher's attendance records
        $attendanceRecords = \App\Models\TeacherAttendance::with('markedBy')
                                ->where('teacher_id', $teacher->id)
                                ->orderBy('date', 'desc')
                                ->take(15)
                                ->get();

        // Get teacher's attendance statistics for current month
        $attendanceStats = \App\Models\TeacherAttendance::getTeacherStats(
            $teacher->id,
            \Carbon\Carbon::now()->startOfMonth()->format('Y-m-d'),
            \Carbon\Carbon::now()->format('Y-m-d')
        );

        // Get teacher's grade statistics
        $gradeStats = [
            'total_grades' => $teacher->grades()->count(),
            'published_grades' => $teacher->grades()->where('is_published', true)->count(),
            'draft_grades' => $teacher->grades()->where('is_published', false)->count(),
            'average_score' => $teacher->grades()->where('is_published', true)->avg('percentage') ?? 0,
            'students_graded' => $teacher->grades()->distinct('student_id')->count('student_id'),
            'grades_this_month' => $teacher->grades()
                                          ->whereMonth('assignment_date', now()->month)
                                          ->whereYear('assignment_date', now()->year)
                                          ->count(),
        ];

        return view('admin.teachers.show', compact(
            'teacher',
            'invoiceStats',
            'recentInvoices',
            'assignedStudents',
            'attendanceRecords',
            'attendanceStats',
            'gradeStats'
        ));
    }

    /**
     * Show the form for editing the specified teacher
     */
    public function edit(Teacher $teacher)
    {
        $teacher->load(['user']);
        $specializations = Teacher::distinct()->pluck('specialization')->filter()->sort();
        $qualifications = Teacher::distinct()->pluck('qualification')->filter()->sort();
        $classes = Student::distinct()->pluck('class')->filter()->sort();
        
        return view('admin.teachers.edit', compact('teacher', 'specializations', 'qualifications', 'classes'));
    }

    /**
     * Update the specified teacher
     */
    public function update(Request $request, Teacher $teacher)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($teacher->user_id)],
            'password' => 'nullable|string|min:8|confirmed',
            'employee_id' => ['required', 'string', Rule::unique('teachers')->ignore($teacher->id)],
            'qualification' => 'nullable|string|max:255',
            'specialization' => 'nullable|string|max:255',
            'hire_date' => 'nullable|date',
            'salary' => 'nullable|numeric|min:0',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'is_active' => 'boolean',
            'subjects' => 'nullable|array',
            'subjects.*' => 'string|max:100',
            'classes' => 'nullable|array',
            'classes.*' => 'string|max:100',
        ]);

        try {
            DB::beginTransaction();

            // Update user account
            $userData = [
                'name' => $request->name,
                'email' => $request->email,
                'is_active' => $request->boolean('is_active', true),
                'address' => $request->address,
                'phone' => $request->phone,
            ];

            if ($request->filled('password')) {
                $userData['password'] = Hash::make($request->password);
            }

            $teacher->user()->update($userData);

            // Update teacher profile
            $teacher->update([
                'employee_id' => $request->employee_id,
                'qualification' => $request->qualification,
                'specialization' => $request->specialization,
                'hire_date' => $request->hire_date,
                'salary' => $request->salary,
                'subjects' => $request->subjects ?? [],
                'classes' => $request->classes ?? [],
            ]);

            DB::commit();

            return redirect()->route('admin.teachers.show', $teacher)
                           ->with('success', 'Teacher updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to update teacher. Please try again.');
        }
    }

    /**
     * Remove the specified teacher
     */
    public function destroy(Teacher $teacher)
    {
        try {
            DB::beginTransaction();

            // Check if teacher has any invoices
            if (Invoice::where('created_by', $teacher->user_id)->exists()) {
                return back()->with('error', 'Cannot delete teacher with existing invoices.');
            }

            // Delete teacher and user
            $user = $teacher->user;
            $teacher->delete();
            $user->delete();

            DB::commit();

            return redirect()->route('admin.teachers.index')
                           ->with('success', 'Teacher deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to delete teacher. Please try again.');
        }
    }

    /**
     * Toggle teacher status (active/inactive)
     */
    public function toggleStatus(Teacher $teacher)
    {
        try {
            $teacher->user()->update([
                'is_active' => !$teacher->user->is_active
            ]);

            $status = $teacher->user->is_active ? 'activated' : 'deactivated';
            
            return redirect()->route('admin.teachers.show', $teacher)
                           ->with('success', "Teacher {$status} successfully.");

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update teacher status. Please try again.');
        }
    }
}
