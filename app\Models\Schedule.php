<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;

class Schedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'academic_year_id',
        'academic_term_id',
        'class_id',
        'section_id',
        'subject_id',
        'teacher_id',
        'time_slot_id',
        'day_of_week',
        'room_number',
        'is_active',
        'notes',
        'is_substitution',
        'original_schedule_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_substitution' => 'boolean',
    ];

    /**
     * Get the academic year.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the academic term.
     */
    public function academicTerm(): BelongsTo
    {
        return $this->belongsTo(AcademicTerm::class);
    }

    /**
     * Get the class.
     */
    public function class(): BelongsTo
    {
        return $this->belongsTo(ClassModel::class, 'class_id');
    }

    /**
     * Get the school class (alias for class to avoid PHP keyword conflicts).
     */
    public function schoolClass(): BelongsTo
    {
        return $this->belongsTo(ClassModel::class, 'class_id');
    }

    /**
     * Get the section.
     */
    public function section(): BelongsTo
    {
        return $this->belongsTo(Section::class);
    }

    /**
     * Get the subject.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the teacher.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    /**
     * Get the time slot.
     */
    public function timeSlot(): BelongsTo
    {
        return $this->belongsTo(TimeSlot::class);
    }

    /**
     * Get schedules for a specific class and section.
     */
    public static function forClassSection($classId, $sectionId, $termId = null)
    {
        $query = static::where('class_id', $classId)
                      ->where('section_id', $sectionId)
                      ->where('is_active', true)
                      ->with(['subject', 'teacher', 'timeSlot']);

        if ($termId) {
            $query->where('academic_term_id', $termId);
        } else {
            $currentTerm = AcademicTerm::current();
            if ($currentTerm) {
                $query->where('academic_term_id', $currentTerm->id);
            }
        }

        return $query->orderBy('day_of_week')->orderBy('time_slot_id');
    }

    /**
     * Get schedules for a specific teacher.
     */
    public static function forTeacher($teacherId, $termId = null)
    {
        $query = static::where('teacher_id', $teacherId)
                      ->where('is_active', true)
                      ->with(['class', 'section', 'subject', 'timeSlot']);

        if ($termId) {
            $query->where('academic_term_id', $termId);
        } else {
            $currentTerm = AcademicTerm::current();
            if ($currentTerm) {
                $query->where('academic_term_id', $currentTerm->id);
            }
        }

        return $query->orderBy('day_of_week')->orderBy('time_slot_id');
    }

    /**
     * Get formatted class name.
     */
    public function getClassNameAttribute(): string
    {
        return $this->class->name . ' - ' . $this->section->name;
    }

    /**
     * Get day name.
     */
    public function getDayNameAttribute(): string
    {
        return ucfirst($this->day_of_week);
    }

    /**
     * Get the original schedule if this is a substitution.
     */
    public function originalSchedule(): BelongsTo
    {
        return $this->belongsTo(Schedule::class, 'original_schedule_id');
    }

    /**
     * Get substitution schedules for this schedule.
     */
    public function substitutions(): HasMany
    {
        return $this->hasMany(Schedule::class, 'original_schedule_id');
    }

    /**
     * Check for schedule conflicts.
     */
    public static function hasConflict($teacherId, $timeSlotId, $dayOfWeek, $termId, $excludeId = null)
    {
        $query = static::where('teacher_id', $teacherId)
                      ->where('time_slot_id', $timeSlotId)
                      ->where('day_of_week', $dayOfWeek)
                      ->where('academic_term_id', $termId)
                      ->where('is_active', true);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Check for room conflicts.
     */
    public static function hasRoomConflict($roomNumber, $timeSlotId, $dayOfWeek, $termId, $excludeId = null)
    {
        if (!$roomNumber) {
            return false;
        }

        $query = static::where('room_number', $roomNumber)
                      ->where('time_slot_id', $timeSlotId)
                      ->where('day_of_week', $dayOfWeek)
                      ->where('academic_term_id', $termId)
                      ->where('is_active', true);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Get teacher workload for a specific term.
     */
    public static function getTeacherWorkload($teacherId, $termId)
    {
        return static::where('teacher_id', $teacherId)
                    ->where('academic_term_id', $termId)
                    ->where('is_active', true)
                    ->count();
    }

    /**
     * Get available time slots for a teacher on a specific day.
     */
    public static function getAvailableTimeSlots($teacherId, $dayOfWeek, $termId)
    {
        $occupiedSlots = static::where('teacher_id', $teacherId)
                              ->where('day_of_week', $dayOfWeek)
                              ->where('academic_term_id', $termId)
                              ->where('is_active', true)
                              ->pluck('time_slot_id');

        return TimeSlot::classSlots()
                      ->whereNotIn('id', $occupiedSlots)
                      ->get();
    }

    /**
     * Bulk create schedules for a class.
     */
    public static function bulkCreateForClass($classId, $sectionId, $termId, $schedules)
    {
        DB::beginTransaction();
        try {
            foreach ($schedules as $scheduleData) {
                static::create([
                    'academic_year_id' => AcademicTerm::find($termId)->academic_year_id,
                    'academic_term_id' => $termId,
                    'class_id' => $classId,
                    'section_id' => $sectionId,
                    'subject_id' => $scheduleData['subject_id'],
                    'teacher_id' => $scheduleData['teacher_id'],
                    'time_slot_id' => $scheduleData['time_slot_id'],
                    'day_of_week' => $scheduleData['day_of_week'],
                    'room_number' => $scheduleData['room_number'] ?? null,
                    'is_active' => true,
                ]);
            }
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
}
