@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Student Portal</h1>
                <p class="text-gray-600">Welcome back, {{ auth()->user()->name }}!</p>
                @if($student)
                    <p class="text-sm text-gray-500">Student ID: {{ $student->student_id }} | Class: {{ $student->class }} - {{ $student->section }}</p>
                @endif
            </div>
            <div class="text-right">
                <p class="text-sm text-gray-500">{{ now()->format('l, F j, Y') }}</p>
                <p class="text-sm text-gray-500">{{ now()->format('g:i A') }}</p>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <span class="text-white font-bold text-sm">{{ $stats['pending_invoices'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending Bills</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['pending_invoices'] }} invoices</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <span class="text-white font-bold text-sm">{{ $stats['paid_invoices'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Paid Bills</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['paid_invoices'] }} completed</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <span class="text-white font-bold text-sm">{{ $stats['overdue_invoices'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Overdue Bills</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['overdue_invoices'] }} overdue</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <span class="text-white font-bold text-xs">RM</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Amount Due</dt>
                            <dd class="text-lg font-medium text-gray-900">RM {{ number_format($stats['total_amount_due'], 2) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Student Information -->
    @if($student)
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">My Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Personal Details</h4>
                    <dl class="space-y-2">
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">Full Name:</dt>
                            <dd class="text-sm text-gray-900">{{ $student->user->name }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">Student ID:</dt>
                            <dd class="text-sm text-gray-900">{{ $student->student_id }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">Date of Birth:</dt>
                            <dd class="text-sm text-gray-900">{{ $student->date_of_birth->format('M j, Y') }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">Gender:</dt>
                            <dd class="text-sm text-gray-900">{{ ucfirst($student->gender) }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">Blood Group:</dt>
                            <dd class="text-sm text-gray-900">{{ $student->blood_group ?? 'N/A' }}</dd>
                        </div>
                    </dl>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Academic Details</h4>
                    <dl class="space-y-2">
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">Class:</dt>
                            <dd class="text-sm text-gray-900">{{ $student->class }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">Section:</dt>
                            <dd class="text-sm text-gray-900">{{ $student->section }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">Roll Number:</dt>
                            <dd class="text-sm text-gray-900">{{ $student->roll_number }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">Admission Date:</dt>
                            <dd class="text-sm text-gray-900">{{ $student->admission_date->format('M j, Y') }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm text-gray-500">Emergency Contact:</dt>
                            <dd class="text-sm text-gray-900">{{ $student->emergency_contact ?? 'N/A' }}</dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Academic Information -->
    @if($student && $student->currentEnrollment)
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">My Subjects</h3>

        @if($student->activeSubjects && $student->activeSubjects->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($student->activeSubjects as $subject)
                    @php
                        $teachers = $student->getTeachersForSubject($subject->id);
                    @endphp
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-sm font-medium text-gray-900">{{ $subject->name }}</h4>
                            <span class="badge {{ $subject->pivot->is_mandatory ?? true ? 'badge-blue' : 'badge-green' }}">
                                {{ $subject->pivot->is_mandatory ?? true ? 'Mandatory' : 'Elective' }}
                            </span>
                        </div>
                        <p class="text-xs text-gray-500 mb-2">{{ $subject->subject_code }}</p>
                        <p class="text-xs text-gray-600 mb-3">{{ $subject->description }}</p>

                        <!-- Assigned Teachers -->
                        @if($teachers->count() > 0)
                            <div class="border-t border-gray-100 pt-3 mt-3">
                                <p class="text-xs font-medium text-gray-700 mb-2">My Teachers:</p>
                                <div class="flex flex-wrap gap-1">
                                    @foreach($teachers as $teacher)
                                        <span class="badge {{ $teacher->pivot->is_primary ?? false ? 'badge-blue' : 'badge-gray' }}">
                                            {{ $teacher->user->name }}
                                            @if($teacher->pivot->is_primary ?? false)
                                                (Primary)
                                            @endif
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                        @else
                            <div class="border-t border-gray-100 pt-3 mt-3">
                                <p class="text-xs text-gray-500 italic">No teachers assigned</p>
                            </div>
                        @endif

                        <div class="mt-3 flex items-center justify-between">
                            <span class="text-xs text-gray-500">{{ $subject->credits }} credits</span>
                            <span class="text-xs text-gray-500">{{ ucfirst($subject->category) }}</span>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No subjects enrolled</h3>
                <p class="mt-1 text-sm text-gray-500">You are not enrolled in any subjects yet.</p>
            </div>
        @endif
    </div>
    @endif

    <!-- Attendance Summary -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">My Attendance (Current Month)</h3>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ $attendanceStats['present'] }}</div>
                <div class="text-sm text-gray-500">Present</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-red-600">{{ $attendanceStats['absent'] }}</div>
                <div class="text-sm text-gray-500">Absent</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ $attendanceStats['excused'] }}</div>
                <div class="text-sm text-gray-500">Excused</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">{{ $attendanceStats['attendance_percentage'] }}%</div>
                <div class="text-sm text-gray-500">Attendance Rate</div>
            </div>
        </div>

        <div class="mb-4">
            <div class="flex items-center justify-between text-sm mb-2">
                <span>Overall Attendance</span>
                <span class="font-semibold">{{ $attendanceStats['attendance_percentage'] }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $attendanceStats['attendance_percentage'] }}%"></div>
            </div>
        </div>
    </div>

    <!-- Recent Attendance -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Attendance</h3>
        </div>

        @if($recentAttendance->count() > 0)
            <div class="divide-y divide-gray-200">
                @foreach($recentAttendance->take(5) as $attendance)
                    <div class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $attendance->date->format('M d, Y') }}</p>
                                <p class="text-sm text-gray-500">
                                    Marked by: {{ $attendance->markedBy->name }}
                                    @if($attendance->check_in_time)
                                        at {{ $attendance->check_in_time->format('H:i') }}
                                    @endif
                                </p>
                                @if($attendance->notes)
                                    <p class="text-xs text-gray-400 mt-1">{{ $attendance->notes }}</p>
                                @endif
                            </div>
                            <div class="text-right">
                                <span class="badge badge-{{ $attendance->status_badge_color }}">
                                    {{ $attendance->status_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="px-6 py-8 text-center">
                <p class="text-sm text-gray-500">No attendance records found.</p>
            </div>
        @endif
    </div>

    <!-- Recent Invoices -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Invoices</h3>
        </div>
        <div class="p-6">
            @if($recent_invoices->count() > 0)
                <div class="space-y-4">
                    @foreach($recent_invoices as $invoice)
                        <div class="flex items-center justify-between border-b border-gray-100 pb-4">
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $invoice->title }}</p>
                                <p class="text-sm text-gray-500">{{ $invoice->description }}</p>
                                <p class="text-xs text-gray-400">Due: {{ $invoice->due_date->format('M j, Y') }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900">RM {{ number_format($invoice->total_amount, 2) }}</p>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($invoice->status === 'paid') bg-green-100 text-green-800
                                    @elseif($invoice->status === 'sent') bg-blue-100 text-blue-800
                                    @elseif($invoice->status === 'overdue') bg-red-100 text-red-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    {{ ucfirst($invoice->status) }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No invoices</h3>
                    <p class="mt-1 text-sm text-gray-500">You don't have any invoices at the moment.</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{{ route('student.grades.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">View Grades</p>
                    <p class="text-sm text-gray-500">Check your academic performance</p>
                </div>
            </a>

            <div class="flex items-center p-4 border border-gray-200 rounded-lg bg-gray-50">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8a2 2 0 100-4 2 2 0 000 4zm6 0a2 2 0 100-4 2 2 0 000 4zm-6 4a2 2 0 100-4 2 2 0 000 4zm6 0a2 2 0 100-4 2 2 0 000 4z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Attendance</p>
                    <p class="text-sm text-gray-400">Coming soon...</p>
                </div>
            </div>

            <div class="flex items-center p-4 border border-gray-200 rounded-lg bg-gray-50">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Assignments</p>
                    <p class="text-sm text-gray-400">Coming soon...</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
