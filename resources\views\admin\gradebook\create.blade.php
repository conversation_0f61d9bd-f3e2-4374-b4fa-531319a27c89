@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Add Grade"
        description="Add a new grade for a student"
        :back-route="route('admin.gradebook.index')"
        back-label="Back to Gradebook">
    </x-page-header>

    <!-- Grade Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Grade Information</h3>
        </div>
        
        <form action="{{ route('admin.gradebook.store') }}" method="POST" class="p-6">
            @csrf
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Student Selection -->
                <div>
                    <label for="student_id" class="block text-sm font-medium text-gray-700 mb-2">Student *</label>
                    <select name="student_id" id="student_id" required class="form-select">
                        <option value="">Select Student</option>
                        @foreach($students as $student)
                            <option value="{{ $student->id }}" {{ old('student_id') == $student->id ? 'selected' : '' }}>
                                {{ $student->user->name }} ({{ $student->student_id }})
                            </option>
                        @endforeach
                    </select>
                    @error('student_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Subject Selection -->
                <div>
                    <label for="subject_id" class="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
                    <select name="subject_id" id="subject_id" required class="form-select">
                        <option value="">Select Subject</option>
                        @foreach($subjects as $subject)
                            <option value="{{ $subject->id }}" {{ old('subject_id') == $subject->id ? 'selected' : '' }}>
                                {{ $subject->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('subject_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Teacher Selection -->
                <div>
                    <label for="teacher_id" class="block text-sm font-medium text-gray-700 mb-2">Teacher *</label>
                    <select name="teacher_id" id="teacher_id" required class="form-select">
                        <option value="">Select Teacher</option>
                        @foreach($teachers as $teacher)
                            <option value="{{ $teacher->id }}" {{ old('teacher_id') == $teacher->id ? 'selected' : '' }}>
                                {{ $teacher->user->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('teacher_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Grade Category Selection -->
                <div>
                    <label for="grade_category_id" class="block text-sm font-medium text-gray-700 mb-2">Grade Category *</label>
                    <select name="grade_category_id" id="grade_category_id" required class="form-select">
                        <option value="">Select Category</option>
                        @foreach($gradeCategories as $category)
                            <option value="{{ $category->id }}" {{ old('grade_category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }} ({{ $category->weight }}%)
                            </option>
                        @endforeach
                    </select>
                    @error('grade_category_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Academic Term Selection -->
                <div>
                    <label for="academic_term_id" class="block text-sm font-medium text-gray-700 mb-2">Academic Term *</label>
                    <select name="academic_term_id" id="academic_term_id" required class="form-select">
                        <option value="">Select Term</option>
                        @foreach($academicTerms as $term)
                            <option value="{{ $term->id }}" {{ old('academic_term_id') == $term->id ? 'selected' : '' }}>
                                {{ $term->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('academic_term_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Assignment Name -->
                <div>
                    <label for="assignment_name" class="block text-sm font-medium text-gray-700 mb-2">Assignment Name *</label>
                    <input type="text" name="assignment_name" id="assignment_name" value="{{ old('assignment_name') }}" required
                           class="form-input" placeholder="e.g., Math Quiz 1, Science Project">
                    @error('assignment_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Score -->
                <div>
                    <label for="score" class="block text-sm font-medium text-gray-700 mb-2">Score *</label>
                    <input type="number" name="score" id="score" value="{{ old('score') }}" required
                           step="0.01" min="0" class="form-input" placeholder="e.g., 85">
                    @error('score')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Max Score -->
                <div>
                    <label for="max_score" class="block text-sm font-medium text-gray-700 mb-2">Max Score *</label>
                    <input type="number" name="max_score" id="max_score" value="{{ old('max_score', 100) }}" required
                           step="0.01" min="0.01" class="form-input" placeholder="e.g., 100">
                    @error('max_score')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Assignment Date -->
                <div>
                    <label for="assignment_date" class="block text-sm font-medium text-gray-700 mb-2">Assignment Date *</label>
                    <input type="date" name="assignment_date" id="assignment_date" value="{{ old('assignment_date', date('Y-m-d')) }}" required
                           class="form-input">
                    @error('assignment_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Due Date -->
                <div>
                    <label for="due_date" class="block text-sm font-medium text-gray-700 mb-2">Due Date</label>
                    <input type="date" name="due_date" id="due_date" value="{{ old('due_date') }}"
                           class="form-input">
                    @error('due_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div class="mt-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea name="description" id="description" rows="3" class="form-input"
                          placeholder="Optional description of the assignment">{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Comments -->
            <div class="mt-6">
                <label for="comments" class="block text-sm font-medium text-gray-700 mb-2">Comments</label>
                <textarea name="comments" id="comments" rows="3" class="form-input"
                          placeholder="Optional comments about the grade">{{ old('comments') }}</textarea>
                @error('comments')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Publish Status -->
            <div class="mt-6">
                <label class="flex items-center">
                    <input type="checkbox" name="is_published" value="1" {{ old('is_published') ? 'checked' : '' }}
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    <span class="ml-2 text-sm text-gray-700">Publish grade immediately (students and guardians can see it)</span>
                </label>
                @error('is_published')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Form Actions -->
            <div class="mt-8 flex items-center justify-end space-x-3">
                <a href="{{ route('admin.gradebook.index') }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Add Grade
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
// Calculate percentage when score or max_score changes
function calculatePercentage() {
    const score = parseFloat(document.getElementById('score').value) || 0;
    const maxScore = parseFloat(document.getElementById('max_score').value) || 1;
    const percentage = (score / maxScore) * 100;
    
    // You can add a percentage display here if needed
    console.log('Percentage:', percentage.toFixed(2) + '%');
}

document.getElementById('score').addEventListener('input', calculatePercentage);
document.getElementById('max_score').addEventListener('input', calculatePercentage);
</script>
@endpush

@endsection
