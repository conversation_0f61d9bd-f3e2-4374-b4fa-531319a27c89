@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Teacher Dashboard</h1>
                <p class="text-gray-600">Welcome back, {{ auth()->user()->name }}!</p>
                @if($teacher)
                    <p class="text-sm text-gray-500">Employee ID: {{ $teacher->employee_id }} | Specialization: {{ $teacher->specialization }}</p>
                @endif
            </div>
            <div class="text-right">
                <p class="text-sm text-gray-500">{{ now()->format('l, F j, Y') }}</p>
                <p class="text-sm text-gray-500">{{ now()->format('g:i A') }}</p>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <span class="text-white font-bold text-sm">{{ $stats['my_invoices'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">My Invoices</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['my_invoices'] }} total</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <span class="text-white font-bold text-sm">{{ $stats['pending_invoices'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending Invoices</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['pending_invoices'] }} pending</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <span class="text-white font-bold text-sm">{{ $stats['paid_invoices'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Paid Invoices</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['paid_invoices'] }} paid</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Students</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['total_students'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Attendance -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">Today's Attendance</h3>
            <span class="text-sm text-gray-500">{{ $today }}</span>
        </div>

        @if($todayAttendance)
            <div class="flex items-center justify-between p-4 bg-{{ $todayAttendance->status_badge_color }}-50 border border-{{ $todayAttendance->status_badge_color }}-200 rounded-lg">
                <div>
                    <p class="text-sm font-medium text-{{ $todayAttendance->status_badge_color }}-800">
                        Status: {{ $todayAttendance->status_display }}
                    </p>
                    @if($todayAttendance->check_in_time)
                        <p class="text-xs text-{{ $todayAttendance->status_badge_color }}-600">
                            Check-in: {{ $todayAttendance->check_in_time->format('g:i A') }}
                            @if($todayAttendance->check_out_time)
                                | Check-out: {{ $todayAttendance->check_out_time->format('g:i A') }}
                            @endif
                        </p>
                    @endif
                    @if($todayAttendance->notes)
                        <p class="text-xs text-{{ $todayAttendance->status_badge_color }}-600 mt-1">
                            Notes: {{ $todayAttendance->notes }}
                        </p>
                    @endif
                </div>
                <button onclick="markMyAttendance('{{ $todayAttendance->status }}', '{{ $todayAttendance->check_in_time ? $todayAttendance->check_in_time->format('H:i') : '' }}', '{{ $todayAttendance->check_out_time ? $todayAttendance->check_out_time->format('H:i') : '' }}', '{{ $todayAttendance->notes }}')"
                        class="text-{{ $todayAttendance->status_badge_color }}-600 hover:text-{{ $todayAttendance->status_badge_color }}-800 text-sm font-medium">
                    Edit
                </button>
            </div>
        @else
            <div class="text-center py-6">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Attendance Not Marked</h3>
                <p class="mt-1 text-sm text-gray-500">Mark your attendance for today.</p>
                <div class="mt-4">
                    <button onclick="markMyAttendance()" class="btn-primary">
                        Mark Attendance
                    </button>
                </div>
            </div>
        @endif
    </div>

    <!-- Attendance Statistics -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">My Attendance Statistics (This Month)</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center p-3 bg-green-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600">{{ $attendanceStats['present'] }}</div>
                <div class="text-sm text-gray-500">Present</div>
            </div>
            <div class="text-center p-3 bg-red-50 rounded-lg">
                <div class="text-2xl font-bold text-red-600">{{ $attendanceStats['absent'] }}</div>
                <div class="text-sm text-gray-500">Absent</div>
            </div>
            <div class="text-center p-3 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600">{{ $attendanceStats['sick_leave'] }}</div>
                <div class="text-sm text-gray-500">Sick Leave</div>
            </div>
            <div class="text-center p-3 bg-blue-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600">{{ $attendanceStats['attendance_percentage'] }}%</div>
                <div class="text-sm text-gray-500">Attendance Rate</div>
            </div>
        </div>
    </div>

    <!-- Recent Attendance History -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Attendance History</h3>
        @if($recentAttendance->count() > 0)
            <div class="space-y-3">
                @foreach($recentAttendance as $attendance)
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ \Carbon\Carbon::parse($attendance->date)->format('M j, Y') }}</p>
                            <p class="text-xs text-gray-500">{{ \Carbon\Carbon::parse($attendance->date)->format('l') }}</p>
                        </div>
                        <div class="text-right">
                            <span class="badge badge-{{ $attendance->status_badge_color }}">
                                {{ $attendance->status_display }}
                            </span>
                            @if($attendance->check_in_time)
                                <p class="text-xs text-gray-500 mt-1">{{ $attendance->check_in_time->format('g:i A') }}</p>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <p class="text-gray-500 text-center py-4">No attendance records found.</p>
        @endif
    </div>

    <!-- Recent Invoices -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Invoices Created</h3>
        </div>
        <div class="p-6">
            @if($recent_invoices->count() > 0)
                <div class="space-y-4">
                    @foreach($recent_invoices as $invoice)
                        <div class="flex items-center justify-between border-b border-gray-100 pb-4">
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $invoice->title }}</p>
                                <p class="text-sm text-gray-500">Student: {{ $invoice->student->user->name ?? 'N/A' }}</p>
                                <p class="text-xs text-gray-400">Created: {{ $invoice->created_at->format('M j, Y g:i A') }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900">RM {{ number_format($invoice->total_amount, 2) }}</p>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($invoice->status === 'paid') bg-green-100 text-green-800
                                    @elseif($invoice->status === 'sent') bg-blue-100 text-blue-800
                                    @elseif($invoice->status === 'overdue') bg-red-100 text-red-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    {{ ucfirst($invoice->status) }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No invoices</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating your first invoice.</p>
                    <div class="mt-6">
                        <button type="button" class="btn-primary">
                            Create Invoice
                        </button>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{{ route('teacher.invoices.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Manage Invoices</p>
                    <p class="text-sm text-gray-500">Create and manage student invoices</p>
                </div>
            </a>

            <a href="{{ route('teacher.students.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">View Students</p>
                    <p class="text-sm text-gray-500">Access student information</p>
                </div>
            </a>

            <div class="flex items-center p-4 border border-gray-200 rounded-lg bg-gray-50">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Reports</p>
                    <p class="text-sm text-gray-400">Coming soon...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Modal -->
<div id="attendanceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4" id="modalTitle">Mark My Attendance</h3>
            <form id="attendanceForm">
                <input type="hidden" name="date" value="{{ $today }}">

                <div class="mb-4">
                    <label for="attendanceStatus" class="block text-sm font-medium text-gray-700">Status</label>
                    <select id="attendanceStatus" name="status" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="present">Present</option>
                        <option value="absent">Absent</option>
                        <option value="sick_leave">Sick Leave</option>
                        <option value="personal_leave">Personal Leave</option>
                        <option value="official_duty">Official Duty</option>
                    </select>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="checkInTime" class="block text-sm font-medium text-gray-700">Check-in Time</label>
                        <input type="time" id="checkInTime" name="check_in_time"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="checkOutTime" class="block text-sm font-medium text-gray-700">Check-out Time</label>
                        <input type="time" id="checkOutTime" name="check_out_time"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>
                </div>

                <div class="mb-4">
                    <label for="attendanceNotes" class="block text-sm font-medium text-gray-700">Notes</label>
                    <textarea id="attendanceNotes" name="notes" rows="3"
                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                              placeholder="Optional notes..."></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Save Attendance</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function markMyAttendance(currentStatus = '', currentCheckIn = '', currentCheckOut = '', currentNotes = '') {
    document.getElementById('attendanceStatus').value = currentStatus || 'present';
    document.getElementById('checkInTime').value = currentCheckIn || '';
    document.getElementById('checkOutTime').value = currentCheckOut || '';
    document.getElementById('attendanceNotes').value = currentNotes || '';
    document.getElementById('modalTitle').textContent = currentStatus ? 'Edit My Attendance' : 'Mark My Attendance';
    document.getElementById('attendanceModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('attendanceModal').classList.add('hidden');
}

document.getElementById('attendanceForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = Object.fromEntries(formData);

    try {
        const response = await fetch('{{ route("admin.attendance.mark-my-attendance") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            showToast(result.message, 'success');
            closeModal();
            // Force reload with cache busting
            setTimeout(() => {
                window.location.href = window.location.href + (window.location.href.includes('?') ? '&' : '?') + '_t=' + Date.now();
            }, 1000);
        } else {
            showToast(result.message || 'Error marking attendance', 'error');
        }
    } catch (error) {
        showToast('Error marking attendance', 'error');
    }
});

// Close modal when clicking outside
document.getElementById('attendanceModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>
@endpush
@endsection
