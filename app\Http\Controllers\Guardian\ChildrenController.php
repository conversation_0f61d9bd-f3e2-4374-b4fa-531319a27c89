<?php

namespace App\Http\Controllers\Guardian;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ChildrenController extends Controller
{
    public function index()
    {
        $guardian = auth()->user()->guardian;

        if (!$guardian) {
            $students = collect();
        } else {
            // Load students with basic relationships only
            $students = $guardian->students()->with('user')->get();
        }

        return view('guardian.children.index', compact('students'));
    }
}
