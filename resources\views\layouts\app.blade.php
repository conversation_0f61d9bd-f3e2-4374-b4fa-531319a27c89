<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="font-sans antialiased bg-gray-50 overflow-hidden" x-data="{
    sidebarOpen: true
}">
    <div class="h-screen flex overflow-hidden">
        <!-- Sidebar -->
        @if(auth()->check())
            @include('components.sidebar')
        @endif

        <!-- Main Content -->
        <main class="flex-1 min-w-0 flex flex-col overflow-hidden @if(auth()->check()) ml-64 @endif">
            <div class="flex-1 overflow-y-auto pb-20">
                <div class="px-6 py-6">
                    @yield('content')
                </div>
            </div>
        </main>


    </div>

    <!-- Toast Notifications -->
    @include('components.toast')

    <!-- Confirmation Modal -->
    @include('components.confirmation-modal')

    <!-- Additional Scripts -->
    @stack('scripts')
</body>
</html>
