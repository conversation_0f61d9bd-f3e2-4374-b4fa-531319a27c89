<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id',
        'paid_by',
        'payment_reference',
        'amount',
        'status',
        'payment_method',
        'billplz_bill_id',
        'billplz_transaction_id',
        'billplz_response',
        'paid_at',
        'receipt_url',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'billplz_response' => 'array',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the invoice that owns the payment
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the user who made the payment
     */
    public function payer()
    {
        return $this->belongsTo(User::class, 'paid_by');
    }

    /**
     * Check if payment is successful
     */
    public function isSuccessful()
    {
        return $this->status === 'paid';
    }

    /**
     * Check if payment is pending
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }
}
