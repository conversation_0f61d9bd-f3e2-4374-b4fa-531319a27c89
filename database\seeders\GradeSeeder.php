<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Grade;
use App\Models\Student;
use App\Models\Subject;
use App\Models\Teacher;
use App\Models\GradeCategory;
use App\Models\AcademicTerm;
use Carbon\Carbon;

class GradeSeeder extends Seeder
{
    public function run()
    {
        // Get required data
        $students = Student::with('user')->take(10)->get();
        $subjects = Subject::take(3)->get();
        $teachers = Teacher::take(3)->get();
        $gradeCategories = GradeCategory::take(4)->get();
        $academicTerm = AcademicTerm::first();

        if ($students->isEmpty() || $subjects->isEmpty() || $teachers->isEmpty() || 
            $gradeCategories->isEmpty() || !$academicTerm) {
            $this->command->info('Required data not found. Please run other seeders first.');
            return;
        }

        $assignmentTypes = [
            'Quiz 1', 'Quiz 2', 'Quiz 3',
            'Assignment 1', 'Assignment 2', 'Assignment 3',
            'Test 1', 'Test 2', 'Midterm Exam', 'Final Exam',
            'Project 1', 'Project 2', 'Lab Report 1', 'Lab Report 2'
        ];

        $descriptions = [
            'Chapter 1 assessment',
            'Weekly quiz on recent topics',
            'Homework assignment',
            'Group project submission',
            'Laboratory practical',
            'Research paper',
            'Presentation assignment',
            'Problem-solving exercise'
        ];

        // Create grades for each student and subject combination
        foreach ($students as $student) {
            foreach ($subjects as $subject) {
                $teacher = $teachers->random();
                
                // Create 3-5 grades per student per subject
                $gradeCount = rand(3, 5);
                
                for ($i = 0; $i < $gradeCount; $i++) {
                    $category = $gradeCategories->random();
                    $assignmentName = $assignmentTypes[array_rand($assignmentTypes)];
                    $description = $descriptions[array_rand($descriptions)];
                    
                    // Generate realistic scores
                    $maxScore = rand(50, 100);
                    $percentage = rand(60, 98); // Most students pass
                    $score = round(($percentage / 100) * $maxScore, 2);
                    
                    $assignmentDate = Carbon::now()->subDays(rand(1, 60));
                    $dueDate = $assignmentDate->copy()->addDays(rand(1, 14));
                    
                    $grade = Grade::create([
                        'student_id' => $student->id,
                        'subject_id' => $subject->id,
                        'teacher_id' => $teacher->id,
                        'grade_category_id' => $category->id,
                        'academic_term_id' => $academicTerm->id,
                        'assignment_name' => $assignmentName,
                        'description' => $description,
                        'score' => $score,
                        'max_score' => $maxScore,
                        'assignment_date' => $assignmentDate,
                        'due_date' => $dueDate,
                        'comments' => rand(1, 3) == 1 ? 'Good work!' : null,
                        'is_published' => rand(1, 4) != 1, // 75% published
                    ]);

                    // Update letter grade and GPA
                    $grade->updateLetterGrade();
                }
            }
        }

        $this->command->info('Sample grades created successfully!');
    }
}
