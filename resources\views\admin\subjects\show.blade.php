@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <x-page-header
        title="Subject Details"
        description="View and manage subject information"
        :back-route="route('admin.academic.subjects.index')"
        back-label="Back to Subjects">
        
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.academic.subjects.edit', $subject) }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Subject
            </a>

            <button type="button"
                    onclick="toggleSubjectStatus('{{ $subject->id }}', '{{ $subject->name }}', {{ $subject->is_active ? 'false' : 'true' }})"
                    class="btn-{{ $subject->is_active ? 'secondary' : 'primary' }}">
                @if($subject->is_active)
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                    </svg>
                    Deactivate
                @else
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Activate
                @endif
            </button>
        </div>
    </x-page-header>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Subject Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="card">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    <div class="flex items-center space-x-2">
                        <span class="badge badge-{{ $subject->category_badge_color }}">
                            {{ $subject->category }}
                        </span>
                        <span class="badge badge-{{ $subject->status_badge_color }}">
                            {{ $subject->status_display }}
                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Subject Code</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $subject->subject_code }}</dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Subject Name</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $subject->name }}</dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Credits</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $subject->credits }}</dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Grade Levels</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $subject->formatted_grade_levels }}</dd>
                    </div>
                </div>

                @if($subject->description)
                    <div class="mt-6">
                        <dt class="text-sm font-medium text-gray-500">Description</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $subject->description }}</dd>
                    </div>
                @endif
            </div>

            <!-- Prerequisites -->
            @if($subject->prerequisiteSubjects->count() > 0)
                <div class="card">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Prerequisites</h3>
                    <div class="space-y-3">
                        @foreach($subject->prerequisiteSubjects as $prerequisite)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $prerequisite->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $prerequisite->subject_code }}</div>
                                </div>
                                <span class="badge badge-{{ $prerequisite->category_badge_color }}">
                                    {{ $prerequisite->category }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Dependent Subjects -->
            @if($subject->dependentSubjects->count() > 0)
                <div class="card">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Subjects that require this as prerequisite</h3>
                    <div class="space-y-3">
                        @foreach($subject->dependentSubjects as $dependent)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $dependent->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $dependent->subject_code }}</div>
                                </div>
                                <span class="badge badge-{{ $dependent->category_badge_color }}">
                                    {{ $dependent->category }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>

        <!-- Statistics and Assignments -->
        <div class="space-y-6">
            <!-- Statistics -->
            <div class="card">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Classes Offering</span>
                        <span class="text-sm font-medium text-gray-900">{{ $subject->classes->count() }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Assigned Teachers</span>
                        <span class="text-sm font-medium text-gray-900">{{ $subject->teachers->count() }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Prerequisites</span>
                        <span class="text-sm font-medium text-gray-900">{{ $subject->prerequisiteSubjects->count() }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Dependent Subjects</span>
                        <span class="text-sm font-medium text-gray-900">{{ $subject->dependentSubjects->count() }}</span>
                    </div>
                </div>
            </div>

            <!-- Classes Offering This Subject -->
            @if($subject->classes->count() > 0)
                <div class="card">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Classes Offering This Subject</h3>
                    <div class="space-y-3">
                        @foreach($subject->classes as $class)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $class->name }}</div>
                                    <div class="text-sm text-gray-500">
                                        {{ $class->pivot->hours_per_week }} hours/week
                                        @if($class->pivot->is_mandatory)
                                            • Mandatory
                                        @else
                                            • Elective
                                        @endif
                                    </div>
                                </div>
                                <span class="badge badge-{{ $class->level_badge_color }}">
                                    {{ $class->level }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Assigned Teachers -->
            @if($subject->teachers->count() > 0)
                <div class="card">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Assigned Teachers</h3>
                    <div class="space-y-3">
                        @foreach($subject->teachers as $teacher)
                            <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm font-medium">
                                            {{ substr($teacher->user->name, 0, 2) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="text-sm font-medium text-gray-900">{{ $teacher->user->name }}</div>
                                    <div class="text-sm text-gray-500">
                                        @if($teacher->pivot->class_id)
                                            Class: {{ $teacher->pivot->class->name ?? 'N/A' }}
                                            @if($teacher->pivot->section_id)
                                                - {{ $teacher->pivot->section->name ?? 'N/A' }}
                                            @endif
                                        @endif
                                        @if($teacher->pivot->is_primary)
                                            <span class="badge badge-blue ml-2">Primary</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
// Toggle subject status
async function toggleSubjectStatus(subjectId, subjectName, newStatus) {
    const action = newStatus === 'true' ? 'activate' : 'deactivate';
    
    const confirmed = await confirmAction(
        `${action.charAt(0).toUpperCase() + action.slice(1)} Subject`,
        `Are you sure you want to ${action} "${subjectName}"?`
    );
    
    if (confirmed) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/academic/subjects/${subjectId}/toggle-status`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection
