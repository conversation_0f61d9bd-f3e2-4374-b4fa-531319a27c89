<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\StudentAttendance;
use App\Models\TeacherAttendance;

use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AttendanceController extends Controller
{
    /**
     * Display attendance dashboard
     */
    public function index()
    {
        $today = Carbon::today();

        
        // Get today's attendance statistics
        $studentStats = [
            'total_students' => Student::whereHas('user', function($q) { $q->where('is_active', true); })->count(),
            'marked_today' => StudentAttendance::where('date', $today)->count(),
            'present_today' => StudentAttendance::where('date', $today)->where('status', 'present')->count(),
            'absent_today' => StudentAttendance::where('date', $today)->where('status', 'absent')->count(),
            'late_today' => StudentAttendance::where('date', $today)->where('status', 'late')->count(),
        ];

        $teacherStats = [
            'total_teachers' => Teacher::whereHas('user', function($q) { $q->where('is_active', true); })->count(),
            'marked_today' => TeacherAttendance::where('date', $today)->count(),
            'present_today' => TeacherAttendance::where('date', $today)->where('status', 'present')->count(),
            'absent_today' => TeacherAttendance::where('date', $today)->where('status', 'absent')->count(),
        ];

        // Calculate attendance percentages
        $studentStats['attendance_percentage'] = $studentStats['total_students'] > 0 
            ? round((($studentStats['present_today'] + $studentStats['late_today']) / $studentStats['total_students']) * 100, 1)
            : 0;

        $teacherStats['attendance_percentage'] = $teacherStats['total_teachers'] > 0 
            ? round(($teacherStats['present_today'] / $teacherStats['total_teachers']) * 100, 1)
            : 0;

        // Get recent attendance activities
        $recentActivities = collect()
            ->merge(
                StudentAttendance::with(['student.user', 'markedBy'])
                    ->where('date', $today)
                    ->latest()
                    ->take(10)
                    ->get()
                    ->map(function($attendance) {
                        return [
                            'type' => 'student',
                            'name' => $attendance->student->user->name,
                            'status' => $attendance->status,
                            'time' => $attendance->created_at,
                            'marked_by' => $attendance->markedBy->name,
                        ];
                    })
            )
            ->merge(
                TeacherAttendance::with(['teacher.user', 'markedBy'])
                    ->where('date', $today)
                    ->latest()
                    ->take(10)
                    ->get()
                    ->map(function($attendance) {
                        return [
                            'type' => 'teacher',
                            'name' => $attendance->teacher->user->name,
                            'status' => $attendance->status,
                            'time' => $attendance->created_at,
                            'marked_by' => $attendance->markedBy->name,
                        ];
                    })
            )
            ->sortByDesc('time')
            ->take(15);

        return view('admin.attendance.index', compact(
            'studentStats',
            'teacherStats',
            'recentActivities',
            'today'
        ));
    }

    /**
     * Display student attendance management
     */
    public function students(Request $request)
    {
        $date = $request->get('date', Carbon::today()->format('Y-m-d'));
        $class = $request->get('class');
        $section = $request->get('section');
        $status = $request->get('status');
        $search = $request->get('search');

        $query = Student::with(['user', 'attendances' => function($q) use ($date) {
            $q->whereDate('date', $date);
        }])->whereHas('user', function($q) {
            $q->where('is_active', true);
        });

        if ($class) {
            $query->where('class', $class);
        }

        if ($section) {
            $query->where('section', $section);
        }

        // Add search functionality
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->whereHas('user', function($userQuery) use ($search) {
                    $userQuery->where('name', 'like', '%' . $search . '%')
                              ->orWhere('email', 'like', '%' . $search . '%');
                })->orWhere('student_id', 'like', '%' . $search . '%')
                  ->orWhere('roll_number', 'like', '%' . $search . '%');
            });
        }

        $students = $query->orderBy('class')->orderBy('section')->orderBy('roll_number')->get();

        // Filter by attendance status if specified
        if ($status) {
            $students = $students->filter(function($student) use ($status) {
                $attendance = $student->attendances->first();
                if ($status === 'not_marked') {
                    return !$attendance;
                }
                return $attendance && $attendance->status === $status;
            });
        }

        // Get available classes and sections
        $classes = Student::distinct()->pluck('class')->filter()->sort()->values();
        $sections = Student::distinct()->pluck('section')->filter()->sort()->values();

        // Get attendance statistics for the selected date
        $attendanceStats = StudentAttendance::getClassStats($class, $section, $date);

        return view('admin.attendance.simplified-students', compact(
            'students',
            'date',
            'class',
            'section',
            'status',
            'search',
            'classes',
            'sections',
            'attendanceStats'
        ));
    }

    /**
     * Display teacher attendance management
     */
    public function teachers(Request $request)
    {
        $date = $request->get('date', Carbon::today()->format('Y-m-d'));
        $status = $request->get('status');
        $search = $request->get('search');

        $query = Teacher::with(['user', 'attendances' => function($q) use ($date) {
            $q->whereDate('date', $date);
        }])->whereHas('user', function($q) {
            $q->where('is_active', true);
        });

        // Add search functionality
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->whereHas('user', function($userQuery) use ($search) {
                    $userQuery->where('name', 'like', '%' . $search . '%')
                              ->orWhere('email', 'like', '%' . $search . '%');
                })->orWhere('employee_id', 'like', '%' . $search . '%')
                  ->orWhere('specialization', 'like', '%' . $search . '%');
            });
        }

        $teachers = $query->orderBy('employee_id')->get();

        // Filter by attendance status if specified
        if ($status) {
            $teachers = $teachers->filter(function($teacher) use ($status) {
                $attendance = $teacher->attendances->first();
                if ($status === 'not_marked') {
                    return !$attendance;
                }
                return $attendance && $attendance->status === $status;
            });
        }

        // Get attendance statistics for the selected date
        $attendanceStats = TeacherAttendance::getOverallStats($date, $date);

        return view('admin.attendance.simplified-teachers', compact(
            'teachers',
            'date',
            'status',
            'search',
            'attendanceStats'
        ));
    }

    /**
     * Mark student attendance
     */
    public function markStudent(Request $request)
    {
        try {
            $request->validate([
                'student_id' => 'required|exists:students,id',
                'date' => 'required|date',
                'status' => 'required|in:present,absent',
                'subcategory' => 'nullable|string|max:100',
                'notes' => 'nullable|string|max:500',
            ]);

            $student = Student::findOrFail($request->student_id);

            // For students, clear subcategory if status is present (students don't have subcategories for present status)
            $subcategory = $request->status === 'present' ? null : $request->subcategory;

            // Allow backdated marking (no restrictions for simplified system)

            // Check if attendance already exists for this student and date
            $existingAttendance = StudentAttendance::where('student_id', $request->student_id)
                ->whereDate('date', $request->date)
                ->first();

            if ($existingAttendance) {
                // Update existing record
                $existingAttendance->update([
                    'status' => $request->status,
                    'subcategory' => $subcategory,
                    'notes' => $request->notes,
                    'class' => $student->class,
                    'section' => $student->section,
                    'marked_by' => auth()->id(),
                ]);
                $attendance = $existingAttendance;
            } else {
                // Create new record
                $attendance = StudentAttendance::create([
                    'student_id' => $request->student_id,
                    'date' => $request->date,
                    'status' => $request->status,
                    'subcategory' => $subcategory,
                    'notes' => $request->notes,
                    'class' => $student->class,
                    'section' => $student->section,
                    'marked_by' => auth()->id(),
                ]);
            }

            // Log activity
            \App\Models\ActivityLog::logAttendance(
                'marked_attendance',
                'student',
                $student->user->name,
                $request->date,
                $request->status,
                1,
                $request->notes
            );

            return response()->json([
                'success' => true,
                'message' => 'Attendance marked successfully.',
                'attendance' => $attendance
            ]);
        } catch (\Exception $e) {
            \Log::error('Student attendance marking failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to mark attendance: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark teacher attendance
     */
    public function markTeacher(Request $request)
    {
        try {
            $request->validate([
                'teacher_id' => 'required|exists:teachers,id',
                'date' => 'required|date',
                'status' => 'required|in:present,absent',
                'subcategory' => 'nullable|string|max:100',
                'notes' => 'nullable|string|max:500',
            ]);

            // Allow backdated marking (no restrictions for simplified system)

            // Check if attendance already exists for this teacher and date
            $existingAttendance = TeacherAttendance::where('teacher_id', $request->teacher_id)
                ->whereDate('date', $request->date)
                ->first();

            if ($existingAttendance) {
                // Update existing record
                $existingAttendance->update([
                    'status' => $request->status,
                    'subcategory' => $request->subcategory,
                    'notes' => $request->notes,
                    'marked_by' => auth()->id(),
                ]);
                $attendance = $existingAttendance;
            } else {
                // Create new record
                $attendance = TeacherAttendance::create([
                    'teacher_id' => $request->teacher_id,
                    'date' => $request->date,
                    'status' => $request->status,
                    'subcategory' => $request->subcategory,
                    'notes' => $request->notes,
                    'marked_by' => auth()->id(),
                ]);
            }

            // Log activity
            $teacher = \App\Models\Teacher::findOrFail($request->teacher_id);
            \App\Models\ActivityLog::logAttendance(
                'marked_attendance',
                'teacher',
                $teacher->user->name,
                $request->date,
                $request->status,
                1,
                $request->notes
            );

            return response()->json([
                'success' => true,
                'message' => 'Attendance marked successfully.',
                'attendance' => $attendance
            ]);
        } catch (\Exception $e) {
            \Log::error('Teacher attendance marking failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to mark attendance: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk mark student attendance
     */
    public function bulkMarkStudents(Request $request)
    {
        $request->validate([
            'student_ids' => 'required|array|min:1',
            'student_ids.*' => 'exists:students,id',
            'date' => 'required|date',
            'status' => 'required|in:present,absent',
            'subcategory' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500',
        ]);

        // Allow backdated marking (no restrictions for simplified system)

        // For students, clear subcategory if status is present (students don't have subcategories for present status)
        $subcategory = $request->status === 'present' ? null : $request->subcategory;

        $markedCount = 0;
        $errors = [];
        $studentNames = [];

        foreach ($request->student_ids as $studentId) {
            try {
                $student = Student::findOrFail($studentId);
                $studentNames[] = $student->user->name;

                // Find existing attendance record using whereDate to handle datetime vs date format
                $existingAttendance = StudentAttendance::where('student_id', $studentId)
                    ->whereDate('date', $request->date)
                    ->first();

                if ($existingAttendance) {
                    // Update existing record
                    $existingAttendance->update([
                        'status' => $request->status,
                        'subcategory' => $subcategory,
                        'notes' => $request->notes,
                        'class' => $student->class,
                        'section' => $student->section,
                        'marked_by' => auth()->id(),
                    ]);
                    $attendance = $existingAttendance;
                } else {
                    // Create new record
                    $attendance = StudentAttendance::create([
                        'student_id' => $studentId,
                        'date' => $request->date,
                        'status' => $request->status,
                        'subcategory' => $subcategory,
                        'notes' => $request->notes,
                        'class' => $student->class,
                        'section' => $student->section,
                        'marked_by' => auth()->id(),
                    ]);
                }

                $markedCount++;
            } catch (\Exception $e) {
                $errors[] = "Failed to mark attendance for student ID: {$studentId} - " . $e->getMessage();
                \Log::error('Bulk student attendance marking failed', [
                    'student_id' => $studentId,
                    'error' => $e->getMessage(),
                    'date' => $request->date,
                    'status' => $request->status
                ]);
            }
        }

        // Log bulk attendance activity
        if ($markedCount > 0) {
            \App\Models\ActivityLog::logAttendance(
                'bulk_marked_attendance',
                'student',
                $studentNames,
                $request->date,
                $request->status,
                $markedCount,
                $request->notes
            );
        }

        if ($markedCount > 0) {
            return response()->json([
                'success' => true,
                'message' => "Successfully marked attendance for {$markedCount} student(s).",
                'marked_count' => $markedCount,
                'errors' => $errors
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark attendance for any students.',
                'errors' => $errors
            ], 422);
        }
    }

    /**
     * Bulk mark teacher attendance
     */
    public function bulkMarkTeachers(Request $request)
    {
        $request->validate([
            'teacher_ids' => 'required|array|min:1',
            'teacher_ids.*' => 'exists:teachers,id',
            'date' => 'required|date',
            'status' => 'required|in:present,absent',
            'subcategory' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500',
        ]);

        // Allow backdated marking (no restrictions for simplified system)

        $markedCount = 0;
        $errors = [];
        $teacherNames = [];

        foreach ($request->teacher_ids as $teacherId) {
            try {
                $teacher = Teacher::findOrFail($teacherId);
                $teacherNames[] = $teacher->user->name;

                // Find existing attendance record using whereDate to handle datetime vs date format
                $existingAttendance = TeacherAttendance::where('teacher_id', $teacherId)
                    ->whereDate('date', $request->date)
                    ->first();

                if ($existingAttendance) {
                    // Update existing record
                    $existingAttendance->update([
                        'status' => $request->status,
                        'subcategory' => $request->subcategory,
                        'notes' => $request->notes,
                        'marked_by' => auth()->id(),
                    ]);
                    $attendance = $existingAttendance;
                } else {
                    // Create new record
                    $attendance = TeacherAttendance::create([
                        'teacher_id' => $teacherId,
                        'date' => $request->date,
                        'status' => $request->status,
                        'subcategory' => $request->subcategory,
                        'notes' => $request->notes,
                        'marked_by' => auth()->id(),
                    ]);
                }

                $markedCount++;
            } catch (\Exception $e) {
                $errors[] = "Failed to mark attendance for teacher ID: {$teacherId} - " . $e->getMessage();
                \Log::error('Bulk teacher attendance marking failed', [
                    'teacher_id' => $teacherId,
                    'error' => $e->getMessage(),
                    'date' => $request->date,
                    'status' => $request->status
                ]);
            }
        }

        // Log bulk attendance activity
        if ($markedCount > 0) {
            \App\Models\ActivityLog::logAttendance(
                'bulk_marked_attendance',
                'teacher',
                $teacherNames,
                $request->date,
                $request->status,
                $markedCount,
                $request->notes
            );
        }

        if ($markedCount > 0) {
            return response()->json([
                'success' => true,
                'message' => "Successfully marked attendance for {$markedCount} teacher(s).",
                'marked_count' => $markedCount,
                'errors' => $errors
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark attendance for any teachers.',
                'errors' => $errors
            ], 422);
        }
    }

    /**
     * Display attendance reports
     */
    public function reports(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        $reportType = $request->get('report_type', 'summary');
        $class = $request->get('class');
        $section = $request->get('section');

        // Get available classes and sections
        $classes = Student::distinct()->pluck('class')->filter()->sort()->values();
        $sections = Student::distinct()->pluck('section')->filter()->sort()->values();

        $reportData = [];

        if ($reportType === 'summary') {
            // Overall summary report
            $studentStats = StudentAttendance::getOverallStats($startDate, $endDate);
            $teacherStats = TeacherAttendance::getOverallStats($startDate, $endDate);

            $reportData = [
                'student_stats' => $studentStats,
                'teacher_stats' => $teacherStats,
                'period' => Carbon::parse($startDate)->format('M d, Y') . ' - ' . Carbon::parse($endDate)->format('M d, Y')
            ];
        } elseif ($reportType === 'detailed') {
            // Detailed student report
            $query = Student::with(['user', 'attendances' => function($q) use ($startDate, $endDate) {
                $q->whereBetween('date', [$startDate, $endDate]);
            }]);

            if ($class) {
                $query->where('class', $class);
            }
            if ($section) {
                $query->where('section', $section);
            }

            $students = $query->get()->map(function($student) use ($startDate, $endDate) {
                $stats = StudentAttendance::getStudentStats($student->id, $startDate, $endDate);
                return [
                    'student' => $student,
                    'stats' => $stats
                ];
            });

            $reportData = [
                'students' => $students,
                'class' => $class,
                'section' => $section
            ];
        }

        return view('admin.attendance.reports', compact(
            'reportData',
            'startDate',
            'endDate',
            'reportType',
            'class',
            'section',
            'classes',
            'sections'
        ));
    }

    /**
     * Teacher marks their own attendance
     */
    public function markMyAttendance(Request $request)
    {
        $request->validate([
            'date' => 'required|date',
            'status' => 'required|in:present,absent,sick_leave,personal_leave,official_duty',
            'check_in_time' => 'nullable|date_format:H:i',
            'check_out_time' => 'nullable|date_format:H:i',
            'notes' => 'nullable|string|max:500',
        ]);

        $teacher = auth()->user()->teacher;
        if (!$teacher) {
            return response()->json([
                'success' => false,
                'message' => 'Teacher profile not found.'
            ], 404);
        }



        $existingAttendance = TeacherAttendance::where('teacher_id', $teacher->id)
            ->whereDate('date', $request->date)
            ->first();

        $isEdit = $existingAttendance ? true : false;
        $oldStatus = $existingAttendance ? $existingAttendance->status : null;

        if ($existingAttendance) {
            // Update existing record
            $existingAttendance->update([
                'status' => $request->status,
                'check_in_time' => $request->check_in_time,
                'check_out_time' => $request->check_out_time,
                'notes' => $request->notes,
                'marked_by' => auth()->id(),
            ]);
            $attendance = $existingAttendance;
        } else {
            // Create new record
            $attendance = TeacherAttendance::create([
                'teacher_id' => $teacher->id,
                'date' => $request->date,
                'status' => $request->status,
                'check_in_time' => $request->check_in_time,
                'check_out_time' => $request->check_out_time,
                'notes' => $request->notes,
                'marked_by' => auth()->id(),
            ]);
        }

        // Calculate hours worked if both times are provided
        if ($request->check_in_time && $request->check_out_time) {
            $attendance->calculateHoursWorked();
        }

        // Log the activity
        $action = $isEdit ? 'updated_own_attendance' : 'marked_own_attendance';
        \App\Models\ActivityLog::log(
            $action,
            "Teacher {$teacher->user->name} " . ($isEdit ? 'updated' : 'marked') . " their own attendance for {$request->date}" .
            ($isEdit ? " (changed from {$oldStatus} to {$request->status})" : ""),
            'App\Models\TeacherAttendance',
            $attendance->id,
            [
                'teacher_id' => $teacher->id,
                'teacher_name' => $teacher->user->name,
                'date' => $request->date,
                'old_status' => $oldStatus,
                'new_status' => $request->status,
                'is_edit' => $isEdit,
            ]
        );

        return response()->json([
            'success' => true,
            'message' => $isEdit ? 'Attendance updated successfully.' : 'Attendance marked successfully.',
            'attendance' => $attendance
        ]);
    }


}
