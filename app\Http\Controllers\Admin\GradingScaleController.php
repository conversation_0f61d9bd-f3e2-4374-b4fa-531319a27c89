<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\GradingScale;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class GradingScaleController extends Controller
{
    /**
     * Display a listing of grading scales
     */
    public function index(Request $request)
    {
        $search = $request->get('search', '');
        $status = $request->get('status', '');

        $query = GradingScale::query();

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('letter_grade', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($status !== '') {
            $isActive = $status === 'active';
            $query->where('is_active', $isActive);
        }

        $scales = $query->orderBy('min_score', 'desc')->paginate(25);

        // Statistics
        $stats = [
            'total_scales' => GradingScale::count(),
            'active_scales' => GradingScale::active()->count(),
            'default_scale' => GradingScale::where('is_default', true)->first()?->name ?? 'None',
            'grade_ranges' => GradingScale::active()->count(),
        ];

        return view('admin.grading-scales.index', compact('scales', 'stats', 'search', 'status'));
    }

    /**
     * Show the form for creating a new grading scale
     */
    public function create()
    {
        return view('admin.grading-scales.create');
    }

    /**
     * Store a newly created grading scale
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'min_score' => 'required|numeric|min:0|max:100',
            'max_score' => 'required|numeric|min:0|max:100|gte:min_score',
            'letter_grade' => 'required|string|max:5',
            'gpa_points' => 'required|numeric|min:0|max:4',
            'description' => 'nullable|string',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            // If setting as default, unset other defaults
            if ($request->boolean('is_default')) {
                GradingScale::where('is_default', true)->update(['is_default' => false]);
            }

            $scale = GradingScale::create([
                'name' => $request->name,
                'min_score' => $request->min_score,
                'max_score' => $request->max_score,
                'letter_grade' => $request->letter_grade,
                'gpa_points' => $request->gpa_points,
                'description' => $request->description,
                'is_default' => $request->boolean('is_default', false),
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::log(
                'created_grading_scale',
                "Created grading scale: {$scale->letter_grade} ({$scale->min_score}-{$scale->max_score}%)",
                'App\Models\GradingScale',
                $scale->id,
                [
                    'name' => $scale->name,
                    'letter_grade' => $scale->letter_grade,
                    'min_score' => $scale->min_score,
                    'max_score' => $scale->max_score,
                    'gpa_points' => $scale->gpa_points,
                    'is_default' => $scale->is_default,
                ]
            );

            DB::commit();

            return redirect()->route('admin.grading-scales.index')
                ->with('success', 'Grading scale created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Failed to create grading scale. Please try again.');
        }
    }

    /**
     * Display the specified grading scale
     */
    public function show(GradingScale $gradingScale)
    {
        return view('admin.grading-scales.show', compact('gradingScale'));
    }

    /**
     * Show the form for editing the specified grading scale
     */
    public function edit(GradingScale $gradingScale)
    {
        return view('admin.grading-scales.edit', compact('gradingScale'));
    }

    /**
     * Update the specified grading scale
     */
    public function update(Request $request, GradingScale $gradingScale)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'min_score' => 'required|numeric|min:0|max:100',
            'max_score' => 'required|numeric|min:0|max:100|gte:min_score',
            'letter_grade' => 'required|string|max:5',
            'gpa_points' => 'required|numeric|min:0|max:4',
            'description' => 'nullable|string',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            // Store old data for logging
            $oldData = $gradingScale->toArray();

            // If setting as default, unset other defaults
            if ($request->boolean('is_default') && !$gradingScale->is_default) {
                GradingScale::where('is_default', true)->update(['is_default' => false]);
            }

            $gradingScale->update([
                'name' => $request->name,
                'min_score' => $request->min_score,
                'max_score' => $request->max_score,
                'letter_grade' => $request->letter_grade,
                'gpa_points' => $request->gpa_points,
                'description' => $request->description,
                'is_default' => $request->boolean('is_default', false),
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::log(
                'updated_grading_scale',
                "Updated grading scale: {$gradingScale->letter_grade} ({$gradingScale->min_score}-{$gradingScale->max_score}%)",
                'App\Models\GradingScale',
                $gradingScale->id,
                [
                    'name' => $gradingScale->name,
                    'letter_grade' => $gradingScale->letter_grade,
                    'old_min_score' => $oldData['min_score'],
                    'new_min_score' => $gradingScale->min_score,
                    'old_max_score' => $oldData['max_score'],
                    'new_max_score' => $gradingScale->max_score,
                    'old_gpa_points' => $oldData['gpa_points'],
                    'new_gpa_points' => $gradingScale->gpa_points,
                    'old_default' => $oldData['is_default'],
                    'new_default' => $gradingScale->is_default,
                ]
            );

            DB::commit();

            return redirect()->route('admin.grading-scales.index')
                ->with('success', 'Grading scale updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Failed to update grading scale. Please try again.');
        }
    }

    /**
     * Remove the specified grading scale
     */
    public function destroy(GradingScale $gradingScale)
    {
        try {
            DB::beginTransaction();

            // Prevent deletion of default scale
            if ($gradingScale->is_default) {
                return back()->with('error', 'Cannot delete the default grading scale.');
            }

            // Store scale info for logging before deletion
            $scaleInfo = [
                'name' => $gradingScale->name,
                'letter_grade' => $gradingScale->letter_grade,
                'min_score' => $gradingScale->min_score,
                'max_score' => $gradingScale->max_score,
                'gpa_points' => $gradingScale->gpa_points,
            ];

            $gradingScale->delete();

            // Log activity
            ActivityLog::log(
                'deleted_grading_scale',
                "Deleted grading scale: {$scaleInfo['letter_grade']} ({$scaleInfo['min_score']}-{$scaleInfo['max_score']}%)",
                'App\Models\GradingScale',
                null,
                $scaleInfo
            );

            DB::commit();

            return redirect()->route('admin.grading-scales.index')
                ->with('success', 'Grading scale deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to delete grading scale. Please try again.');
        }
    }

    /**
     * Set grading scale as default
     */
    public function setDefault(GradingScale $gradingScale)
    {
        try {
            DB::beginTransaction();

            // Unset current default
            GradingScale::where('is_default', true)->update(['is_default' => false]);
            
            // Set new default
            $gradingScale->update(['is_default' => true, 'is_active' => true]);

            // Log activity
            ActivityLog::log(
                'set_default_grading_scale',
                "Set default grading scale: {$gradingScale->letter_grade} ({$gradingScale->min_score}-{$gradingScale->max_score}%)",
                'App\Models\GradingScale',
                $gradingScale->id,
                [
                    'name' => $gradingScale->name,
                    'letter_grade' => $gradingScale->letter_grade,
                    'min_score' => $gradingScale->min_score,
                    'max_score' => $gradingScale->max_score,
                ]
            );

            DB::commit();

            return back()->with('success', 'Default grading scale updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to set default grading scale. Please try again.');
        }
    }
}
