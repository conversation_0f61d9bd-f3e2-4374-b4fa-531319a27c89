<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Student;
use App\Models\TeacherAttendance;
use App\Models\AttendanceSettings;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $teacher = auth()->user()->teacher;
        $today = Carbon::today()->format('Y-m-d');

        $stats = [
            'my_invoices' => Invoice::where('created_by', auth()->id())->count(),
            'pending_invoices' => Invoice::where('created_by', auth()->id())->where('status', 'sent')->count(),
            'paid_invoices' => Invoice::where('created_by', auth()->id())->where('status', 'paid')->count(),
            'total_students' => Student::count(), // Can be filtered by teacher's classes later
        ];

        $recent_invoices = Invoice::with(['student.user'])
            ->where('created_by', auth()->id())
            ->latest()
            ->take(5)
            ->get();

        // Get teacher's attendance for today
        $todayAttendance = TeacherAttendance::where('teacher_id', $teacher->id)
            ->where('date', $today)
            ->first();

        // Get teacher's attendance statistics for current month
        $attendanceStats = TeacherAttendance::getTeacherStats(
            $teacher->id,
            Carbon::now()->startOfMonth()->format('Y-m-d'),
            Carbon::now()->format('Y-m-d')
        );

        // Get recent attendance history
        $recentAttendance = TeacherAttendance::where('teacher_id', $teacher->id)
            ->orderBy('date', 'desc')
            ->take(7)
            ->get();

        return view('teacher.dashboard', compact(
            'stats',
            'recent_invoices',
            'teacher',
            'todayAttendance',
            'attendanceStats',
            'recentAttendance',
            'today'
        ));
    }
}
