<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Section extends Model
{
    use HasFactory;

    protected $fillable = [
        'class_id',
        'name',
        'capacity',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'capacity' => 'integer',
    ];

    /**
     * Get the class that owns this section
     */
    public function class()
    {
        return $this->belongsTo(ClassModel::class, 'class_id');
    }

    /**
     * Get the students in this section through enrollments
     */
    public function students()
    {
        return $this->belongsToMany(Student::class, 'student_enrollments')
                    ->withPivot('class_id', 'enrollment_date', 'end_date', 'is_active')
                    ->withTimestamps();
    }

    /**
     * Get active students in this section
     */
    public function activeStudents()
    {
        return $this->students()->wherePivot('is_active', true);
    }

    /**
     * Get the teachers assigned to this section
     */
    public function teachers()
    {
        return $this->belongsToMany(Teacher::class, 'teacher_subjects', 'section_id', 'teacher_id')
                    ->withPivot('subject_id', 'class_id', 'is_primary')
                    ->withTimestamps();
    }

    /**
     * Scope to filter active sections
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by class
     */
    public function scopeForClass($query, $classId)
    {
        return $query->where('class_id', $classId);
    }

    /**
     * Get the status badge color
     */
    public function getStatusBadgeColorAttribute()
    {
        return $this->is_active ? 'green' : 'red';
    }

    /**
     * Get the status display text
     */
    public function getStatusDisplayAttribute()
    {
        return $this->is_active ? 'Active' : 'Inactive';
    }

    /**
     * Get current enrollment count
     */
    public function getCurrentEnrollmentAttribute()
    {
        return $this->students()->count();
    }

    /**
     * Get available spots
     */
    public function getAvailableSpotsAttribute()
    {
        return max(0, $this->capacity - $this->current_enrollment);
    }

    /**
     * Check if section is full
     */
    public function getIsFullAttribute()
    {
        return $this->current_enrollment >= $this->capacity;
    }

    /**
     * Get enrollment percentage
     */
    public function getEnrollmentPercentageAttribute()
    {
        if ($this->capacity == 0) {
            return 0;
        }
        
        return round(($this->current_enrollment / $this->capacity) * 100, 1);
    }

    /**
     * Get full section name (Class + Section)
     */
    public function getFullNameAttribute()
    {
        return $this->class->name . ' - ' . $this->name;
    }
}
