<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GradeCategory;
use App\Models\Subject;
use App\Models\AcademicTerm;

class GradeCategorySeeder extends Seeder
{
    public function run()
    {
        // Get first subject and academic term for seeding
        $subject = Subject::first();
        $academicTerm = AcademicTerm::first();

        if (!$subject || !$academicTerm) {
            $this->command->info('No subjects or academic terms found. Please seed subjects and academic terms first.');
            return;
        }

        $categories = [
            [
                'name' => 'Assignments',
                'description' => 'Regular homework and class assignments',
                'weight' => 30.00,
                'subject_id' => $subject->id,
                'academic_term_id' => $academicTerm->id,
                'is_active' => true,
            ],
            [
                'name' => 'Quizzes',
                'description' => 'Short quizzes and pop tests',
                'weight' => 20.00,
                'subject_id' => $subject->id,
                'academic_term_id' => $academicTerm->id,
                'is_active' => true,
            ],
            [
                'name' => 'Tests',
                'description' => 'Unit tests and chapter tests',
                'weight' => 25.00,
                'subject_id' => $subject->id,
                'academic_term_id' => $academicTerm->id,
                'is_active' => true,
            ],
            [
                'name' => 'Final Exam',
                'description' => 'Final examination',
                'weight' => 20.00,
                'subject_id' => $subject->id,
                'academic_term_id' => $academicTerm->id,
                'is_active' => true,
            ],
            [
                'name' => 'Participation',
                'description' => 'Class participation and engagement',
                'weight' => 5.00,
                'subject_id' => $subject->id,
                'academic_term_id' => $academicTerm->id,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            GradeCategory::create($category);
        }

        $this->command->info('Grade categories seeded successfully.');
    }
}
