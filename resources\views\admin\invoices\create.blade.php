@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Create Invoice"
        description="Create a new invoice for a student"
        :back-route="route('admin.invoices.index')"
        back-label="Back to Invoices">
    </x-page-header>

    <form method="POST" action="{{ route('admin.invoices.store') }}" class="space-y-6">
        @csrf
        
        <!-- Basic Information -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Basic Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Student Selection -->
                <div>
                    <label for="student_id" class="block text-sm font-medium text-gray-700 mb-2">Student</label>
                    <select name="student_id" id="student_id" class="form-input @error('student_id') border-red-500 @enderror" required>
                        <option value="">Select a student</option>
                        @foreach($students as $student)
                            <option value="{{ $student->id }}" {{ old('student_id') == $student->id ? 'selected' : '' }}>
                                {{ $student->user->name }} ({{ $student->student_id }})
                            </option>
                        @endforeach
                    </select>
                    @error('student_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Invoice Type -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Invoice Type</label>
                    <select name="type" id="type" class="form-input @error('type') border-red-500 @enderror" required>
                        <option value="">Select type</option>
                        @foreach($invoiceTypes as $key => $label)
                            <option value="{{ $key }}" {{ old('type') == $key ? 'selected' : '' }}>{{ $label }}</option>
                        @endforeach
                    </select>
                    @error('type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Title -->
                <div class="md:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Invoice Title</label>
                    <input type="text" name="title" id="title" value="{{ old('title') }}" 
                           class="form-input @error('title') border-red-500 @enderror" 
                           placeholder="e.g., Monthly Tuition Fee - January 2024" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea name="description" id="description" rows="3" 
                              class="form-input @error('description') border-red-500 @enderror"
                              placeholder="Additional details about this invoice...">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Amount and Due Date -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Amount & Due Date</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Amount -->
                <div>
                    <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">Amount (RM)</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">RM</span>
                        </div>
                        <input type="number" name="amount" id="amount" value="{{ old('amount') }}" 
                               step="0.01" min="0" class="form-input pl-12 @error('amount') border-red-500 @enderror" 
                               placeholder="0.00" required>
                    </div>
                    @error('amount')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Discount -->
                <div>
                    <label for="discount" class="block text-sm font-medium text-gray-700 mb-2">Discount (RM)</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">RM</span>
                        </div>
                        <input type="number" name="discount" id="discount" value="{{ old('discount', 0) }}" 
                               step="0.01" min="0" class="form-input pl-12 @error('discount') border-red-500 @enderror" 
                               placeholder="0.00">
                    </div>
                    @error('discount')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Due Date -->
                <div>
                    <label for="due_date" class="block text-sm font-medium text-gray-700 mb-2">Due Date</label>
                    <input type="date" name="due_date" id="due_date" value="{{ old('due_date', date('Y-m-d', strtotime('+1 month'))) }}"
                           class="form-input @error('due_date') border-red-500 @enderror"
                           min="{{ date('Y-m-d', strtotime('+1 day')) }}" required>
                    @error('due_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Add Line Item Button -->
            <div class="mt-6">
                <button type="button" id="add-line-item" class="btn-secondary text-sm">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Line Item
                </button>
            </div>

            <!-- Line Items Container -->
            <div id="line-items-container" class="mt-6 space-y-4">
                <!-- Line items will be added here dynamically -->
            </div>

            <!-- Total Amount Display -->
            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                <div class="flex justify-between items-center">
                    <span class="text-lg font-medium text-gray-900">Total Amount:</span>
                    <span id="total-amount" class="text-xl font-bold text-blue-600">RM 0.00</span>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div></div> <!-- Empty div for spacing -->
                <div class="flex items-center space-x-4">
                    <a href="{{ route('admin.invoices.index') }}" class="btn-secondary">Cancel</a>
                    <button type="submit" class="btn-primary">Create Invoice</button>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('amount');
    const discountInput = document.getElementById('discount');
    const totalAmountDisplay = document.getElementById('total-amount');
    const addLineItemBtn = document.getElementById('add-line-item');
    const lineItemsContainer = document.getElementById('line-items-container');
    let lineItemCount = 0;

    // Calculate total amount
    function calculateTotal() {
        const amount = parseFloat(amountInput.value) || 0;
        const discount = parseFloat(discountInput.value) || 0;

        // Calculate line items total
        let lineItemsTotal = 0;
        const lineItemAmounts = document.querySelectorAll('input[name*="[amount]"]');
        lineItemAmounts.forEach(input => {
            lineItemsTotal += parseFloat(input.value) || 0;
        });

        const total = Math.max(0, amount + lineItemsTotal - discount);
        totalAmountDisplay.textContent = `RM ${total.toFixed(2)}`;
    }

    // Add event listeners for amount calculation
    amountInput.addEventListener('input', calculateTotal);
    discountInput.addEventListener('input', calculateTotal);

    // Add line item functionality
    addLineItemBtn.addEventListener('click', function() {
        const lineItemHtml = `
            <div class="line-item border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start mb-4">
                    <h4 class="text-sm font-medium text-gray-900">Line Item ${lineItemCount + 1}</h4>
                    <button type="button" class="remove-line-item text-red-600 hover:text-red-900">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <input type="text" name="line_items[${lineItemCount}][description]" 
                               class="form-input" placeholder="Item description">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Amount (RM)</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">RM</span>
                            </div>
                            <input type="number" name="line_items[${lineItemCount}][amount]" 
                                   step="0.01" min="0" class="form-input pl-12" placeholder="0.00">
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        lineItemsContainer.insertAdjacentHTML('beforeend', lineItemHtml);

        // Add event listener to the new amount input for total calculation
        const newAmountInput = lineItemsContainer.querySelector(`input[name="line_items[${lineItemCount}][amount]"]`);
        newAmountInput.addEventListener('input', calculateTotal);

        lineItemCount++;
    });

    // Remove line item functionality
    lineItemsContainer.addEventListener('click', function(e) {
        if (e.target.closest('.remove-line-item')) {
            e.target.closest('.line-item').remove();
            calculateTotal(); // Recalculate total after removing item
        }
    });

    // Initial calculation
    calculateTotal();
});
</script>
@endpush
@endsection
