@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Announcement Details"
        description="View announcement information"
        :back-route="route('admin.announcements.index')"
        back-label="Back to Announcements">
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.announcements.edit', $announcement) }}" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Announcement
            </a>
            <form method="POST" action="{{ route('admin.announcements.toggle-pin', $announcement) }}" class="inline">
                @csrf
                <button type="submit" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    {{ $announcement->is_pinned ? 'Unpin' : 'Pin' }} Announcement
                </button>
            </form>
        </div>
    </x-page-header>

    <!-- Announcement Details -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Announcement Information</h3>
                <div class="flex items-center space-x-2">
                    @if($announcement->is_pinned)
                        <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    @endif
                    <span class="badge badge-blue">
                        {{ ucfirst($announcement->type) }}
                    </span>
                    <span class="badge badge-purple">
                        {{ ucfirst($announcement->target_audience) }}
                    </span>
                    @if($announcement->is_published)
                        <span class="badge badge-green">Published</span>
                    @else
                        <span class="badge badge-gray">Draft</span>
                    @endif
                    @if($announcement->expire_date && $announcement->expire_date->isPast())
                        <span class="badge badge-red">Expired</span>
                    @endif
                </div>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700">Title</label>
                    <p class="mt-1 text-xl font-semibold text-gray-900">{{ $announcement->title }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Type</label>
                    <p class="mt-1 text-sm text-gray-900">{{ ucfirst($announcement->type) }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Target Audience</label>
                    <p class="mt-1 text-sm text-gray-900">{{ ucfirst($announcement->target_audience) }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Publish Date</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $announcement->publish_date->format('F d, Y') }}</p>
                </div>
                @if($announcement->expire_date)
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Expire Date</label>
                        <p class="mt-1 text-sm text-gray-900">
                            {{ $announcement->expire_date->format('F d, Y') }}
                            @if($announcement->expire_date->isPast())
                                <span class="text-red-600 font-medium">(Expired)</span>
                            @endif
                        </p>
                    </div>
                @endif
                <div>
                    <label class="block text-sm font-medium text-gray-700">Created by</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $announcement->creator->name ?? 'Unknown' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Created</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $announcement->created_at->format('F d, Y g:i A') }}</p>
                </div>
            </div>
            
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700">Content</label>
                <div class="mt-2 p-4 bg-gray-50 rounded-lg">
                    <div class="text-sm text-gray-900 prose max-w-none">
                        {!! nl2br(e($announcement->content)) !!}
                    </div>
                </div>
            </div>

            @if($announcement->target_classes && count($announcement->target_classes) > 0)
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700">Target Classes</label>
                    <div class="mt-2 flex flex-wrap gap-2">
                        @foreach($announcement->target_classes as $classId)
                            @php
                                $class = \App\Models\ClassModel::find($classId);
                            @endphp
                            @if($class)
                                <span class="badge badge-blue">{{ $class->name }}</span>
                            @endif
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Announcement Timeline -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Announcement Timeline</h3>
        </div>
        <div class="p-6">
            <div class="flow-root">
                <ul class="-mb-8">
                    <li>
                        <div class="relative pb-8">
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-3 w-3 rounded-full bg-blue-500"></span>
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                    <div>
                                        <p class="text-sm text-gray-500">Announcement created by <span class="font-medium text-gray-900">{{ $announcement->creator->name ?? 'Unknown' }}</span></p>
                                    </div>
                                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                        {{ $announcement->created_at->format('M d, Y g:i A') }}
                                    </div>
                                </div>
                            </div>
                            <span class="absolute top-1.5 left-1.5 -ml-px h-full w-0.5 border-l-2 border-dotted border-gray-300" aria-hidden="true"></span>
                        </div>
                    </li>
                    
                    @if($announcement->updated_at != $announcement->created_at)
                        <li>
                            <div class="relative pb-8">
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-3 w-3 rounded-full bg-green-500"></span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">Announcement updated</p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            {{ $announcement->updated_at->format('M d, Y g:i A') }}
                                        </div>
                                    </div>
                                </div>
                                <span class="absolute top-1.5 left-1.5 -ml-px h-full w-0.5 border-l-2 border-dotted border-gray-300" aria-hidden="true"></span>
                            </div>
                        </li>
                    @endif

                    <li>
                        <div class="relative pb-8">
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-3 w-3 rounded-full {{ $announcement->publish_date->isFuture() ? 'bg-yellow-500' : ($announcement->publish_date->isToday() ? 'bg-green-500' : 'bg-purple-500') }}"></span>
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                    <div>
                                        <p class="text-sm text-gray-500">
                                            Announcement
                                            @if($announcement->publish_date->isFuture())
                                                scheduled for publication
                                            @elseif($announcement->publish_date->isToday())
                                                published today
                                            @else
                                                published
                                            @endif
                                        </p>
                                    </div>
                                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                        {{ $announcement->publish_date->format('M d, Y') }}
                                    </div>
                                </div>
                            </div>
                            @if($announcement->expire_date)
                                <span class="absolute top-1.5 left-1.5 -ml-px h-full w-0.5 border-l-2 border-dotted border-gray-300" aria-hidden="true"></span>
                            @endif
                        </div>
                    </li>

                    @if($announcement->expire_date)
                        <li>
                            <div class="relative">
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-3 w-3 rounded-full {{ $announcement->expire_date->isFuture() ? 'bg-orange-500' : 'bg-red-500' }}"></span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">
                                                Announcement
                                                @if($announcement->expire_date->isFuture())
                                                    will expire
                                                @else
                                                    expired
                                                @endif
                                            </p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            {{ $announcement->expire_date->format('M d, Y') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    @endif
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection
