@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Schedule Details"
        description="View schedule information and details"
        :back-route="route('admin.schedules.index')"
        back-label="Back to Schedules">
        
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.schedules.edit', $schedule) }}" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Schedule
            </a>
        </div>
    </x-page-header>

    <!-- Schedule Details -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-indigo-500 to-indigo-600">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-6">
                    <h1 class="text-2xl font-bold text-white">{{ $schedule->subject->name }}</h1>
                    <p class="text-indigo-100">{{ $schedule->class->name }} - {{ $schedule->section->name }}</p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="badge badge-blue">
                            {{ ucfirst($schedule->day_of_week) }}
                        </span>
                        <span class="badge badge-green">
                            {{ \Carbon\Carbon::parse($schedule->timeSlot->start_time)->format('g:i A') }} - 
                            {{ \Carbon\Carbon::parse($schedule->timeSlot->end_time)->format('g:i A') }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="px-6 py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Class Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Class Information</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Academic Year</dt>
                            <dd class="text-sm text-gray-900">{{ $schedule->academicYear->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Academic Term</dt>
                            <dd class="text-sm text-gray-900">{{ $schedule->academicTerm->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Class</dt>
                            <dd class="text-sm text-gray-900">{{ $schedule->class->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Section</dt>
                            <dd class="text-sm text-gray-900">{{ $schedule->section->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Subject</dt>
                            <dd class="text-sm text-gray-900">
                                {{ $schedule->subject->name }}
                                <span class="text-gray-500">({{ $schedule->subject->code }})</span>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Teacher</dt>
                            <dd class="text-sm text-gray-900">{{ $schedule->teacher->name }}</dd>
                        </div>
                    </dl>
                </div>

                <!-- Schedule Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Schedule Information</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Day of Week</dt>
                            <dd class="text-sm text-gray-900">{{ ucfirst($schedule->day_of_week) }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Time Slot</dt>
                            <dd class="text-sm text-gray-900">{{ $schedule->timeSlot->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Start Time</dt>
                            <dd class="text-sm text-gray-900">{{ \Carbon\Carbon::parse($schedule->timeSlot->start_time)->format('g:i A') }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">End Time</dt>
                            <dd class="text-sm text-gray-900">{{ \Carbon\Carbon::parse($schedule->timeSlot->end_time)->format('g:i A') }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Duration</dt>
                            <dd class="text-sm text-gray-900">
                                {{ \Carbon\Carbon::parse($schedule->timeSlot->start_time)->diffInMinutes(\Carbon\Carbon::parse($schedule->timeSlot->end_time)) }} minutes
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Room Number</dt>
                            <dd class="text-sm text-gray-900">{{ $schedule->room_number ?: 'Not assigned' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="text-sm text-gray-900">
                                <span class="badge {{ $schedule->is_active ? 'badge-green' : 'badge-red' }}">
                                    {{ $schedule->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Subject Details -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Subject Details</h3>
        </div>
        
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Subject Name</dt>
                            <dd class="text-sm text-gray-900">{{ $schedule->subject->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Subject Code</dt>
                            <dd class="text-sm text-gray-900">{{ $schedule->subject->code }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Category</dt>
                            <dd class="text-sm text-gray-900">
                                <span class="badge badge-{{ $schedule->subject->category_badge_color }}">
                                    {{ $schedule->subject->category }}
                                </span>
                            </dd>
                        </div>
                    </dl>
                </div>
                <div>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Credits</dt>
                            <dd class="text-sm text-gray-900">{{ $schedule->subject->credits }}</dd>
                        </div>
                        @if($schedule->subject->description)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Description</dt>
                                <dd class="text-sm text-gray-900">{{ $schedule->subject->description }}</dd>
                            </div>
                        @endif
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Teacher Details -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Teacher Details</h3>
        </div>
        
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Name</dt>
                            <dd class="text-sm text-gray-900">{{ $schedule->teacher->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Email</dt>
                            <dd class="text-sm text-gray-900">{{ $schedule->teacher->email }}</dd>
                        </div>
                    </dl>
                </div>
                <div>
                    <dl class="space-y-3">
                        @if($schedule->teacher->phone)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Phone</dt>
                                <dd class="text-sm text-gray-900">{{ $schedule->teacher->phone }}</dd>
                            </div>
                        @endif
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Role</dt>
                            <dd class="text-sm text-gray-900">{{ ucfirst($schedule->teacher->role) }}</dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
