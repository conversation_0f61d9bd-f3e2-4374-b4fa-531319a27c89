<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Student;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class InvoiceController extends Controller
{
    /**
     * Display a listing of invoices
     */
    public function index(Request $request)
    {
        $query = Invoice::with(['student.user', 'creator', 'payments']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Search by invoice number, student name, or other fields
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('amount', 'like', "%{$search}%")
                  ->orWhere('total_amount', 'like', "%{$search}%")
                  ->orWhere('type', 'like', "%{$search}%")
                  ->orWhere('status', 'like', "%{$search}%")
                  ->orWhereHas('student.user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%")
                                ->orWhere('phone', 'like', "%{$search}%");
                  })
                  ->orWhereHas('student', function ($studentQuery) use ($search) {
                      $studentQuery->where('student_id', 'like', "%{$search}%")
                                   ->orWhere('class', 'like', "%{$search}%")
                                   ->orWhere('section', 'like', "%{$search}%");
                  });
            });
        }

        $invoices = $query->latest()->paginate(15)->withQueryString();

        $stats = [
            'total' => Invoice::count(),
            'draft' => Invoice::where('status', 'draft')->count(),
            'sent' => Invoice::where('status', 'sent')->count(),
            'paid' => Invoice::where('status', 'paid')->count(),
            'overdue' => Invoice::where('status', 'overdue')->count(),
            'cancelled' => Invoice::where('status', 'cancelled')->count(),
        ];

        return view('admin.invoices.index', compact('invoices', 'stats'));
    }

    /**
     * Show the form for creating a new invoice
     */
    public function create()
    {
        $students = Student::with('user')->get();
        $invoiceTypes = [
            'tuition' => 'Tuition Fee',
            'exam' => 'Exam Fee',
            'transport' => 'Transport Fee',
            'library' => 'Library Fee',
            'sports' => 'Sports Fee',
            'other' => 'Other'
        ];

        return view('admin.invoices.create', compact('students', 'invoiceTypes'));
    }

    /**
     * Store a newly created invoice
     */
    public function store(Request $request)
    {
        $request->validate([
            'student_id' => 'required|exists:students,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'due_date' => 'required|date|after:today',
            'type' => 'required|in:tuition,exam,transport,library,sports,other',
            'line_items' => 'nullable|array',
            'line_items.*.description' => 'required_with:line_items|string',
            'line_items.*.amount' => 'required_with:line_items|numeric|min:0',
        ]);

        // Calculate line items total
        $lineItemsTotal = 0;
        if ($request->line_items) {
            foreach ($request->line_items as $item) {
                $lineItemsTotal += floatval($item['amount'] ?? 0);
            }
        }

        $totalAmount = $request->amount + $lineItemsTotal - ($request->discount ?? 0);

        $invoice = Invoice::create([
            'invoice_number' => $this->generateInvoiceNumber(),
            'student_id' => $request->student_id,
            'created_by' => auth()->id(),
            'title' => $request->title,
            'description' => $request->description,
            'amount' => $request->amount,
            'discount' => $request->discount ?? 0,
            'total_amount' => $totalAmount,
            'due_date' => $request->due_date,
            'type' => $request->type,
            'line_items' => $request->line_items,
            'status' => 'draft',
        ]);

        return redirect()->route('admin.invoices.show', $invoice)
                        ->with('success', 'Invoice created successfully.');
    }

    /**
     * Display the specified invoice
     */
    public function show(Invoice $invoice)
    {
        $invoice->load(['student.user', 'creator', 'payments']);
        return view('admin.invoices.show', compact('invoice'));
    }

    /**
     * Show the form for editing the specified invoice
     */
    public function edit(Invoice $invoice)
    {
        // Only allow editing draft invoices
        if ($invoice->status !== 'draft') {
            return redirect()->route('admin.invoices.show', $invoice)
                            ->with('error', 'Only draft invoices can be edited.');
        }

        $students = Student::with('user')->get();
        $invoiceTypes = [
            'tuition' => 'Tuition Fee',
            'exam' => 'Exam Fee',
            'transport' => 'Transport Fee',
            'library' => 'Library Fee',
            'sports' => 'Sports Fee',
            'other' => 'Other'
        ];

        return view('admin.invoices.edit', compact('invoice', 'students', 'invoiceTypes'));
    }

    /**
     * Update the specified invoice
     */
    public function update(Request $request, Invoice $invoice)
    {
        // Only allow updating draft invoices
        if ($invoice->status !== 'draft') {
            return redirect()->route('admin.invoices.show', $invoice)
                            ->with('error', 'Only draft invoices can be updated.');
        }

        $request->validate([
            'student_id' => 'required|exists:students,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'due_date' => 'required|date|after:today',
            'type' => 'required|in:tuition,exam,transport,library,sports,other',
            'line_items' => 'nullable|array',
            'line_items.*.description' => 'required_with:line_items|string',
            'line_items.*.amount' => 'required_with:line_items|numeric|min:0',
        ]);

        $totalAmount = $request->amount - ($request->discount ?? 0);

        $invoice->update([
            'student_id' => $request->student_id,
            'title' => $request->title,
            'description' => $request->description,
            'amount' => $request->amount,
            'discount' => $request->discount ?? 0,
            'total_amount' => $totalAmount,
            'due_date' => $request->due_date,
            'type' => $request->type,
            'line_items' => $request->line_items,
        ]);

        return redirect()->route('admin.invoices.show', $invoice)
                        ->with('success', 'Invoice updated successfully.');
    }

    /**
     * Remove the specified invoice
     */
    public function destroy(Invoice $invoice)
    {
        // Only allow deleting draft invoices
        if ($invoice->status !== 'draft') {
            return redirect()->route('admin.invoices.index')
                            ->with('error', 'Only draft invoices can be deleted.');
        }

        $invoice->delete();

        return redirect()->route('admin.invoices.index')
                        ->with('success', 'Invoice deleted successfully.');
    }

    /**
     * Send invoice to guardian
     */
    public function send(Invoice $invoice)
    {
        if ($invoice->status !== 'draft') {
            return redirect()->route('admin.invoices.show', $invoice)
                            ->with('error', 'Only draft invoices can be sent.');
        }

        $invoice->update(['status' => 'sent']);

        // TODO: Send email notification to guardian

        return redirect()->route('admin.invoices.show', $invoice)
                        ->with('success', 'Invoice sent successfully.');
    }

    /**
     * Cancel invoice
     */
    public function cancel(Invoice $invoice)
    {
        if (!in_array($invoice->status, ['draft', 'sent'])) {
            return redirect()->route('admin.invoices.show', $invoice)
                            ->with('error', 'Invoice cannot be cancelled.');
        }

        $invoice->update(['status' => 'cancelled']);

        return redirect()->route('admin.invoices.show', $invoice)
                        ->with('success', 'Invoice cancelled successfully.');
    }

    /**
     * Generate unique invoice number
     */
    private function generateInvoiceNumber()
    {
        $prefix = 'INV-' . date('Y') . '-';
        $lastInvoice = Invoice::where('invoice_number', 'like', $prefix . '%')
                             ->orderBy('invoice_number', 'desc')
                             ->first();

        if ($lastInvoice) {
            $lastNumber = (int) substr($lastInvoice->invoice_number, strlen($prefix));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }
}
