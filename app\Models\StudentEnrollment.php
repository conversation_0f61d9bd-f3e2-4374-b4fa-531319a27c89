<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentEnrollment extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'class_id',
        'section_id',
        'enrollment_date',
        'end_date',
        'is_active',
    ];

    protected $casts = [
        'enrollment_date' => 'date',
        'end_date' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the student that owns this enrollment
     */
    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the class for this enrollment
     */
    public function class()
    {
        return $this->belongsTo(ClassModel::class, 'class_id');
    }

    /**
     * Get the section for this enrollment
     */
    public function section()
    {
        return $this->belongsTo(Section::class);
    }

    /**
     * Scope to filter active enrollments
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by academic year
     */
    public function scopeCurrentYear($query)
    {
        return $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
    }
}
