<?php

namespace App\Http\Controllers\Guardian;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Student;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $guardian = auth()->user()->guardian;
        $students = $guardian ? $guardian->students : collect();
        $studentIds = $students->pluck('id');

        $stats = [
            'my_children' => $students->count(),
            'pending_invoices' => Invoice::whereIn('student_id', $studentIds)->where('status', 'sent')->count(),
            'paid_invoices' => Invoice::whereIn('student_id', $studentIds)->where('status', 'paid')->count(),
            'overdue_invoices' => Invoice::whereIn('student_id', $studentIds)->where('status', 'overdue')->count(),
            'total_paid' => Payment::where('paid_by', auth()->id())->where('status', 'paid')->sum('amount'),
            'pending_amount' => Invoice::whereIn('student_id', $studentIds)->where('status', 'sent')->sum('total_amount'),
        ];

        $recent_invoices = Invoice::with(['student.user'])
            ->whereIn('student_id', $studentIds)
            ->latest()
            ->take(5)
            ->get();

        $recent_payments = Payment::with(['invoice.student.user'])
            ->where('paid_by', auth()->id())
            ->where('status', 'paid')
            ->latest()
            ->take(5)
            ->get();

        // Get attendance statistics for all children (current month)
        $childrenAttendance = [];
        foreach ($students as $student) {
            $attendanceStats = \App\Models\StudentAttendance::getStudentStats(
                $student->id,
                now()->startOfMonth()->format('Y-m-d'),
                now()->format('Y-m-d')
            );
            $childrenAttendance[$student->id] = $attendanceStats;
        }

        return view('guardian.dashboard', compact('stats', 'recent_invoices', 'recent_payments', 'students', 'guardian', 'childrenAttendance'));
    }
}
