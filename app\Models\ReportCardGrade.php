<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReportCardGrade extends Model
{
    use HasFactory;

    protected $fillable = [
        'report_card_id',
        'subject_id',
        'assignment_marks',
        'exam_marks',
        'total_marks',
        'percentage',
        'grade',
        'gpa_points',
        'remarks',
    ];

    protected $casts = [
        'assignment_marks' => 'decimal:2',
        'exam_marks' => 'decimal:2',
        'total_marks' => 'decimal:2',
        'percentage' => 'decimal:2',
        'gpa_points' => 'decimal:2',
    ];

    /**
     * Get the report card.
     */
    public function reportCard(): BelongsTo
    {
        return $this->belongsTo(ReportCard::class);
    }

    /**
     * Get the subject.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Check if the student passed this subject.
     */
    public function isPassed(): bool
    {
        return $this->percentage >= 40; // Assuming 40% is the pass mark
    }

    /**
     * Get grade color for display.
     */
    public function getGradeColorAttribute(): string
    {
        return match($this->grade) {
            'A+', 'A' => 'text-green-600',
            'B+', 'B' => 'text-blue-600',
            'C+', 'C' => 'text-yellow-600',
            'D' => 'text-orange-600',
            'F' => 'text-red-600',
            default => 'text-gray-600',
        };
    }
}
