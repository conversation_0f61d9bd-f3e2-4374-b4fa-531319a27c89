@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <x-page-header
        title="Class Schedules"
        description="Manage class schedules and timetables"
        :back-route="route('admin.academic-calendar.index')"
        back-label="Back to Calendar">
        
        <a href="{{ route('admin.schedules.create') }}" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Schedule
        </a>
    </x-page-header>

    <!-- Statistics Cards -->
    <div class="grid-stats-4">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-blue-500 text-white">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Schedules</dt>
                            <dd class="stat-card-value">{{ $schedules->total() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-green-500 text-white">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Classes Covered</dt>
                            <dd class="stat-card-value">{{ $allSchedules->pluck('class_id')->unique()->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-purple-500 text-white">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Subjects Scheduled</dt>
                            <dd class="stat-card-value">{{ $allSchedules->pluck('subject_id')->unique()->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-orange-500 text-white">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Teachers Assigned</dt>
                            <dd class="stat-card-value">{{ $allSchedules->pluck('teacher_id')->unique()->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6"
         x-data="{
             searchQuery: '',
             class: '',
             subject: '',
             day: '',
             showAdvanced: false
         }">

        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
            <!-- Search Input -->
            <div class="flex-1">
                <input
                    type="text"
                    x-model="searchQuery"
                    @input="filterSchedules()"
                    placeholder="Search schedules by class, subject, teacher, day, time, or room..."
                    class="form-input w-full"
                >
            </div>

            <!-- Filter Toggle and Clear Buttons -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                    <svg class="w-4 h-4 ml-1 transition-transform duration-200" :class="showAdvanced ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <!-- Clear All Filters -->
                <button
                    @click="clearAllFilters()"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition class="mt-6 pt-6 border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Class Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Class</label>
                    <select x-model="class" @change="filterSchedules()" class="form-select">
                        <option value="">All Classes</option>
                        @foreach($allSchedules->pluck('class')->unique('id') as $class)
                            <option value="{{ $class->id }}">{{ $class->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Subject Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                    <select x-model="subject" @change="filterSchedules()" class="form-select">
                        <option value="">All Subjects</option>
                        @foreach($allSchedules->pluck('subject')->unique('id') as $subject)
                            <option value="{{ $subject->id }}">{{ $subject->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Day Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Day</label>
                    <select x-model="day" @change="filterSchedules()" class="form-select">
                        <option value="">All Days</option>
                        <option value="monday">Monday</option>
                        <option value="tuesday">Tuesday</option>
                        <option value="wednesday">Wednesday</option>
                        <option value="thursday">Thursday</option>
                        <option value="friday">Friday</option>
                        <option value="saturday">Saturday</option>
                        <option value="sunday">Sunday</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedules Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Class Schedules</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class & Section</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Day & Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($schedules as $schedule)
                        <tr class="hover:bg-gray-50" data-filterable
                            data-search-text="{{ $schedule->class->name }} {{ $schedule->section->name }} {{ $schedule->subject->name }} {{ $schedule->subject->code }} {{ $schedule->teacher->name }} {{ $schedule->teacher->email }} {{ ucfirst($schedule->day_of_week) }} {{ \Carbon\Carbon::parse($schedule->timeSlot->start_time)->format('g:i A') }} {{ \Carbon\Carbon::parse($schedule->timeSlot->end_time)->format('g:i A') }} {{ $schedule->room_number }}"
                            data-class="{{ $schedule->class->id }}"
                            data-subject="{{ $schedule->subject->id }}"
                            data-day="{{ $schedule->day_of_week }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $schedule->class->name }}</div>
                                <div class="text-sm text-gray-500">{{ $schedule->section->name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $schedule->subject->name }}</div>
                                <div class="text-sm text-gray-500">{{ $schedule->subject->code }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $schedule->teacher->name }}</div>
                                <div class="text-sm text-gray-500">{{ $schedule->teacher->email }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ ucfirst($schedule->day_of_week) }}</div>
                                <div class="text-sm text-gray-500">
                                    {{ \Carbon\Carbon::parse($schedule->timeSlot->start_time)->format('g:i A') }} - 
                                    {{ \Carbon\Carbon::parse($schedule->timeSlot->end_time)->format('g:i A') }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $schedule->room_number ?: 'Not assigned' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <div class="flex items-center justify-center space-x-2">
                                    <a href="{{ route('admin.schedules.show', $schedule) }}"
                                       class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition-colors duration-200"
                                       title="View Schedule">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        View
                                    </a>
                                    <a href="{{ route('admin.schedules.edit', $schedule) }}"
                                       class="inline-flex items-center px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium rounded-md transition-colors duration-200"
                                       title="Edit Schedule">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        Edit
                                    </a>
                                    <form action="{{ route('admin.schedules.destroy', $schedule) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="inline-flex items-center px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded-md transition-colors duration-200"
                                                onclick="return confirm('Are you sure you want to delete this schedule?')"
                                                title="Delete Schedule">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                No schedules found.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($schedules->hasPages())
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $schedules->links() }}
            </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
// Schedule filtering functionality
function filterSchedules() {
    const searchQuery = document.querySelector('[x-model="searchQuery"]').value.toLowerCase();
    const classFilter = document.querySelector('[x-model="class"]').value;
    const subjectFilter = document.querySelector('[x-model="subject"]').value;
    const dayFilter = document.querySelector('[x-model="day"]').value;

    const schedules = document.querySelectorAll('[data-filterable]');
    let visibleCount = 0;

    schedules.forEach(schedule => {
        const searchText = schedule.getAttribute('data-search-text').toLowerCase();
        const scheduleClass = schedule.getAttribute('data-class');
        const scheduleSubject = schedule.getAttribute('data-subject');
        const scheduleDay = schedule.getAttribute('data-day');

        let isVisible = true;

        // Search filter
        if (searchQuery && !searchText.includes(searchQuery)) {
            isVisible = false;
        }

        // Class filter
        if (classFilter && scheduleClass !== classFilter) {
            isVisible = false;
        }

        // Subject filter
        if (subjectFilter && scheduleSubject !== subjectFilter) {
            isVisible = false;
        }

        // Day filter
        if (dayFilter && scheduleDay !== dayFilter) {
            isVisible = false;
        }

        if (isVisible) {
            schedule.style.display = '';
            visibleCount++;
        } else {
            schedule.style.display = 'none';
        }
    });

    // Update results count if needed
    console.log(`Showing ${visibleCount} of ${schedules.length} schedules`);
}

// Clear all filters
function clearAllFilters() {
    document.querySelector('[x-model="searchQuery"]').value = '';
    document.querySelector('[x-model="class"]').value = '';
    document.querySelector('[x-model="subject"]').value = '';
    document.querySelector('[x-model="day"]').value = '';

    // Show all schedules
    const schedules = document.querySelectorAll('[data-filterable]');
    schedules.forEach(schedule => {
        schedule.style.display = '';
    });
}
</script>
@endpush
@endsection
