<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Student Attendance Table
        Schema::create('student_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->foreignId('marked_by')->constrained('users')->onDelete('cascade'); // Teacher/Admin who marked
            $table->date('date');
            $table->enum('status', ['present', 'absent', 'late', 'excused', 'sick'])->default('present');
            $table->time('check_in_time')->nullable();
            $table->time('check_out_time')->nullable();
            $table->text('notes')->nullable();
            $table->string('class')->nullable(); // Class during attendance
            $table->string('section')->nullable(); // Section during attendance
            $table->timestamps();

            // Ensure one attendance record per student per date
            $table->unique(['student_id', 'date']);
            $table->index(['date', 'status']);
            $table->index(['student_id', 'date']);
        });

        // Teacher Attendance Table
        Schema::create('teacher_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('teacher_id')->constrained()->onDelete('cascade');
            $table->foreignId('marked_by')->constrained('users')->onDelete('cascade'); // Admin who marked
            $table->date('date');
            $table->enum('status', ['present', 'absent', 'late', 'sick_leave', 'personal_leave', 'official_duty'])->default('present');
            $table->time('check_in_time')->nullable();
            $table->time('check_out_time')->nullable();
            $table->decimal('hours_worked', 4, 2)->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            // Ensure one attendance record per teacher per date
            $table->unique(['teacher_id', 'date']);
            $table->index(['date', 'status']);
            $table->index(['teacher_id', 'date']);
        });

        // Attendance Settings Table
        Schema::create('attendance_settings', function (Blueprint $table) {
            $table->id();
            $table->time('school_start_time')->default('08:00:00');
            $table->time('school_end_time')->default('15:00:00');
            $table->time('late_threshold')->default('08:15:00'); // After this time, mark as late
            $table->integer('grace_period_minutes')->default(15); // Grace period for late marking
            $table->boolean('auto_mark_absent')->default(true); // Auto mark absent if not marked by end of day
            $table->json('working_days')->default('["monday", "tuesday", "wednesday", "thursday", "friday"]');
            $table->json('holidays')->nullable(); // Array of holiday dates
            $table->boolean('allow_retroactive_marking')->default(true); // Allow marking attendance for past dates
            $table->integer('retroactive_days_limit')->default(7); // How many days back can be marked
            $table->timestamps();
        });

        // Attendance Summary Table (for performance optimization)
        Schema::create('attendance_summaries', function (Blueprint $table) {
            $table->id();
            $table->string('attendee_type'); // 'student' or 'teacher'
            $table->unsignedBigInteger('attendee_id');
            $table->integer('year');
            $table->integer('month');
            $table->integer('total_days');
            $table->integer('present_days');
            $table->integer('absent_days');
            $table->integer('late_days');
            $table->integer('excused_days');
            $table->integer('sick_days');
            $table->decimal('attendance_percentage', 5, 2);
            $table->timestamps();

            $table->index(['attendee_type', 'attendee_id', 'year', 'month']);
            $table->unique(['attendee_type', 'attendee_id', 'year', 'month']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance_summaries');
        Schema::dropIfExists('attendance_settings');
        Schema::dropIfExists('teacher_attendances');
        Schema::dropIfExists('student_attendances');
    }
};
