@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Guardian Profile"
        description="View and manage guardian information"
        :back-route="route('admin.guardians.index')"
        back-label="Back to Guardians">
        
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.guardians.edit', $guardian) }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Guardian
            </a>
            
            @if($guardian->students->count() > 0)
                <a href="{{ route('admin.invoices.create', ['student_id' => $guardian->students->first()->id]) }}" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Create Invoice
                </a>
            @endif
        </div>
    </x-page-header>

    <!-- Guardian Profile Card -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-yellow-500 to-yellow-600">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-2xl">
                            {{ substr($guardian->user->name, 0, 2) }}
                        </span>
                    </div>
                </div>
                <div class="ml-6">
                    <h1 class="text-2xl font-bold text-white">{{ $guardian->user->name }}</h1>
                    <p class="text-yellow-100">{{ $guardian->relationship }}</p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="badge {{ $guardian->is_active ? 'badge-green' : 'badge-red' }}">
                            {{ $guardian->is_active ? 'Active' : 'Inactive' }}
                        </span>
                        @if($guardian->occupation)
                            <span class="text-yellow-100">{{ $guardian->occupation }}</span>
                        @endif
                        <span class="text-yellow-100">{{ $guardian->children_count }} {{ Str::plural('child', $guardian->children_count) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Invoices</dt>
                            <dd class="stat-card-value">{{ $financialStats['total_invoices'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Paid Invoices</dt>
                            <dd class="stat-card-value">{{ $financialStats['paid_invoices'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Pending</dt>
                            <dd class="stat-card-value">{{ $financialStats['pending_invoices'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-red-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Overdue</dt>
                            <dd class="stat-card-value">{{ $financialStats['overdue_invoices'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Personal & Professional Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Personal Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Personal Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Full Name</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $guardian->user->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Relationship</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $guardian->relationship }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $guardian->user->email }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Phone</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $guardian->user->phone ?: 'Not provided' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Emergency Contact</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $guardian->emergency_contact ?: 'Not provided' }}</p>
                    </div>
                    
                    @if($guardian->user->address)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Address</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $guardian->user->address }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Professional Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Professional Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Occupation</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $guardian->occupation ?: 'Not specified' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Workplace</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $guardian->workplace ?: 'Not specified' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Monthly Income</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $guardian->formatted_income }}</p>
                    </div>
                </div>
            </div>

            <!-- Children -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Children</h3>
                
                @if($guardian->students->count() > 0)
                    <div class="space-y-4">
                        @foreach($guardian->students as $student)
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                                            <span class="text-gray-600 font-medium text-sm">
                                                {{ substr($student->user->name, 0, 2) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">{{ $student->user->name }}</p>
                                        <p class="text-sm text-gray-500">{{ $student->student_id }} • {{ $student->class_section }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('admin.students.show', $student) }}" 
                                       class="text-blue-600 hover:text-blue-500 text-sm">
                                        View Profile
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-sm text-gray-500">No children assigned to this guardian.</p>
                @endif
            </div>

            <!-- Recent Invoices -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Recent Invoices</h3>
                        @if($guardian->students->count() > 0)
                            <a href="{{ route('admin.invoices.index', ['student_id' => $guardian->students->pluck('id')->implode(',')]) }}" class="text-sm text-blue-600 hover:text-blue-500">
                                View all invoices
                            </a>
                        @endif
                    </div>
                </div>
                
                @if($recentInvoices->count() > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($recentInvoices as $invoice)
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $invoice->invoice_number }}</p>
                                        <p class="text-sm text-gray-500">{{ $invoice->title }}</p>
                                        @if($invoice->student)
                                            <p class="text-xs text-gray-400">Student: {{ $invoice->student->user->name }}</p>
                                        @endif
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-medium text-gray-900">RM {{ number_format($invoice->total_amount, 2) }}</p>
                                        <span class="badge {{ $invoice->status === 'paid' ? 'badge-green' : ($invoice->status === 'overdue' ? 'badge-red' : 'badge-yellow') }}">
                                            {{ ucfirst($invoice->status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="px-6 py-8 text-center">
                        <p class="text-sm text-gray-500">No invoices found for this guardian's children.</p>
                    </div>
                @endif
            </div>

            <!-- Children's Academic Performance -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Children's Academic Performance</h3>
                        @if($guardian->students->count() > 0)
                            <a href="{{ route('admin.gradebook.index', ['student_id' => $guardian->students->pluck('id')->implode(',')]) }}" class="text-sm text-blue-600 hover:text-blue-500">
                                View all grades
                            </a>
                        @endif
                    </div>
                </div>

                <!-- Grade Statistics -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ $gradeStats['total_grades'] }}</div>
                            <div class="text-sm text-gray-500">Total Grades</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ number_format($gradeStats['average_percentage'], 1) }}%</div>
                            <div class="text-sm text-gray-500">Average Score</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">{{ number_format($gradeStats['highest_score'], 1) }}%</div>
                            <div class="text-sm text-gray-500">Highest Score</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-orange-600">{{ number_format($gradeStats['lowest_score'], 1) }}%</div>
                            <div class="text-sm text-gray-500">Lowest Score</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-red-600">{{ $gradeStats['grades_this_month'] }}</div>
                            <div class="text-sm text-gray-500">This Month</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-indigo-600">{{ $guardian->students->count() }}</div>
                            <div class="text-sm text-gray-500">Children</div>
                        </div>
                    </div>
                </div>

                <!-- Recent Grades -->
                @if($recentGrades->count() > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($recentGrades as $grade)
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex-1">
                                        <h5 class="text-sm font-medium text-gray-900">{{ $grade->assignment_name }}</h5>
                                        <p class="text-xs text-gray-500">{{ $grade->subject->name }} • {{ $grade->student->user->name }} • {{ $grade->teacher->user->name }}</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-medium text-gray-900">{{ $grade->score }}/{{ $grade->max_score }}</div>
                                        <div class="text-xs text-gray-500">{{ number_format($grade->percentage, 1) }}%</div>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <span class="badge
                                            @if($grade->letter_grade === 'A' || $grade->letter_grade === 'A+') badge-green
                                            @elseif($grade->letter_grade === 'B' || $grade->letter_grade === 'B+') badge-blue
                                            @elseif($grade->letter_grade === 'C' || $grade->letter_grade === 'C+') badge-yellow
                                            @elseif($grade->letter_grade === 'D' || $grade->letter_grade === 'D+') badge-orange
                                            @else badge-red
                                            @endif">
                                            {{ $grade->letter_grade }}
                                        </span>
                                        <span class="badge badge-blue">{{ $grade->gradeCategory->name }}</span>
                                        <span class="badge badge-gray">{{ $grade->academicTerm->name }}</span>
                                    </div>
                                    <span class="text-xs text-gray-500">{{ $grade->assignment_date->format('M d, Y') }}</span>
                                </div>
                                @if($grade->comments)
                                    <div class="mt-2 text-xs text-gray-600 bg-gray-50 rounded p-2">
                                        {{ $grade->comments }}
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="px-6 py-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No grades available</h3>
                        <p class="mt-1 text-sm text-gray-500">No published grades found for this guardian's children.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Financial Summary -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Financial Summary</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Paid:</span>
                        <span class="text-sm font-medium text-green-600">RM {{ number_format($financialStats['total_amount_paid'], 2) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Amount Due:</span>
                        <span class="text-sm font-medium text-red-600">RM {{ number_format($financialStats['total_amount_due'], 2) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Payment Rate:</span>
                        <span class="text-sm font-medium text-blue-600">
                            {{ $financialStats['total_invoices'] > 0 ? number_format(($financialStats['paid_invoices'] / $financialStats['total_invoices']) * 100, 1) : 0 }}%
                        </span>
                    </div>
                </div>
            </div>

            <!-- Academic Summary -->
            @if($guardian->students->count() > 0)
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Academic Summary</h3>

                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Total Grades:</span>
                            <span class="text-sm font-medium text-gray-900">{{ $gradeStats['total_grades'] }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Average Performance:</span>
                            <span class="text-sm font-medium text-blue-600">{{ number_format($gradeStats['average_percentage'], 1) }}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Best Performance:</span>
                            <span class="text-sm font-medium text-green-600">{{ number_format($gradeStats['highest_score'], 1) }}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Grades This Month:</span>
                            <span class="text-sm font-medium text-purple-600">{{ $gradeStats['grades_this_month'] }}</span>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Recent Payments -->
            @if($recentPayments->count() > 0)
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Payments</h3>
                    
                    <div class="space-y-3">
                        @foreach($recentPayments as $payment)
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">RM {{ number_format($payment->amount, 2) }}</p>
                                    <p class="text-xs text-gray-500">{{ $payment->created_at->format('M d, Y') }}</p>
                                </div>
                                <span class="badge badge-green">Paid</span>
                            </div>
                        @endforeach
                    </div>
                    
                    <div class="mt-4">
                        <a href="{{ route('admin.payments.index', ['paid_by' => $guardian->user_id]) }}" 
                           class="text-sm text-blue-600 hover:text-blue-500">
                            View all payments
                        </a>
                    </div>
                </div>
            @endif

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    @if($guardian->students->count() > 0)
                        <a href="{{ route('admin.invoices.create', ['student_id' => $guardian->students->first()->id]) }}" 
                           class="w-full btn-primary text-center">
                            Create Invoice
                        </a>
                    @endif
                    
                    <a href="{{ route('admin.guardians.edit', $guardian) }}" 
                       class="w-full btn-secondary text-center">
                        Edit Profile
                    </a>
                    
                    @if($guardian->is_active)
                        <button onclick="toggleGuardianStatus('{{ $guardian->id }}')" 
                                class="w-full bg-red-50 text-red-700 px-4 py-2 rounded-lg hover:bg-red-100 transition-colors text-center">
                            Deactivate Guardian
                        </button>
                    @else
                        <button onclick="toggleGuardianStatus('{{ $guardian->id }}')" 
                                class="w-full bg-green-50 text-green-700 px-4 py-2 rounded-lg hover:bg-green-100 transition-colors text-center">
                            Activate Guardian
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
async function toggleGuardianStatus(guardianId) {
    const action = {{ $guardian->is_active ? 'false' : 'true' }};
    const actionText = action ? 'activate' : 'deactivate';
    
    const confirmed = await confirmAction(
        `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} Guardian`,
        `Are you sure you want to ${actionText} this guardian?`
    );

    if (confirmed) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ route('admin.guardians.toggle-status', $guardian) }}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection
