<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TimeSlot;
use Illuminate\Http\Request;

class TimeSlotController extends Controller
{
    public function index()
    {
        $timeSlots = TimeSlot::orderBy('sort_order')->orderBy('start_time')->paginate(10);
        return view('admin.time-slots.index', compact('timeSlots'));
    }

    public function create()
    {
        return view('admin.time-slots.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'type' => 'required|in:class,break,lunch,assembly',
            'sort_order' => 'required|integer|min:0',
        ]);

        TimeSlot::create($request->all());

        return redirect()->route('admin.time-slots.index')
                        ->with('success', 'Time slot created successfully.');
    }

    public function show(TimeSlot $timeSlot)
    {
        return view('admin.time-slots.show', compact('timeSlot'));
    }

    public function edit(TimeSlot $timeSlot)
    {
        return view('admin.time-slots.edit', compact('timeSlot'));
    }

    public function update(Request $request, TimeSlot $timeSlot)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'type' => 'required|in:class,break,lunch,assembly',
            'sort_order' => 'required|integer|min:0',
        ]);

        $timeSlot->update($request->all());

        return redirect()->route('admin.time-slots.index')
                        ->with('success', 'Time slot updated successfully.');
    }

    public function destroy(TimeSlot $timeSlot)
    {
        // Check if time slot is being used in schedules
        if ($timeSlot->schedules()->exists()) {
            return redirect()->route('admin.time-slots.index')
                           ->with('error', 'Cannot delete time slot that is being used in schedules.');
        }

        $timeSlot->delete();

        return redirect()->route('admin.time-slots.index')
                        ->with('success', 'Time slot deleted successfully.');
    }
}
