<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AcademicYear extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'start_date',
        'end_date',
        'is_current',
        'is_active',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_current' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the academic terms for this year.
     */
    public function academicTerms(): HasMany
    {
        return $this->hasMany(AcademicTerm::class)->orderBy('sort_order');
    }

    /**
     * Get the schedules for this academic year.
     */
    public function schedules(): HasMany
    {
        return $this->hasMany(Schedule::class);
    }

    /**
     * Get the current academic year.
     */
    public static function current()
    {
        return static::where('is_current', true)->first();
    }

    /**
     * Set this year as current and unset others.
     */
    public function setCurrent()
    {
        static::where('is_current', true)->update(['is_current' => false]);
        $this->update(['is_current' => true]);
    }

    /**
     * Check if this year is active.
     */
    public function isActive(): bool
    {
        return $this->is_active && 
               $this->start_date <= now() && 
               $this->end_date >= now();
    }

    /**
     * Get formatted name with dates.
     */
    public function getFullNameAttribute(): string
    {
        return $this->name . ' (' . $this->start_date->format('M Y') . ' - ' . $this->end_date->format('M Y') . ')';
    }
}
