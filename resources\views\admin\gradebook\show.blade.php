@extends('layouts.app')

@section('title', 'Grade Details')

@section('content')
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Grade Details</h1>
                <p class="text-gray-600">View grade information</p>
            </div>
            <a href="{{ route('admin.gradebook.index') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                Back to Gradebook
            </a>
        </div>

        <!-- Grade Information Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">{{ $gradebook->assignment_name }}</h2>
                <p class="text-sm text-gray-600">{{ $gradebook->description ?? 'No description provided' }}</p>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Student Information -->
                    <div class="space-y-4">
                        <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Student Information</h3>
                        <div class="space-y-2">
                            <div>
                                <span class="text-sm text-gray-500">Name:</span>
                                <p class="font-medium">{{ $gradebook->student->user->name }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Student ID:</span>
                                <p class="font-medium">{{ $gradebook->student->student_id }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Class:</span>
                                <p class="font-medium">{{ $gradebook->student->class_section }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Academic Information -->
                    <div class="space-y-4">
                        <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Academic Information</h3>
                        <div class="space-y-2">
                            <div>
                                <span class="text-sm text-gray-500">Subject:</span>
                                <p class="font-medium">{{ $gradebook->subject->name }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Teacher:</span>
                                <p class="font-medium">{{ $gradebook->teacher->user->name }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Category:</span>
                                <p class="font-medium">{{ $gradebook->gradeCategory->name }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Term:</span>
                                <p class="font-medium">{{ $gradebook->academicTerm->name }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Grade Information -->
                    <div class="space-y-4">
                        <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Grade Information</h3>
                        <div class="space-y-2">
                            <div>
                                <span class="text-sm text-gray-500">Score:</span>
                                <p class="font-medium">{{ $gradebook->score }} / {{ $gradebook->max_score }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Percentage:</span>
                                <p class="font-medium">{{ number_format($gradebook->percentage, 2) }}%</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Letter Grade:</span>
                                <span class="badge
                                    @if($gradebook->letter_grade === 'A' || $gradebook->letter_grade === 'A+') badge-green
                                    @elseif($gradebook->letter_grade === 'B' || $gradebook->letter_grade === 'B+') badge-blue
                                    @elseif($gradebook->letter_grade === 'C' || $gradebook->letter_grade === 'C+') badge-yellow
                                    @elseif($gradebook->letter_grade === 'D' || $gradebook->letter_grade === 'D+') badge-orange
                                    @else badge-red
                                    @endif">
                                    {{ $gradebook->letter_grade }}
                                </span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">GPA Points:</span>
                                <p class="font-medium">{{ number_format($gradebook->gpa_points, 2) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dates and Status -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div>
                            <span class="text-sm text-gray-500">Assignment Date:</span>
                            <p class="font-medium">{{ $gradebook->assignment_date ? $gradebook->assignment_date->format('M d, Y') : 'Not set' }}</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">Due Date:</span>
                            <p class="font-medium">{{ $gradebook->due_date ? $gradebook->due_date->format('M d, Y') : 'Not set' }}</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">Status:</span>
                            <span class="badge {{ $gradebook->is_published ? 'badge-green' : 'badge-gray' }}">
                                {{ $gradebook->is_published ? 'Published' : 'Draft' }}
                            </span>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">Created:</span>
                            <p class="font-medium">{{ $gradebook->created_at->format('M d, Y') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Comments -->
                @if($gradebook->comments)
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-2">Comments</h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-700">{{ $gradebook->comments }}</p>
                    </div>
                </div>
                @endif

                <!-- Actions -->
                <div class="mt-6 pt-6 border-t border-gray-200 flex justify-end space-x-3">
                    <a href="{{ route('admin.gradebook.edit', $gradebook) }}"
                       class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        Edit Grade
                    </a>
                    <form action="{{ route('admin.gradebook.destroy', $gradebook) }}" method="POST" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit"
                                onclick="return confirm('Are you sure you want to delete this grade?')"
                                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            Delete Grade
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
