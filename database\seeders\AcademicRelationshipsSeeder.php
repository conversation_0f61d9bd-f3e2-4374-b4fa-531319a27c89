<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\ClassModel;
use App\Models\Section;
use App\Models\Subject;
use App\Models\StudentEnrollment;
use App\Models\StudentSubject;
use App\Models\TeacherSubject;
use Carbon\Carbon;

class AcademicRelationshipsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating academic relationships...');

        // Get all students, classes, sections, subjects, and teachers
        $students = Student::all();
        $classes = ClassModel::all();
        $sections = Section::all();
        $subjects = Subject::all();
        $teachers = Teacher::all();

        if ($students->isEmpty() || $classes->isEmpty() || $sections->isEmpty() || $subjects->isEmpty() || $teachers->isEmpty()) {
            $this->command->warn('Missing required data. Please run ExtendedSchoolDataSeeder first.');
            return;
        }

        // 1. Enroll students in classes and sections
        $this->command->info('Enrolling students in classes and sections...');
        foreach ($students as $student) {
            // Randomly assign student to a class and section
            $class = $classes->random();
            $section = $class->sections()->inRandomOrder()->first();
            
            if ($section) {
                StudentEnrollment::create([
                    'student_id' => $student->id,
                    'class_id' => $class->id,
                    'section_id' => $section->id,
                    'enrollment_date' => Carbon::now()->subMonths(rand(1, 6)),
                    'is_active' => true,
                ]);
            }
        }

        // 2. Assign teachers to subjects and classes
        $this->command->info('Assigning teachers to subjects and classes...');
        foreach ($teachers as $teacher) {
            // Each teacher gets 2-4 subject assignments
            $assignmentCount = rand(2, 4);
            $assignedSubjects = $subjects->random($assignmentCount);
            
            foreach ($assignedSubjects as $subject) {
                // Assign to 1-2 classes
                $classCount = rand(1, 2);
                $assignedClasses = $classes->random($classCount);
                
                foreach ($assignedClasses as $class) {
                    // Assign to 1-2 sections in the class
                    $classSections = $class->sections;
                    if ($classSections->isNotEmpty()) {
                        $sectionCount = rand(1, min(2, $classSections->count()));
                        $assignedSections = $classSections->random($sectionCount);
                        
                        foreach ($assignedSections as $section) {
                            TeacherSubject::create([
                                'teacher_id' => $teacher->id,
                                'subject_id' => $subject->id,
                                'class_id' => $class->id,
                                'section_id' => $section->id,
                                'is_primary' => rand(0, 1) == 1, // 50% chance of being primary
                            ]);
                        }
                    }
                }
            }
        }

        // 3. Enroll students in subjects (mandatory + elective)
        $this->command->info('Enrolling students in subjects...');
        $enrollments = StudentEnrollment::with(['class.subjects', 'section'])->get();
        
        foreach ($enrollments as $enrollment) {
            $student = $enrollment->student;
            $class = $enrollment->class;
            $section = $enrollment->section;
            
            // Enroll in all mandatory subjects for the class
            $mandatorySubjects = $class->subjects()->wherePivot('is_mandatory', true)->get();
            foreach ($mandatorySubjects as $subject) {
                StudentSubject::create([
                    'student_id' => $student->id,
                    'subject_id' => $subject->id,
                    'class_id' => $class->id,
                    'section_id' => $section->id,
                    'enrollment_date' => $enrollment->enrollment_date,
                    'is_active' => true,
                ]);
            }
            
            // Enroll in 1-2 elective subjects
            $electiveSubjects = $class->subjects()->wherePivot('is_mandatory', false)->get();
            if ($electiveSubjects->isNotEmpty()) {
                $electiveCount = rand(1, min(2, $electiveSubjects->count()));
                $selectedElectives = $electiveSubjects->random($electiveCount);
                
                foreach ($selectedElectives as $subject) {
                    StudentSubject::create([
                        'student_id' => $student->id,
                        'subject_id' => $subject->id,
                        'class_id' => $class->id,
                        'section_id' => $section->id,
                        'enrollment_date' => $enrollment->enrollment_date,
                        'is_active' => true,
                    ]);
                }
            }
        }

        $this->command->info('Academic relationships created successfully!');
        $this->command->info('- Student enrollments: ' . StudentEnrollment::count());
        $this->command->info('- Teacher assignments: ' . TeacherSubject::count());
        $this->command->info('- Student subject enrollments: ' . StudentSubject::count());
    }
}
