<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('grading_scales', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "Standard A-F Scale"
            $table->decimal('min_score', 5, 2);
            $table->decimal('max_score', 5, 2);
            $table->string('letter_grade', 5); // A+, A, B+, etc.
            $table->decimal('gpa_points', 3, 2); // 4.0, 3.7, etc.
            $table->string('description')->nullable();
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['min_score', 'max_score']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('grading_scales');
    }
};
