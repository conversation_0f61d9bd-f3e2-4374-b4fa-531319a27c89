<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Invoice;
use App\Services\BillplzService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BillplzWebhookController extends Controller
{
    protected $billplzService;

    public function __construct(BillplzService $billplzService)
    {
        $this->billplzService = $billplzService;
    }

    /**
     * Handle Billplz webhook callback
     */
    public function handle(Request $request)
    {
        try {
            $data = $request->all();

            Log::info('Billplz webhook received', [
                'data' => $data,
                'headers' => $request->headers->all()
            ]);

            // For sandbox testing, skip signature verification
            $isSandbox = config('services.billplz.sandbox', true);

            if (!$isSandbox) {
                // Get the X-Signature header
                $signature = $request->header('X-Signature');

                if (!$signature) {
                    Log::warning('Billplz webhook received without X-Signature');
                    return response('Missing X-Signature', 400);
                }

                // Verify the signature
                if (!$this->billplzService->verifySignature($data, $signature)) {
                    Log::warning('Billplz webhook signature verification failed', [
                        'data' => $data,
                        'signature' => $signature
                    ]);
                    return response('Invalid signature', 400);
                }
            }

            // Find the payment by Billplz bill ID
            $payment = Payment::where('billplz_bill_id', $data['id'])->first();

            if (!$payment) {
                Log::warning('Payment not found for Billplz bill ID', [
                    'billplz_bill_id' => $data['id']
                ]);
                return response('Payment not found', 404);
            }

            // Update payment based on Billplz response
            $this->updatePaymentStatus($payment, $data);

            Log::info('Billplz webhook processed successfully', [
                'payment_id' => $payment->id,
                'billplz_bill_id' => $data['id'],
                'status' => $data['state']
            ]);

            return response('OK', 200);

        } catch (\Exception $e) {
            Log::error('Billplz webhook processing failed', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response('Internal Server Error', 500);
        }
    }

    /**
     * Handle payment return from Billplz
     */
    public function return(Request $request, Payment $payment)
    {
        try {
            Log::info('Payment return processing started', [
                'payment_id' => $payment->id,
                'current_status' => $payment->status,
                'request_data' => $request->all()
            ]);

            // Get latest bill information from Billplz
            if ($payment->billplz_bill_id) {
                $billplzInfo = $this->billplzService->getBill($payment->billplz_bill_id);

                Log::info('Retrieved Billplz bill info', [
                    'payment_id' => $payment->id,
                    'billplz_info' => $billplzInfo
                ]);

                if ($billplzInfo) {
                    $this->updatePaymentStatus($payment, $billplzInfo);
                    // Reload payment to get updated status
                    $payment->refresh();
                }
            }

            $payment->load(['invoice.student.user']);

            Log::info('Final payment status before redirect', [
                'payment_id' => $payment->id,
                'status' => $payment->status
            ]);

            // Redirect based on payment status
            if ($payment->status === 'paid') {
                return redirect()->route('guardian.payments.show', $payment)
                                ->with('success', 'Payment completed successfully!');
            } else {
                return redirect()->route('guardian.invoices.show', $payment->invoice)
                                ->with('error', 'Payment was not successful. Please try again.');
            }

        } catch (\Exception $e) {
            Log::error('Payment return processing failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return redirect()->route('guardian.invoices.show', $payment->invoice)
                            ->with('error', 'An error occurred while processing your payment. Please contact support.');
        }
    }

    /**
     * Handle payment callback from Billplz
     */
    public function callback(Request $request, Payment $payment)
    {
        // This is similar to return but typically used for background processing
        return $this->return($request, $payment);
    }

    /**
     * Update payment status based on Billplz response
     */
    private function updatePaymentStatus(Payment $payment, array $billplzData)
    {
        $oldStatus = $payment->status;

        Log::info('Processing payment status update', [
            'payment_id' => $payment->id,
            'billplz_data' => $billplzData,
            'current_status' => $oldStatus
        ]);

        // For sandbox/testing, be more lenient with verification
        $isSandbox = config('services.billplz.sandbox', true);

        // Verify payment signature if available (skip in sandbox for testing)
        if (isset($billplzData['x_signature']) && !$isSandbox) {
            $dataToVerify = $billplzData;
            unset($dataToVerify['x_signature']);

            if (!$this->billplzService->verifySignature($dataToVerify, $billplzData['x_signature'])) {
                Log::warning('Invalid Billplz signature detected', [
                    'payment_id' => $payment->id,
                    'billplz_bill_id' => $billplzData['id'] ?? null
                ]);
                return;
            }
        }

        // Additional verification: Check if payment amount matches (be more lenient)
        if (isset($billplzData['amount'])) {
            $billplzAmount = $billplzData['amount'] / 100; // Convert from cents
            if (abs($billplzAmount - $payment->amount) > 1.00) { // Allow 1 RM difference for rounding
                Log::warning('Payment amount mismatch detected', [
                    'payment_id' => $payment->id,
                    'expected_amount' => $payment->amount,
                    'billplz_amount' => $billplzAmount
                ]);
                // Don't fail immediately, just log the warning
            }
        }

        // Map Billplz states to our payment statuses
        switch ($billplzData['state']) {
            case 'paid':
                $payment->status = 'paid';
                $payment->paid_at = now();
                $payment->payment_method = $this->mapPaymentMethod($billplzData);

                // Set transaction ID if available
                if (isset($billplzData['transaction_id'])) {
                    $payment->billplz_transaction_id = $billplzData['transaction_id'];
                }

                Log::info('Payment marked as paid', [
                    'payment_id' => $payment->id,
                    'transaction_id' => $billplzData['transaction_id'] ?? 'N/A'
                ]);
                break;

            case 'due':
                $payment->status = 'pending';
                break;

            case 'deleted':
                $payment->status = 'failed';
                break;

            default:
                // For unknown states, keep current status but log
                Log::warning('Unknown Billplz state received', [
                    'payment_id' => $payment->id,
                    'state' => $billplzData['state'],
                    'keeping_current_status' => $payment->status
                ]);
                break;
        }

        // Update Billplz response data
        $payment->billplz_response = array_merge(
            $payment->billplz_response ?? [],
            $billplzData
        );

        $payment->save();

        // Update invoice status if payment is successful
        if ($payment->status === 'paid' && $oldStatus !== 'paid') {
            $this->updateInvoiceStatus($payment->invoice);
        }

        Log::info('Payment status updated', [
            'payment_id' => $payment->id,
            'old_status' => $oldStatus,
            'new_status' => $payment->status,
            'billplz_state' => $billplzData['state'],
            'transaction_id' => $billplzData['transaction_id'] ?? null
        ]);
    }

    /**
     * Update invoice status based on payments
     */
    private function updateInvoiceStatus(Invoice $invoice)
    {
        $totalPaid = $invoice->payments()->where('status', 'paid')->sum('amount');
        
        if ($totalPaid >= $invoice->total_amount) {
            $invoice->update(['status' => 'paid']);
            
            Log::info('Invoice marked as paid', [
                'invoice_id' => $invoice->id,
                'total_amount' => $invoice->total_amount,
                'total_paid' => $totalPaid
            ]);
        }
    }

    /**
     * Map Billplz payment method to our enum values
     */
    private function mapPaymentMethod(array $billplzData)
    {
        // Extract payment method from Billplz response
        // This might need adjustment based on actual Billplz response format
        
        if (isset($billplzData['transaction_id'])) {
            $transactionId = $billplzData['transaction_id'];
            
            // Basic mapping based on transaction ID patterns
            if (strpos($transactionId, 'FPX') !== false) {
                return 'fpx';
            } elseif (strpos($transactionId, 'CC') !== false || strpos($transactionId, 'VISA') !== false || strpos($transactionId, 'MASTER') !== false) {
                return 'card';
            } elseif (strpos($transactionId, 'TNG') !== false || strpos($transactionId, 'BOOST') !== false || strpos($transactionId, 'GRAB') !== false) {
                return 'ewallet';
            } elseif (strpos($transactionId, 'DUITNOW') !== false) {
                return 'duitnow';
            }
        }

        // Default to FPX if unable to determine
        return 'fpx';
    }

    /**
     * Handle failed payments (for manual processing)
     */
    public function handleFailedPayment(Payment $payment, $reason = null)
    {
        $payment->update([
            'status' => 'failed',
            'billplz_response' => array_merge(
                $payment->billplz_response ?? [],
                [
                    'failure_reason' => $reason,
                    'failed_at' => now(),
                ]
            )
        ]);

        Log::warning('Payment marked as failed', [
            'payment_id' => $payment->id,
            'reason' => $reason
        ]);
    }

    /**
     * Retry payment (create new Billplz bill)
     */
    public function retryPayment(Payment $payment)
    {
        if ($payment->status === 'paid') {
            return false;
        }

        try {
            $invoice = $payment->invoice;
            $guardian = $payment->payer;

            // Create new Billplz bill
            $billData = [
                'email' => $guardian->email,
                'mobile' => $guardian->phone ?? null,
                'name' => $guardian->name,
                'amount' => $payment->amount,
                'description' => $invoice->title . ' - ' . $invoice->student->user->name,
                'callback_url' => route('billplz.webhook'),
                'redirect_url' => route('payment.return', $payment),
                'invoice_id' => $invoice->id,
            ];

            $billplzResponse = $this->billplzService->createBill($billData);

            if ($billplzResponse) {
                $payment->update([
                    'billplz_bill_id' => $billplzResponse['id'],
                    'billplz_response' => $billplzResponse,
                    'status' => 'pending',
                ]);

                return $billplzResponse['url'];
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Payment retry failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }
}
