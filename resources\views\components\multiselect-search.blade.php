@props([
    'items' => [],
    'selected' => [],
    'name' => 'items[]',
    'placeholder' => 'Search and select items...',
    'label' => null,
    'error' => null
])

<div class="space-y-3">
    @if($label)
        <label class="block text-sm font-medium text-gray-700">{{ $label }}</label>
    @endif
    
    <!-- Multiselect Search Component -->
    <div x-data="multiselectSearch({
        items: @json($items),
        selected: @json($selected),
        name: '{{ $name }}',
        placeholder: '{{ $placeholder }}'
    })" class="relative">
        
        <!-- Search Input -->
        <div class="relative">
            <input
                type="text"
                x-model="searchQuery"
                @focus="showDropdown = true"
                @click.away="showDropdown = false"
                class="form-input w-full pr-10"
                :placeholder="placeholder"
                autocomplete="off"
            >
            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
        </div>

        <!-- Selected Items -->
        <div x-show="selectedItems.length > 0" class="mt-2 flex flex-wrap gap-2">
            <template x-for="item in selectedItems" :key="item.id">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                    <span x-text="item.name"></span>
                    <button type="button" @click="removeItem(item.id)" class="ml-2 text-blue-600 hover:text-blue-800">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </span>
            </template>
        </div>

        <!-- Dropdown -->
        <div x-show="showDropdown && filteredItems.length > 0" 
             x-transition:enter="transition ease-out duration-100"
             x-transition:enter-start="transform opacity-0 scale-95"
             x-transition:enter-end="transform opacity-100 scale-100"
             x-transition:leave="transition ease-in duration-75"
             x-transition:leave-start="transform opacity-100 scale-100"
             x-transition:leave-end="transform opacity-0 scale-95"
             class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
            <template x-for="item in filteredItems" :key="item.id">
                <div @click="toggleItem(item)" 
                     class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50"
                     :class="{ 'bg-blue-50': isSelected(item.id) }">
                    <div class="flex items-center">
                        <span class="font-medium block truncate" x-text="item.name"></span>
                        <span x-show="isSelected(item.id)" class="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-600">
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </span>
                    </div>
                    <span class="text-gray-500 block truncate text-sm" x-text="item.subtitle"></span>
                </div>
            </template>
        </div>

        <!-- Hidden inputs for form submission -->
        <template x-for="item in selectedItems" :key="item.id">
            <input type="hidden" :name="name" :value="item.id">
        </template>
    </div>
    
    @if($error)
        <p class="mt-1 text-sm text-red-600">{{ $error }}</p>
    @endif
</div>

@once
@push('scripts')
<script>
function multiselectSearch(config) {
    return {
        items: config.items || [],
        selectedItems: [],
        searchQuery: '',
        showDropdown: false,
        name: config.name || 'items[]',
        placeholder: config.placeholder || 'Search and select items...',
        
        init() {
            // Initialize selected items based on config.selected
            if (config.selected && config.selected.length > 0) {
                this.selectedItems = this.items.filter(item => 
                    config.selected.includes(parseInt(item.id))
                );
            }
        },
        
        get filteredItems() {
            if (!this.searchQuery) {
                return this.items.filter(item => !this.isSelected(item.id));
            }
            
            return this.items.filter(item => {
                const matchesSearch = item.searchText.toLowerCase().includes(this.searchQuery.toLowerCase());
                const notSelected = !this.isSelected(item.id);
                return matchesSearch && notSelected;
            });
        },
        
        isSelected(itemId) {
            return this.selectedItems.some(item => item.id == itemId);
        },
        
        toggleItem(item) {
            if (this.isSelected(item.id)) {
                this.removeItem(item.id);
            } else {
                this.selectedItems.push(item);
                this.searchQuery = '';
                this.showDropdown = false;
            }
        },
        
        removeItem(itemId) {
            this.selectedItems = this.selectedItems.filter(item => item.id != itemId);
        }
    };
}
</script>
@endpush
@endonce
