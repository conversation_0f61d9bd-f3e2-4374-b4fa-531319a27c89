<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ActivityLog;
use App\Models\User;
use App\Models\Student;
use App\Models\Teacher;
use Carbon\Carbon;

class ActivityLogSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $users = User::all();
        $students = Student::with('user')->take(10)->get();
        $teachers = Teacher::with('user')->take(5)->get();

        // Create sample activity logs for the past 30 days
        for ($i = 0; $i < 30; $i++) {
            $date = Carbon::now()->subDays($i);
            
            // Random user for each activity
            $user = $users->random();
            
            // Create various types of activities
            $activities = [
                // Attendance activities
                [
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'action' => 'marked_attendance',
                    'description' => 'Marked ' . $students->random()->user->name . ' (student) as present for ' . $date->format('Y-m-d'),
                    'model_type' => 'App\Models\StudentAttendance',
                    'model_id' => null,
                    'properties' => [
                        'type' => 'student',
                        'names' => [$students->random()->user->name],
                        'date' => $date->format('Y-m-d'),
                        'status' => 'present',
                        'count' => 1,
                    ],
                    'ip_address' => '192.168.1.' . rand(1, 254),
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'created_at' => $date->setTime(rand(8, 17), rand(0, 59)),
                ],
                [
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'action' => 'bulk_marked_attendance',
                    'description' => 'Bulk marked ' . rand(5, 15) . ' student(s) as present for ' . $date->format('Y-m-d'),
                    'model_type' => 'App\Models\StudentAttendance',
                    'model_id' => null,
                    'properties' => [
                        'type' => 'student',
                        'names' => $students->take(rand(5, 10))->pluck('user.name')->toArray(),
                        'date' => $date->format('Y-m-d'),
                        'status' => 'present',
                        'count' => rand(5, 15),
                    ],
                    'ip_address' => '192.168.1.' . rand(1, 254),
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'created_at' => $date->setTime(rand(8, 17), rand(0, 59)),
                ],
                [
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'action' => 'marked_attendance',
                    'description' => 'Marked ' . $teachers->random()->user->name . ' (teacher) as present for ' . $date->format('Y-m-d'),
                    'model_type' => 'App\Models\TeacherAttendance',
                    'model_id' => null,
                    'properties' => [
                        'type' => 'teacher',
                        'names' => [$teachers->random()->user->name],
                        'date' => $date->format('Y-m-d'),
                        'status' => 'present',
                        'count' => 1,
                    ],
                    'ip_address' => '192.168.1.' . rand(1, 254),
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'created_at' => $date->setTime(rand(8, 17), rand(0, 59)),
                ],
                // User activities
                [
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'action' => 'created_user',
                    'description' => 'Created user ' . $users->random()->name,
                    'model_type' => 'App\Models\User',
                    'model_id' => $users->random()->id,
                    'properties' => [
                        'user_name' => $users->random()->name,
                        'user_email' => $users->random()->email,
                        'user_role' => $users->random()->user_type,
                    ],
                    'ip_address' => '192.168.1.' . rand(1, 254),
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'created_at' => $date->setTime(rand(8, 17), rand(0, 59)),
                ],
                [
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'action' => 'updated_user',
                    'description' => 'Updated user ' . $users->random()->name,
                    'model_type' => 'App\Models\User',
                    'model_id' => $users->random()->id,
                    'properties' => [
                        'user_name' => $users->random()->name,
                        'user_email' => $users->random()->email,
                        'user_role' => $users->random()->user_type,
                    ],
                    'ip_address' => '192.168.1.' . rand(1, 254),
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'created_at' => $date->setTime(rand(8, 17), rand(0, 59)),
                ],
                // Login activities
                [
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'action' => 'login',
                    'description' => 'User logged in',
                    'model_type' => null,
                    'model_id' => null,
                    'properties' => [
                        'user_role' => $user->user_type,
                    ],
                    'ip_address' => '192.168.1.' . rand(1, 254),
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'created_at' => $date->setTime(rand(8, 17), rand(0, 59)),
                ],
            ];

            // Create 2-5 random activities per day
            $dailyActivities = collect($activities)->random(rand(2, 5));
            
            foreach ($dailyActivities as $activity) {
                ActivityLog::create($activity);
            }
        }

        // Create some recent activities for today
        $today = Carbon::today();
        for ($i = 0; $i < 10; $i++) {
            $user = $users->random();
            $time = $today->copy()->setTime(rand(8, 17), rand(0, 59));
            
            ActivityLog::create([
                'user_id' => $user->id,
                'user_name' => $user->name,
                'action' => collect(['marked_attendance', 'bulk_marked_attendance', 'updated_user', 'login'])->random(),
                'description' => 'Recent activity for testing - ' . $user->name,
                'model_type' => null,
                'model_id' => null,
                'properties' => [
                    'test' => true,
                    'user_role' => $user->user_type,
                ],
                'ip_address' => '192.168.1.' . rand(1, 254),
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'created_at' => $time,
            ]);
        }
    }
}
