<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class AttendanceSettings extends Model
{
    use HasFactory;

    protected $fillable = [
        'school_start_time',
        'school_end_time',
        'late_threshold',
        'grace_period_minutes',
        'auto_mark_absent',
        'working_days',
        'holidays',
        'allow_retroactive_marking',
        'retroactive_days_limit',
    ];

    protected $casts = [
        'school_start_time' => 'datetime:H:i',
        'school_end_time' => 'datetime:H:i',
        'late_threshold' => 'datetime:H:i',
        'grace_period_minutes' => 'integer',
        'auto_mark_absent' => 'boolean',
        'working_days' => 'array',
        'holidays' => 'array',
        'allow_retroactive_marking' => 'boolean',
        'retroactive_days_limit' => 'integer',
    ];

    /**
     * Get the singleton instance of attendance settings
     */
    public static function getInstance()
    {
        $settings = self::first();
        
        if (!$settings) {
            $settings = self::create([
                'school_start_time' => '08:00:00',
                'school_end_time' => '15:00:00',
                'late_threshold' => '08:15:00',
                'grace_period_minutes' => 15,
                'auto_mark_absent' => true,
                'working_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                'holidays' => [],
                'allow_retroactive_marking' => true,
                'retroactive_days_limit' => 7,
            ]);
        }
        
        return $settings;
    }

    /**
     * Check if a given date is a working day
     */
    public function isWorkingDay($date)
    {
        $carbon = Carbon::parse($date);
        $dayName = strtolower($carbon->format('l'));
        
        // Check if it's a working day
        if (!in_array($dayName, $this->working_days)) {
            return false;
        }
        
        // Check if it's a holiday
        if (in_array($carbon->format('Y-m-d'), $this->holidays ?? [])) {
            return false;
        }
        
        return true;
    }

    /**
     * Check if a time is considered late
     */
    public function isLate($time)
    {
        $checkTime = Carbon::parse($time);
        $threshold = Carbon::parse($this->late_threshold);
        
        return $checkTime->greaterThan($threshold);
    }

    /**
     * Check if retroactive marking is allowed for a date
     */
    public function canMarkRetroactive($date)
    {
        if (!$this->allow_retroactive_marking) {
            return false;
        }
        
        $targetDate = Carbon::parse($date);
        $today = Carbon::today();
        
        $daysDiff = $today->diffInDays($targetDate);
        
        return $daysDiff <= $this->retroactive_days_limit;
    }

    /**
     * Get working days between two dates
     */
    public function getWorkingDaysBetween($startDate, $endDate)
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        $workingDays = [];
        
        while ($start->lte($end)) {
            if ($this->isWorkingDay($start)) {
                $workingDays[] = $start->copy();
            }
            $start->addDay();
        }
        
        return $workingDays;
    }

    /**
     * Add a holiday
     */
    public function addHoliday($date, $description = null)
    {
        $holidays = $this->holidays ?? [];
        $dateStr = Carbon::parse($date)->format('Y-m-d');
        
        if (!in_array($dateStr, $holidays)) {
            $holidays[] = $dateStr;
            $this->holidays = $holidays;
            $this->save();
        }
    }

    /**
     * Remove a holiday
     */
    public function removeHoliday($date)
    {
        $holidays = $this->holidays ?? [];
        $dateStr = Carbon::parse($date)->format('Y-m-d');
        
        $holidays = array_filter($holidays, function($holiday) use ($dateStr) {
            return $holiday !== $dateStr;
        });
        
        $this->holidays = array_values($holidays);
        $this->save();
    }

    /**
     * Get upcoming holidays
     */
    public function getUpcomingHolidays($limit = 10)
    {
        $today = Carbon::today();
        $holidays = $this->holidays ?? [];
        
        $upcomingHolidays = array_filter($holidays, function($holiday) use ($today) {
            return Carbon::parse($holiday)->gte($today);
        });
        
        usort($upcomingHolidays, function($a, $b) {
            return Carbon::parse($a)->compare(Carbon::parse($b));
        });
        
        return array_slice($upcomingHolidays, 0, $limit);
    }
}
