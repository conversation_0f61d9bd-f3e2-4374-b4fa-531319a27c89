@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Parent Portal</h1>
                <p class="text-gray-600">Welcome back, {{ auth()->user()->name }}!</p>
                @if($guardian)
                    <p class="text-sm text-gray-500">{{ $guardian->relationship }} | {{ $guardian->occupation }}</p>
                @endif
            </div>
            <div class="text-right">
                <p class="text-sm text-gray-500">{{ now()->format('l, F j, Y') }}</p>
                <p class="text-sm text-gray-500">{{ now()->format('g:i A') }}</p>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <span class="text-white font-bold text-sm">{{ $stats['my_children'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">My Children</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['my_children'] }} students</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <span class="text-white font-bold text-sm">{{ $stats['pending_invoices'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending Bills</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['pending_invoices'] }} invoices</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <span class="text-white font-bold text-sm">RM</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Amount Due</dt>
                            <dd class="text-lg font-medium text-gray-900">RM {{ number_format($stats['pending_amount'], 2) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Avg Attendance</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                @if($students->count() > 0)
                                    {{ number_format(collect($childrenAttendance)->avg('attendance_percentage'), 1) }}%
                                @else
                                    0%
                                @endif
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Summary -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Paid</dt>
                            <dd class="text-lg font-medium text-gray-900">RM {{ number_format($stats['total_paid'], 2) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <span class="text-white font-bold text-sm">{{ $stats['paid_invoices'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Paid Invoices</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $stats['paid_invoices'] }} completed</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- My Children -->
    @if($students->count() > 0)
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">My Children</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                @foreach($students as $student)
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span class="text-blue-600 font-medium text-sm">
                                            {{ substr($student->user->name, 0, 2) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4 flex-1">
                                    <h4 class="text-sm font-medium text-gray-900">{{ $student->user->name }}</h4>
                                    <p class="text-sm text-gray-500">{{ $student->class }} - {{ $student->section }}</p>
                                    <p class="text-xs text-gray-400">Student ID: {{ $student->student_id }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                @if(isset($childrenAttendance[$student->id]))
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ $childrenAttendance[$student->id]['attendance_percentage'] }}%
                                    </div>
                                    <div class="text-xs text-gray-500">Attendance</div>
                                @else
                                    <div class="text-sm text-gray-500">No data</div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Children Attendance Summary -->
    @if($students->count() > 0)
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">Children's Attendance Summary (Current Month)</h3>

        <div class="space-y-6">
            @foreach($students as $student)
                @if(isset($childrenAttendance[$student->id]))
                    @php $attendance = $childrenAttendance[$student->id]; @endphp
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span class="text-blue-600 font-medium text-xs">
                                        {{ substr($student->user->name, 0, 2) }}
                                    </span>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-gray-900">{{ $student->user->name }}</h4>
                                    <p class="text-xs text-gray-500">{{ $student->class }} - {{ $student->section }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-blue-600">{{ $attendance['attendance_percentage'] }}%</div>
                                <div class="text-xs text-gray-500">Attendance Rate</div>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-3">
                            <div class="text-center">
                                <div class="text-lg font-bold text-green-600">{{ $attendance['present'] }}</div>
                                <div class="text-xs text-gray-500">Present</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-red-600">{{ $attendance['absent'] }}</div>
                                <div class="text-xs text-gray-500">Absent</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-yellow-600">{{ $attendance['late'] ?? 0 }}</div>
                                <div class="text-xs text-gray-500">Late</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-blue-600">{{ $attendance['excused'] }}</div>
                                <div class="text-xs text-gray-500">Excused</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-purple-600">{{ $attendance['sick'] ?? 0 }}</div>
                                <div class="text-xs text-gray-500">Sick</div>
                            </div>
                        </div>

                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $attendance['attendance_percentage'] }}%"></div>
                        </div>
                    </div>
                @endif
            @endforeach
        </div>
    </div>
    @endif

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Invoices -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Recent Invoices</h3>
            </div>
            <div class="p-6">
                @if($recent_invoices->count() > 0)
                    <div class="space-y-4">
                        @foreach($recent_invoices as $invoice)
                            <div class="flex items-center justify-between border-b border-gray-100 pb-4">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ $invoice->title }}</p>
                                    <p class="text-sm text-gray-500">{{ $invoice->student->user->name ?? 'N/A' }}</p>
                                    <p class="text-xs text-gray-400">Due: {{ $invoice->due_date->format('M j, Y') }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">RM {{ number_format($invoice->total_amount, 2) }}</p>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($invoice->status === 'paid') bg-green-100 text-green-800
                                        @elseif($invoice->status === 'sent') bg-blue-100 text-blue-800
                                        @elseif($invoice->status === 'overdue') bg-red-100 text-red-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($invoice->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-center py-4">No invoices found</p>
                @endif
            </div>
        </div>

        <!-- Recent Payments -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Recent Payments</h3>
            </div>
            <div class="p-6">
                @if($recent_payments->count() > 0)
                    <div class="space-y-4">
                        @foreach($recent_payments as $payment)
                            <div class="flex items-center justify-between border-b border-gray-100 pb-4">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ $payment->invoice->title ?? 'N/A' }}</p>
                                    <p class="text-sm text-gray-500">{{ $payment->invoice->student->user->name ?? 'N/A' }}</p>
                                    <p class="text-xs text-gray-400">{{ $payment->paid_at ? $payment->paid_at->format('M j, Y g:i A') : 'N/A' }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">RM {{ number_format($payment->amount, 2) }}</p>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Paid
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-center py-4">No payments found</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{{ route('guardian.invoices.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">View Invoices</p>
                    <p class="text-sm text-gray-500">Check pending bills and payments</p>
                </div>
            </a>

            <a href="{{ route('guardian.payments.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Payment History</p>
                    <p class="text-sm text-gray-500">View all payment records</p>
                </div>
            </a>

            <a href="{{ route('guardian.children.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">My Children</p>
                    <p class="text-sm text-gray-500">View children's information</p>
                </div>
            </a>
        </div>
    </div>
</div>
@endsection
