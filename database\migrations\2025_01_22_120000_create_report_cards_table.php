<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Report Card Templates
        Schema::create('report_card_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('layout_config'); // Template layout configuration
            $table->json('grade_components'); // Which components to include (exams, assignments, attendance)
            $table->json('calculation_rules'); // How to calculate final grades
            $table->boolean('include_attendance')->default(true);
            $table->boolean('include_remarks')->default(true);
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Report Cards
        Schema::create('report_cards', function (Blueprint $table) {
            $table->id();
            $table->string('report_number')->unique();
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_term_id')->constrained()->onDelete('cascade');
            $table->foreignId('template_id')->constrained('report_card_templates')->onDelete('cascade');
            $table->json('grade_data'); // Compiled grade data
            $table->json('attendance_data'); // Attendance summary
            $table->decimal('overall_gpa', 3, 2)->nullable();
            $table->string('overall_grade', 5)->nullable();
            $table->integer('class_rank')->nullable();
            $table->integer('total_students')->nullable();
            $table->text('teacher_remarks')->nullable();
            $table->text('principal_remarks')->nullable();
            $table->enum('status', ['draft', 'generated', 'published', 'distributed'])->default('draft');
            $table->timestamp('generated_at')->nullable();
            $table->timestamp('published_at')->nullable();
            $table->foreignId('generated_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            $table->unique(['student_id', 'academic_term_id']);
            $table->index(['academic_term_id', 'status']);
        });

        // Report Card Subject Grades
        Schema::create('report_card_grades', function (Blueprint $table) {
            $table->id();
            $table->foreignId('report_card_id')->constrained()->onDelete('cascade');
            $table->foreignId('subject_id')->constrained()->onDelete('cascade');
            $table->decimal('assignment_marks', 5, 2)->nullable();
            $table->decimal('exam_marks', 5, 2)->nullable();
            $table->decimal('total_marks', 5, 2);
            $table->decimal('percentage', 5, 2);
            $table->string('grade', 5);
            $table->decimal('gpa_points', 3, 2);
            $table->text('remarks')->nullable();
            $table->timestamps();

            $table->unique(['report_card_id', 'subject_id']);
        });

        // Report Card Comments/Remarks Templates
        Schema::create('report_card_comments', function (Blueprint $table) {
            $table->id();
            $table->string('category'); // academic, behavior, attendance, etc.
            $table->text('comment');
            $table->string('grade_range')->nullable(); // A+, A, B+, etc.
            $table->boolean('is_positive')->default(true);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['category', 'grade_range']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_card_comments');
        Schema::dropIfExists('report_card_grades');
        Schema::dropIfExists('report_cards');
        Schema::dropIfExists('report_card_templates');
    }
};
