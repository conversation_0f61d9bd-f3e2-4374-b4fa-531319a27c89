<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Section;
use App\Models\ClassModel;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SectionController extends Controller
{
    /**
     * Display a listing of sections
     */
    public function index(Request $request)
    {
        $query = Section::with(['class', 'students', 'teachers']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhereHas('class', function($classQuery) use ($search) {
                      $classQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('class_id')) {
            $query->where('class_id', $request->class_id);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $sections = $query->orderBy('class_id')->orderBy('name')->paginate(20);

        // Get filter options
        $classes = ClassModel::active()->ordered()->get();

        // Statistics
        $stats = [
            'total_sections' => Section::count(),
            'active_sections' => Section::active()->count(),
            'total_capacity' => Section::sum('capacity'),
            'total_enrolled' => \App\Models\Student::count(),
        ];

        return view('admin.sections.index', compact('sections', 'classes', 'stats'));
    }

    /**
     * Show the form for creating a new section
     */
    public function create()
    {
        $classes = ClassModel::active()->ordered()->get();
        return view('admin.sections.create', compact('classes'));
    }

    /**
     * Store a newly created section
     */
    public function store(Request $request)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'name' => 'required|string|max:10',
            'capacity' => 'required|integer|min:1|max:100',
            'is_active' => 'boolean',
        ]);

        // Check for duplicate section name within the same class
        $existingSection = Section::where('class_id', $request->class_id)
                                 ->where('name', $request->name)
                                 ->first();

        if ($existingSection) {
            return back()->withInput()
                        ->withErrors(['name' => 'A section with this name already exists in the selected class.']);
        }

        try {
            DB::beginTransaction();

            $section = Section::create([
                'class_id' => $request->class_id,
                'name' => $request->name,
                'capacity' => $request->capacity,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::log(
                'created_section',
                "Created section: {$section->full_name}",
                'App\Models\Section',
                $section->id
            );

            DB::commit();

            return redirect()->route('admin.academic.sections.index')
                           ->with('success', 'Section created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Error creating section: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified section
     */
    public function show(Section $section)
    {
        $section->load(['class', 'students.user', 'teachers.user']);
        
        return view('admin.sections.show', compact('section'));
    }

    /**
     * Show the form for editing the specified section
     */
    public function edit(Section $section)
    {
        $classes = ClassModel::active()->ordered()->get();
        return view('admin.sections.edit', compact('section', 'classes'));
    }

    /**
     * Update the specified section
     */
    public function update(Request $request, Section $section)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'name' => 'required|string|max:10',
            'capacity' => 'required|integer|min:1|max:100',
            'is_active' => 'boolean',
        ]);

        // Check for duplicate section name within the same class (excluding current section)
        $existingSection = Section::where('class_id', $request->class_id)
                                 ->where('name', $request->name)
                                 ->where('id', '!=', $section->id)
                                 ->first();

        if ($existingSection) {
            return back()->withInput()
                        ->withErrors(['name' => 'A section with this name already exists in the selected class.']);
        }

        try {
            DB::beginTransaction();

            $oldData = $section->toArray();

            $section->update([
                'class_id' => $request->class_id,
                'name' => $request->name,
                'capacity' => $request->capacity,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::log(
                'updated_section',
                "Updated section: {$section->full_name}",
                'App\Models\Section',
                $section->id,
                ['old_data' => $oldData, 'new_data' => $section->fresh()->toArray()]
            );

            DB::commit();

            return redirect()->route('admin.academic.sections.index')
                           ->with('success', 'Section updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Error updating section: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified section
     */
    public function destroy(Section $section)
    {
        try {
            DB::beginTransaction();

            // Check if section has students
            if ($section->students()->count() > 0) {
                return back()->with('error', 'Cannot delete section that has students.');
            }

            $sectionName = $section->full_name;

            $section->delete();

            // Log activity
            ActivityLog::log(
                'deleted_section',
                "Deleted section: {$sectionName}",
                'App\Models\Section',
                null
            );

            DB::commit();

            return redirect()->route('admin.academic.sections.index')
                           ->with('success', 'Section deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Error deleting section: ' . $e->getMessage());
        }
    }

    /**
     * Toggle section status
     */
    public function toggleStatus(Section $section)
    {
        try {
            $section->update(['is_active' => !$section->is_active]);

            $status = $section->is_active ? 'activated' : 'deactivated';
            
            // Log activity
            ActivityLog::log(
                $status . '_section',
                "Section {$status}: {$section->full_name}",
                'App\Models\Section',
                $section->id
            );

            return back()->with('success', "Section {$status} successfully.");

        } catch (\Exception $e) {
            return back()->with('error', 'Error updating section status: ' . $e->getMessage());
        }
    }
}
