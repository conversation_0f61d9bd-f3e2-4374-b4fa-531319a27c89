<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Guardian;
use App\Models\Student;
use Illuminate\Support\Facades\Hash;

class AdditionalUsersSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create additional guardians and their children
        $guardianData = [
            [
                'guardian' => [
                    'name' => 'Mrs. <PERSON>',
                    'email' => '<EMAIL>',
                    'phone' => '+60123456789',
                    'relationship' => 'mother',
                    'occupation' => 'Teacher',
                    'workplace' => 'SMK Taman Desa',
                    'monthly_income' => 4500.00,
                ],
                'children' => [
                    [
                        'name' => '<PERSON>',
                        'student_id' => 'STU2025003',
                        'date_of_birth' => '2010-03-15',
                        'gender' => 'female',
                        'class' => 'Form 3',
                        'section' => 'A',
                        'roll_number' => '15',
                        'blood_group' => 'A+',
                    ],
                    [
                        'name' => '<PERSON>',
                        'student_id' => 'STU2025004',
                        'date_of_birth' => '2012-07-22',
                        'gender' => 'male',
                        'class' => 'Form 1',
                        'section' => 'B',
                        'roll_number' => '22',
                        'blood_group' => 'A+',
                    ]
                ]
            ],
            [
                'guardian' => [
                    'name' => 'Mr. David Chen',
                    'email' => '<EMAIL>',
                    'phone' => '+60198765432',
                    'relationship' => 'father',
                    'occupation' => 'Engineer',
                    'workplace' => 'Tech Solutions Sdn Bhd',
                    'monthly_income' => 6500.00,
                ],
                'children' => [
                    [
                        'name' => 'Michelle Chen',
                        'student_id' => 'STU2025005',
                        'date_of_birth' => '2009-11-08',
                        'gender' => 'female',
                        'class' => 'Form 4',
                        'section' => 'A',
                        'roll_number' => '08',
                        'blood_group' => 'B+',
                    ]
                ]
            ],
            [
                'guardian' => [
                    'name' => 'Mrs. Fatimah Abdullah',
                    'email' => '<EMAIL>',
                    'phone' => '+60187654321',
                    'relationship' => 'mother',
                    'occupation' => 'Nurse',
                    'workplace' => 'Hospital Kuala Lumpur',
                    'monthly_income' => 3800.00,
                ],
                'children' => [
                    [
                        'name' => 'Ahmad Abdullah',
                        'student_id' => 'STU2025006',
                        'date_of_birth' => '2011-05-12',
                        'gender' => 'male',
                        'class' => 'Form 2',
                        'section' => 'A',
                        'roll_number' => '12',
                        'blood_group' => 'O+',
                    ],
                    [
                        'name' => 'Siti Abdullah',
                        'student_id' => 'STU2025007',
                        'date_of_birth' => '2013-09-30',
                        'gender' => 'female',
                        'class' => 'Form 1',
                        'section' => 'A',
                        'roll_number' => '30',
                        'blood_group' => 'O+',
                    ]
                ]
            ],
            [
                'guardian' => [
                    'name' => 'Mr. Raj Kumar',
                    'email' => '<EMAIL>',
                    'phone' => '+***********',
                    'relationship' => 'father',
                    'occupation' => 'Business Owner',
                    'workplace' => 'Kumar Trading Sdn Bhd',
                    'monthly_income' => 8200.00,
                ],
                'children' => [
                    [
                        'name' => 'Priya Kumar',
                        'student_id' => 'STU2025008',
                        'date_of_birth' => '2010-01-18',
                        'gender' => 'female',
                        'class' => 'Form 3',
                        'section' => 'B',
                        'roll_number' => '18',
                        'blood_group' => 'AB+',
                    ]
                ]
            ]
        ];

        foreach ($guardianData as $data) {
            // Create guardian user
            $guardianUser = User::create([
                'name' => $data['guardian']['name'],
                'email' => $data['guardian']['email'],
                'phone' => $data['guardian']['phone'],
                'password' => Hash::make('password123'),
                'user_type' => 'guardian',
                'is_active' => true,
            ]);

            // Create guardian profile
            $guardian = Guardian::create([
                'user_id' => $guardianUser->id,
                'relationship' => $data['guardian']['relationship'],
                'occupation' => $data['guardian']['occupation'],
                'workplace' => $data['guardian']['workplace'],
                'monthly_income' => $data['guardian']['monthly_income'],
                'emergency_contact' => $data['guardian']['phone'],
            ]);

            // Create children
            foreach ($data['children'] as $childData) {
                // Create student user
                $studentUser = User::create([
                    'name' => $childData['name'],
                    'email' => strtolower(str_replace(' ', '.', $childData['name'])) . '@student.school.edu.my',
                    'password' => Hash::make('student123'),
                    'user_type' => 'student',
                    'is_active' => true,
                ]);

                // Create student profile
                $student = Student::create([
                    'user_id' => $studentUser->id,
                    'student_id' => $childData['student_id'],
                    'date_of_birth' => $childData['date_of_birth'],
                    'gender' => $childData['gender'],
                    'class' => $childData['class'],
                    'section' => $childData['section'],
                    'roll_number' => $childData['roll_number'],
                    'admission_date' => '2024-01-15',
                    'blood_group' => $childData['blood_group'],
                    'emergency_contact' => $data['guardian']['phone'],
                ]);

                // Link guardian to student
                $guardian->students()->attach($student->id);
            }
        }

        $this->command->info('Additional guardians and students created successfully!');
    }
}
