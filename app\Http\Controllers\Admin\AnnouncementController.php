<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Announcement;
use App\Models\ClassModel;
use Illuminate\Http\Request;

class AnnouncementController extends Controller
{
    public function index()
    {
        $announcements = Announcement::with('creator')
                                   ->orderBy('is_pinned', 'desc')
                                   ->orderBy('publish_date', 'desc')
                                   ->paginate(10);

        return view('admin.announcements.index', compact('announcements'));
    }

    public function create()
    {
        $classes = ClassModel::where('is_active', true)->get();
        return view('admin.announcements.create', compact('classes'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|in:general,urgent,academic,event,reminder',
            'target_audience' => 'required|in:all,students,teachers,parents,staff',
            'target_classes' => 'nullable|array',
            'publish_date' => 'required|date',
            'expire_date' => 'nullable|date|after:publish_date',
            'is_published' => 'boolean',
            'is_pinned' => 'boolean',
        ]);

        $data = $request->all();
        $data['created_by'] = auth()->id();

        Announcement::create($data);

        return redirect()->route('admin.announcements.index')
                        ->with('success', 'Announcement created successfully.');
    }

    public function show(Announcement $announcement)
    {
        $announcement->load('creator');
        return view('admin.announcements.show', compact('announcement'));
    }

    public function edit(Announcement $announcement)
    {
        $classes = ClassModel::where('is_active', true)->get();
        return view('admin.announcements.edit', compact('announcement', 'classes'));
    }

    public function update(Request $request, Announcement $announcement)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|in:general,urgent,academic,event,reminder',
            'target_audience' => 'required|in:all,students,teachers,parents,staff',
            'target_classes' => 'nullable|array',
            'publish_date' => 'required|date',
            'expire_date' => 'nullable|date|after:publish_date',
            'is_published' => 'boolean',
            'is_pinned' => 'boolean',
        ]);

        $announcement->update($request->all());

        return redirect()->route('admin.announcements.index')
                        ->with('success', 'Announcement updated successfully.');
    }

    public function destroy(Announcement $announcement)
    {
        $announcement->delete();

        return redirect()->route('admin.announcements.index')
                        ->with('success', 'Announcement deleted successfully.');
    }

    public function togglePin(Announcement $announcement)
    {
        $announcement->update(['is_pinned' => !$announcement->is_pinned]);

        $status = $announcement->is_pinned ? 'pinned' : 'unpinned';
        
        return redirect()->route('admin.announcements.index')
                        ->with('success', "Announcement {$status} successfully.");
    }
}
