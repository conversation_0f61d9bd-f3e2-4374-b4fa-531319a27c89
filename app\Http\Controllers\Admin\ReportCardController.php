<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ReportCard;
use App\Models\ReportCardTemplate;
use App\Models\Student;
use App\Models\AcademicTerm;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReportCardController extends Controller
{
    public function index(Request $request)
    {
        $search = $request->get('search');
        $status = $request->get('status');
        $academicTerm = $request->get('academic_term');

        $query = ReportCard::with(['student.user', 'academicTerm', 'template']);

        if ($search) {
            $query->whereHas('student.user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhere('report_number', 'like', "%{$search}%");
        }

        if ($status) {
            $query->where('status', $status);
        }

        if ($academicTerm) {
            $query->where('academic_term_id', $academicTerm);
        }

        $reportCards = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get filter options
        $academicTerms = AcademicTerm::where('is_active', true)->get();

        // Get statistics
        $stats = [
            'total' => ReportCard::count(),
            'draft' => ReportCard::where('status', 'draft')->count(),
            'generated' => ReportCard::where('status', 'generated')->count(),
            'published' => ReportCard::where('status', 'published')->count(),
            'distributed' => ReportCard::where('status', 'distributed')->count(),
        ];

        return view('admin.report-cards.index', compact(
            'reportCards', 'academicTerms', 'stats', 
            'search', 'status', 'academicTerm'
        ));
    }

    public function create()
    {
        $students = Student::with('user')->get();
        $academicTerms = AcademicTerm::where('is_active', true)->get();
        $templates = ReportCardTemplate::active()->get();
        
        return view('admin.report-cards.create', compact('students', 'academicTerms', 'templates'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'student_id' => 'required|exists:students,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'template_id' => 'required|exists:report_card_templates,id',
        ]);

        try {
            DB::beginTransaction();

            // Check if report card already exists
            $existing = ReportCard::where('student_id', $request->student_id)
                                 ->where('academic_term_id', $request->academic_term_id)
                                 ->first();

            if ($existing) {
                return back()->withInput()
                            ->with('error', 'Report card already exists for this student and term.');
            }

            // Generate report card data
            $reportData = ReportCard::generateForStudent(
                $request->student_id, 
                $request->academic_term_id, 
                $request->template_id
            );

            $reportCard = ReportCard::create([
                'report_number' => ReportCard::generateReportNumber($request->student_id, $request->academic_term_id),
                'student_id' => $request->student_id,
                'academic_term_id' => $request->academic_term_id,
                'template_id' => $request->template_id,
                'grade_data' => $reportData['grade_data'],
                'attendance_data' => $reportData['attendance_data'],
                'overall_gpa' => $reportData['overall_gpa'],
                'overall_grade' => $reportData['overall_grade'],
                'status' => 'generated',
                'generated_at' => now(),
                'generated_by' => auth()->id(),
            ]);

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'created',
                'description' => "Generated report card: {$reportCard->report_number}",
                'model_type' => ReportCard::class,
                'model_id' => $reportCard->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.report-cards.show', $reportCard)
                            ->with('success', 'Report card generated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to generate report card: ' . $e->getMessage());
        }
    }

    public function show(ReportCard $reportCard)
    {
        $reportCard->load([
            'student.user', 
            'academicTerm.academicYear', 
            'template',
            'grades.subject'
        ]);

        return view('admin.report-cards.show', compact('reportCard'));
    }

    public function edit(ReportCard $reportCard)
    {
        if (!$reportCard->canBeEdited()) {
            return redirect()->route('admin.report-cards.show', $reportCard)
                            ->with('error', 'This report card cannot be edited in its current status.');
        }

        $templates = ReportCardTemplate::active()->get();
        
        return view('admin.report-cards.edit', compact('reportCard', 'templates'));
    }

    public function update(Request $request, ReportCard $reportCard)
    {
        if (!$reportCard->canBeEdited()) {
            return redirect()->route('admin.report-cards.show', $reportCard)
                            ->with('error', 'This report card cannot be edited in its current status.');
        }

        $request->validate([
            'teacher_remarks' => 'nullable|string',
            'principal_remarks' => 'nullable|string',
            'status' => 'required|in:draft,generated,published,distributed',
        ]);

        try {
            DB::beginTransaction();

            $reportCard->update([
                'teacher_remarks' => $request->teacher_remarks,
                'principal_remarks' => $request->principal_remarks,
                'status' => $request->status,
                'published_at' => $request->status === 'published' ? now() : $reportCard->published_at,
            ]);

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'updated',
                'description' => "Updated report card: {$reportCard->report_number}",
                'model_type' => ReportCard::class,
                'model_id' => $reportCard->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.report-cards.show', $reportCard)
                            ->with('success', 'Report card updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to update report card: ' . $e->getMessage());
        }
    }

    public function destroy(ReportCard $reportCard)
    {
        if ($reportCard->status === 'distributed') {
            return redirect()->route('admin.report-cards.index')
                            ->with('error', 'Cannot delete a distributed report card.');
        }

        try {
            DB::beginTransaction();

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'deleted',
                'description' => "Deleted report card: {$reportCard->report_number}",
                'model_type' => ReportCard::class,
                'model_id' => $reportCard->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            $reportCard->delete();

            DB::commit();

            return redirect()->route('admin.report-cards.index')
                            ->with('success', 'Report card deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('admin.report-cards.index')
                            ->with('error', 'Failed to delete report card: ' . $e->getMessage());
        }
    }

    public function generateBulk(Request $request)
    {
        $request->validate([
            'academic_term_id' => 'required|exists:academic_terms,id',
            'template_id' => 'required|exists:report_card_templates,id',
            'student_ids' => 'required|array',
            'student_ids.*' => 'exists:students,id',
        ]);

        try {
            DB::beginTransaction();

            $generated = 0;
            $skipped = 0;

            foreach ($request->student_ids as $studentId) {
                // Check if report card already exists
                $existing = ReportCard::where('student_id', $studentId)
                                     ->where('academic_term_id', $request->academic_term_id)
                                     ->first();

                if ($existing) {
                    $skipped++;
                    continue;
                }

                // Generate report card data
                $reportData = ReportCard::generateForStudent(
                    $studentId, 
                    $request->academic_term_id, 
                    $request->template_id
                );

                ReportCard::create([
                    'report_number' => ReportCard::generateReportNumber($studentId, $request->academic_term_id),
                    'student_id' => $studentId,
                    'academic_term_id' => $request->academic_term_id,
                    'template_id' => $request->template_id,
                    'grade_data' => $reportData['grade_data'],
                    'attendance_data' => $reportData['attendance_data'],
                    'overall_gpa' => $reportData['overall_gpa'],
                    'overall_grade' => $reportData['overall_grade'],
                    'status' => 'generated',
                    'generated_at' => now(),
                    'generated_by' => auth()->id(),
                ]);

                $generated++;
            }

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'bulk_generated',
                'description' => "Bulk generated {$generated} report cards, skipped {$skipped}",
                'model_type' => ReportCard::class,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.report-cards.index')
                            ->with('success', "Generated {$generated} report cards successfully. Skipped {$skipped} existing ones.");

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to generate report cards: ' . $e->getMessage());
        }
    }

    public function publish(ReportCard $reportCard)
    {
        if ($reportCard->status !== 'generated') {
            return redirect()->route('admin.report-cards.show', $reportCard)
                            ->with('error', 'Only generated report cards can be published.');
        }

        try {
            DB::beginTransaction();

            $reportCard->update([
                'status' => 'published',
                'published_at' => now(),
            ]);

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'published',
                'description' => "Published report card: {$reportCard->report_number}",
                'model_type' => ReportCard::class,
                'model_id' => $reportCard->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.report-cards.show', $reportCard)
                            ->with('success', 'Report card published successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('admin.report-cards.show', $reportCard)
                            ->with('error', 'Failed to publish report card: ' . $e->getMessage());
        }
    }

    public function downloadPdf(ReportCard $reportCard)
    {
        // TODO: Implement PDF generation
        return redirect()->route('admin.report-cards.show', $reportCard)
                        ->with('info', 'PDF generation feature will be implemented soon.');
    }
}
