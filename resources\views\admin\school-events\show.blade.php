@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="School Event Details"
        description="View school event information"
        :back-route="route('admin.school-events.index')"
        back-label="Back to School Events">
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.school-events.edit', $schoolEvent) }}" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Event
            </a>
        </div>
    </x-page-header>

    <!-- Event Details -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Event Information</h3>
                <div class="flex items-center space-x-2">
                    <span class="badge badge-purple">
                        {{ ucfirst($schoolEvent->type) }}
                    </span>
                    <span class="badge badge-blue">
                        {{ ucfirst($schoolEvent->scope) }}
                    </span>
                    @if(!$schoolEvent->is_active)
                        <span class="badge badge-gray">Inactive</span>
                    @endif
                </div>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Event Title</label>
                    <p class="mt-1 text-lg font-semibold text-gray-900">{{ $schoolEvent->title }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Event Date</label>
                    <p class="mt-1 text-sm text-gray-900">
                        {{ $schoolEvent->event_date->format('F d, Y') }}
                        @if($schoolEvent->event_date->isToday())
                            <span class="text-green-600 font-medium">(Today)</span>
                        @elseif($schoolEvent->event_date->isTomorrow())
                            <span class="text-blue-600 font-medium">(Tomorrow)</span>
                        @elseif($schoolEvent->event_date->isPast())
                            <span class="text-gray-500">(Past)</span>
                        @endif
                    </p>
                </div>
                @if($schoolEvent->start_time && $schoolEvent->end_time)
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Time</label>
                        <p class="mt-1 text-sm text-gray-900">
                            {{ $schoolEvent->start_time->format('g:i A') }} - {{ $schoolEvent->end_time->format('g:i A') }}
                        </p>
                    </div>
                @endif
                @if($schoolEvent->location)
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Location</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $schoolEvent->location }}</p>
                    </div>
                @endif
                <div>
                    <label class="block text-sm font-medium text-gray-700">Event Type</label>
                    <p class="mt-1 text-sm text-gray-900">{{ ucfirst($schoolEvent->type) }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Scope</label>
                    <p class="mt-1 text-sm text-gray-900">{{ ucfirst($schoolEvent->scope) }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Created by</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $schoolEvent->creator->name ?? 'Unknown' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Created</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $schoolEvent->created_at->format('F d, Y g:i A') }}</p>
                </div>
            </div>
            
            @if($schoolEvent->description)
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700">Description</label>
                    <div class="mt-1 text-sm text-gray-900 prose max-w-none">
                        {!! nl2br(e($schoolEvent->description)) !!}
                    </div>
                </div>
            @endif

            @if($schoolEvent->target_classes && count($schoolEvent->target_classes) > 0)
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700">Target Classes</label>
                    <div class="mt-2 flex flex-wrap gap-2">
                        @foreach($schoolEvent->target_classes as $classId)
                            @php
                                $class = \App\Models\ClassModel::find($classId);
                            @endphp
                            @if($class)
                                <span class="badge badge-blue">{{ $class->name }}</span>
                            @endif
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Event Timeline -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Event Timeline</h3>
        </div>
        <div class="p-6">
            <div class="flow-root">
                <ul class="-mb-8">
                    <li>
                        <div class="relative pb-8">
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </span>
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                    <div>
                                        <p class="text-sm text-gray-500">Event created by <span class="font-medium text-gray-900">{{ $schoolEvent->creator->name ?? 'Unknown' }}</span></p>
                                    </div>
                                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                        {{ $schoolEvent->created_at->format('M d, Y g:i A') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    
                    @if($schoolEvent->updated_at != $schoolEvent->created_at)
                        <li>
                            <div class="relative pb-8">
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">Event updated</p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            {{ $schoolEvent->updated_at->format('M d, Y g:i A') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    @endif

                    <li>
                        <div class="relative">
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-8 w-8 rounded-full {{ $schoolEvent->event_date->isFuture() ? 'bg-yellow-500' : ($schoolEvent->event_date->isToday() ? 'bg-green-500' : 'bg-gray-500') }} flex items-center justify-center ring-8 ring-white">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                    </span>
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                    <div>
                                        <p class="text-sm text-gray-500">
                                            Event 
                                            @if($schoolEvent->event_date->isFuture())
                                                scheduled
                                            @elseif($schoolEvent->event_date->isToday())
                                                happening today
                                            @else
                                                completed
                                            @endif
                                        </p>
                                    </div>
                                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                        {{ $schoolEvent->event_date->format('M d, Y') }}
                                        @if($schoolEvent->start_time)
                                            {{ $schoolEvent->start_time->format('g:i A') }}
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection
