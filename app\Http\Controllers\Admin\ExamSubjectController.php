<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ExamSubject;
use App\Models\Exam;
use App\Models\Subject;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ExamSubjectController extends Controller
{
    public function index(Request $request)
    {
        $examId = $request->get('exam_id');
        $subjectId = $request->get('subject_id');
        $date = $request->get('date');

        $query = ExamSubject::with(['exam', 'subject', 'invigilators.teacher']);

        if ($examId) {
            $query->where('exam_id', $examId);
        }

        if ($subjectId) {
            $query->where('subject_id', $subjectId);
        }

        if ($date) {
            $query->where('exam_date', $date);
        }

        $examSubjects = $query->orderBy('exam_date')->orderBy('start_time')->paginate(15);

        // Get filter options
        $exams = Exam::where('status', '!=', 'cancelled')->get();
        $subjects = Subject::where('is_active', true)->get();

        return view('admin.exam-subjects.index', compact(
            'examSubjects', 'exams', 'subjects', 'examId', 'subjectId', 'date'
        ));
    }

    public function create()
    {
        $exams = Exam::where('status', '!=', 'cancelled')->get();
        $subjects = Subject::where('is_active', true)->get();
        
        return view('admin.exam-subjects.create', compact('exams', 'subjects'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'exam_id' => 'required|exists:exams,id',
            'subject_id' => 'required|exists:subjects,id',
            'exam_date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'duration_minutes' => 'required|integer|min:1',
            'max_marks' => 'required|numeric|min:0',
            'pass_marks' => 'required|numeric|min:0|lte:max_marks',
            'exam_hall' => 'nullable|string|max:255',
            'instructions' => 'nullable|array',
        ]);

        try {
            DB::beginTransaction();

            // Check for duplicate exam-subject combination
            $existing = ExamSubject::where('exam_id', $request->exam_id)
                                  ->where('subject_id', $request->subject_id)
                                  ->first();

            if ($existing) {
                return back()->withInput()
                            ->with('error', 'This subject is already scheduled for the selected exam.');
            }

            $examSubject = ExamSubject::create([
                'exam_id' => $request->exam_id,
                'subject_id' => $request->subject_id,
                'exam_date' => $request->exam_date,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'duration_minutes' => $request->duration_minutes,
                'max_marks' => $request->max_marks,
                'pass_marks' => $request->pass_marks,
                'exam_hall' => $request->exam_hall,
                'instructions' => $request->instructions ?? [],
                'is_active' => true,
            ]);

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'created',
                'description' => "Created exam subject: {$examSubject->subject->name} for {$examSubject->exam->name}",
                'model_type' => ExamSubject::class,
                'model_id' => $examSubject->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.exam-subjects.show', $examSubject)
                            ->with('success', 'Exam subject created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to create exam subject: ' . $e->getMessage());
        }
    }

    public function show(ExamSubject $examSubject)
    {
        $examSubject->load([
            'exam.examType', 
            'subject', 
            'invigilators.teacher',
            'results.student.user'
        ]);

        // Get statistics
        $stats = [
            'total_enrolled' => $examSubject->exam->enrollments()->count(),
            'total_results' => $examSubject->results()->count(),
            'present_count' => $examSubject->results()->where('status', 'present')->count(),
            'absent_count' => $examSubject->results()->where('status', 'absent')->count(),
            'average_marks' => $examSubject->results()->where('status', 'present')->avg('marks_obtained') ?? 0,
            'pass_count' => $examSubject->results()->where('status', 'present')
                                                  ->where('marks_obtained', '>=', $examSubject->pass_marks)
                                                  ->count(),
        ];

        return view('admin.exam-subjects.show', compact('examSubject', 'stats'));
    }

    public function edit(ExamSubject $examSubject)
    {
        if ($examSubject->exam->status === 'completed') {
            return redirect()->route('admin.exam-subjects.show', $examSubject)
                            ->with('error', 'Cannot edit exam subject for completed exam.');
        }

        $exams = Exam::where('status', '!=', 'cancelled')->get();
        $subjects = Subject::where('is_active', true)->get();
        
        return view('admin.exam-subjects.edit', compact('examSubject', 'exams', 'subjects'));
    }

    public function update(Request $request, ExamSubject $examSubject)
    {
        if ($examSubject->exam->status === 'completed') {
            return redirect()->route('admin.exam-subjects.show', $examSubject)
                            ->with('error', 'Cannot edit exam subject for completed exam.');
        }

        $request->validate([
            'exam_id' => 'required|exists:exams,id',
            'subject_id' => 'required|exists:subjects,id',
            'exam_date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'duration_minutes' => 'required|integer|min:1',
            'max_marks' => 'required|numeric|min:0',
            'pass_marks' => 'required|numeric|min:0|lte:max_marks',
            'exam_hall' => 'nullable|string|max:255',
            'instructions' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            // Check for duplicate exam-subject combination (excluding current record)
            $existing = ExamSubject::where('exam_id', $request->exam_id)
                                  ->where('subject_id', $request->subject_id)
                                  ->where('id', '!=', $examSubject->id)
                                  ->first();

            if ($existing) {
                return back()->withInput()
                            ->with('error', 'This subject is already scheduled for the selected exam.');
            }

            $examSubject->update([
                'exam_id' => $request->exam_id,
                'subject_id' => $request->subject_id,
                'exam_date' => $request->exam_date,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'duration_minutes' => $request->duration_minutes,
                'max_marks' => $request->max_marks,
                'pass_marks' => $request->pass_marks,
                'exam_hall' => $request->exam_hall,
                'instructions' => $request->instructions ?? [],
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'updated',
                'description' => "Updated exam subject: {$examSubject->subject->name} for {$examSubject->exam->name}",
                'model_type' => ExamSubject::class,
                'model_id' => $examSubject->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.exam-subjects.show', $examSubject)
                            ->with('success', 'Exam subject updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to update exam subject: ' . $e->getMessage());
        }
    }

    public function destroy(ExamSubject $examSubject)
    {
        if ($examSubject->exam->status === 'completed') {
            return redirect()->route('admin.exam-subjects.index')
                            ->with('error', 'Cannot delete exam subject for completed exam.');
        }

        if ($examSubject->results()->count() > 0) {
            return redirect()->route('admin.exam-subjects.index')
                            ->with('error', 'Cannot delete exam subject that has results.');
        }

        try {
            DB::beginTransaction();

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'deleted',
                'description' => "Deleted exam subject: {$examSubject->subject->name} for {$examSubject->exam->name}",
                'model_type' => ExamSubject::class,
                'model_id' => $examSubject->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            $examSubject->delete();

            DB::commit();

            return redirect()->route('admin.exam-subjects.index')
                            ->with('success', 'Exam subject deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('admin.exam-subjects.index')
                            ->with('error', 'Failed to delete exam subject: ' . $e->getMessage());
        }
    }
}
