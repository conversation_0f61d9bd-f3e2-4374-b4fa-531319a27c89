<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SchoolEvent extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'event_date',
        'start_time',
        'end_time',
        'type',
        'scope',
        'target_classes',
        'location',
        'created_by',
        'is_active',
    ];

    protected $casts = [
        'event_date' => 'date',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'target_classes' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who created this event.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get upcoming events.
     */
    public static function upcoming($limit = 10)
    {
        return static::where('event_date', '>=', now()->toDateString())
                    ->where('is_active', true)
                    ->orderBy('event_date')
                    ->orderBy('start_time')
                    ->limit($limit);
    }

    /**
     * Get events for a specific date.
     */
    public static function forDate($date)
    {
        return static::where('event_date', $date)
                    ->where('is_active', true)
                    ->orderBy('start_time');
    }

    /**
     * Get events for a date range.
     */
    public static function forDateRange($startDate, $endDate)
    {
        return static::whereBetween('event_date', [$startDate, $endDate])
                    ->where('is_active', true)
                    ->orderBy('event_date')
                    ->orderBy('start_time');
    }

    /**
     * Get formatted time range.
     */
    public function getTimeRangeAttribute(): ?string
    {
        if (!$this->start_time) {
            return null;
        }

        $start = Carbon::parse($this->start_time)->format('g:i A');
        
        if ($this->end_time) {
            $end = Carbon::parse($this->end_time)->format('g:i A');
            return $start . ' - ' . $end;
        }

        return $start;
    }

    /**
     * Get formatted date.
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->event_date->format('M d, Y');
    }

    /**
     * Get type badge color.
     */
    public function getTypeBadgeColorAttribute(): string
    {
        return match($this->type) {
            'holiday' => 'bg-red-100 text-red-800',
            'exam' => 'bg-orange-100 text-orange-800',
            'sports' => 'bg-green-100 text-green-800',
            'cultural' => 'bg-purple-100 text-purple-800',
            'meeting' => 'bg-blue-100 text-blue-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Check if event is today.
     */
    public function isToday(): bool
    {
        return $this->event_date->isToday();
    }

    /**
     * Check if event is upcoming.
     */
    public function isUpcoming(): bool
    {
        return $this->event_date->isFuture();
    }
}
