<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AcademicYear;
use App\Models\AcademicTerm;
use App\Models\TimeSlot;
use App\Models\Schedule;
use App\Models\SchoolEvent;
use App\Models\Announcement;
use App\Models\ClassModel;
use App\Models\Section;
use App\Models\Subject;
use App\Models\User;
use Carbon\Carbon;

class AcademicSystemSeeder extends Seeder
{
    public function run(): void
    {
        // Create Academic Year
        $academicYear = AcademicYear::create([
            'name' => '2024-2025',
            'start_date' => '2024-09-01',
            'end_date' => '2025-06-30',
            'is_current' => true,
            'is_active' => true,
        ]);

        // Create Academic Terms
        $term1 = AcademicTerm::create([
            'academic_year_id' => $academicYear->id,
            'name' => 'Term 1',
            'start_date' => '2024-09-01',
            'end_date' => '2024-12-20',
            'is_current' => true,
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $term2 = AcademicTerm::create([
            'academic_year_id' => $academicYear->id,
            'name' => 'Term 2',
            'start_date' => '2025-01-06',
            'end_date' => '2025-03-28',
            'is_current' => false,
            'is_active' => true,
            'sort_order' => 2,
        ]);

        $term3 = AcademicTerm::create([
            'academic_year_id' => $academicYear->id,
            'name' => 'Term 3',
            'start_date' => '2025-04-07',
            'end_date' => '2025-06-30',
            'is_current' => false,
            'is_active' => true,
            'sort_order' => 3,
        ]);

        // Create Time Slots
        $timeSlots = [
            ['name' => 'Period 1', 'start_time' => '08:00', 'end_time' => '08:45', 'type' => 'class', 'sort_order' => 1],
            ['name' => 'Period 2', 'start_time' => '08:45', 'end_time' => '09:30', 'type' => 'class', 'sort_order' => 2],
            ['name' => 'Morning Break', 'start_time' => '09:30', 'end_time' => '09:45', 'type' => 'break', 'sort_order' => 3],
            ['name' => 'Period 3', 'start_time' => '09:45', 'end_time' => '10:30', 'type' => 'class', 'sort_order' => 4],
            ['name' => 'Period 4', 'start_time' => '10:30', 'end_time' => '11:15', 'type' => 'class', 'sort_order' => 5],
            ['name' => 'Period 5', 'start_time' => '11:15', 'end_time' => '12:00', 'type' => 'class', 'sort_order' => 6],
            ['name' => 'Lunch Break', 'start_time' => '12:00', 'end_time' => '13:00', 'type' => 'lunch', 'sort_order' => 7],
            ['name' => 'Period 6', 'start_time' => '13:00', 'end_time' => '13:45', 'type' => 'class', 'sort_order' => 8],
            ['name' => 'Period 7', 'start_time' => '13:45', 'end_time' => '14:30', 'type' => 'class', 'sort_order' => 9],
            ['name' => 'Period 8', 'start_time' => '14:30', 'end_time' => '15:15', 'type' => 'class', 'sort_order' => 10],
        ];

        foreach ($timeSlots as $slot) {
            TimeSlot::create($slot);
        }

        // Create sample schedules if classes, sections, subjects, and teachers exist
        $this->createSampleSchedules($academicYear, $term1);

        // Create School Events
        $events = [
            [
                'title' => 'Picture Day Reminder',
                'description' => 'Don\'t forget to wear your full uniform and bring your best smile!',
                'event_date' => now()->addDays(5),
                'start_time' => '09:00',
                'end_time' => '15:00',
                'type' => 'other',
                'scope' => 'all',
                'location' => 'School Hall',
                'created_by' => 1,
            ],
            [
                'title' => 'Book Fair Opening',
                'description' => 'Annual book fair opens this Thursday. Shop for the newest books to browse the newest books.',
                'event_date' => now()->addDays(3),
                'start_time' => '10:00',
                'end_time' => '16:00',
                'type' => 'cultural',
                'scope' => 'all',
                'location' => 'Library',
                'created_by' => 1,
            ],
            [
                'title' => 'Sports Day Postponed',
                'description' => 'Due to weather, Sports Day has been postponed. A new date will be announced soon.',
                'event_date' => now()->addDays(10),
                'type' => 'sports',
                'scope' => 'all',
                'location' => 'Sports Ground',
                'created_by' => 1,
            ],
            [
                'title' => 'Mid-Term Examinations',
                'description' => 'Mid-term examinations for all classes.',
                'event_date' => now()->addDays(15),
                'start_time' => '08:00',
                'end_time' => '12:00',
                'type' => 'exam',
                'scope' => 'all',
                'created_by' => 1,
            ],
        ];

        foreach ($events as $event) {
            SchoolEvent::create($event);
        }

        // Create Announcements
        $announcements = [
            [
                'title' => 'Picture Day Reminder',
                'content' => 'Picture Day is tomorrow! Don\'t forget to wear your full uniform and bring your best smile.',
                'type' => 'reminder',
                'target_audience' => 'all',
                'publish_date' => now(),
                'is_published' => true,
                'is_pinned' => true,
                'created_by' => 1,
            ],
            [
                'title' => 'Book Fair Opening',
                'content' => 'The annual book fair opens this Thursday. Shop for the newest books to browse the newest books.',
                'type' => 'event',
                'target_audience' => 'all',
                'publish_date' => now(),
                'is_published' => true,
                'is_pinned' => false,
                'created_by' => 1,
            ],
            [
                'title' => 'Sports Day Postponed',
                'content' => 'Due to weather conditions, Sports Day has been postponed. A new date will be announced soon.',
                'type' => 'urgent',
                'target_audience' => 'all',
                'publish_date' => now(),
                'is_published' => true,
                'is_pinned' => false,
                'created_by' => 1,
            ],
        ];

        foreach ($announcements as $announcement) {
            Announcement::create($announcement);
        }
    }

    private function createSampleSchedules($academicYear, $term)
    {
        // Get sample data
        $classes = ClassModel::take(3)->get();
        $sections = Section::take(2)->get();
        $subjects = Subject::take(5)->get();
        $teachers = User::where('role', 'teacher')->take(5)->get();
        $timeSlots = TimeSlot::where('type', 'class')->take(6)->get();

        if ($classes->isEmpty() || $sections->isEmpty() || $subjects->isEmpty() || $teachers->isEmpty()) {
            return; // Skip if no data available
        }

        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        
        foreach ($classes as $class) {
            foreach ($sections as $section) {
                foreach ($days as $dayIndex => $day) {
                    foreach ($timeSlots->take(4) as $slotIndex => $timeSlot) {
                        $subject = $subjects->get($slotIndex % $subjects->count());
                        $teacher = $teachers->get($slotIndex % $teachers->count());

                        Schedule::create([
                            'academic_year_id' => $academicYear->id,
                            'academic_term_id' => $term->id,
                            'class_id' => $class->id,
                            'section_id' => $section->id,
                            'subject_id' => $subject->id,
                            'teacher_id' => $teacher->id,
                            'time_slot_id' => $timeSlot->id,
                            'day_of_week' => $day,
                            'room_number' => 'Room ' . (($slotIndex + $dayIndex) % 10 + 1),
                            'is_active' => true,
                        ]);
                    }
                }
            }
        }
    }
}
