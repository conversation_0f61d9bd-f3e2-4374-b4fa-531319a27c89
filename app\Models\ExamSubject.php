<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ExamSubject extends Model
{
    use HasFactory;

    protected $fillable = [
        'exam_id',
        'subject_id',
        'exam_date',
        'start_time',
        'end_time',
        'duration_minutes',
        'max_marks',
        'pass_marks',
        'exam_hall',
        'instructions',
        'is_active',
    ];

    protected $casts = [
        'exam_date' => 'date',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'max_marks' => 'decimal:2',
        'pass_marks' => 'decimal:2',
        'instructions' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the exam.
     */
    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    /**
     * Get the subject.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the exam results.
     */
    public function results(): HasMany
    {
        return $this->hasMany(ExamResult::class);
    }

    /**
     * Get the invigilators.
     */
    public function invigilators(): HasMany
    {
        return $this->hasMany(ExamInvigilator::class);
    }

    /**
     * Get today's exams.
     */
    public static function today()
    {
        return static::where('exam_date', now()->toDateString())
                    ->where('is_active', true)
                    ->orderBy('start_time');
    }

    /**
     * Get exams for a specific date.
     */
    public static function forDate($date)
    {
        return static::where('exam_date', $date)
                    ->where('is_active', true)
                    ->orderBy('start_time');
    }

    /**
     * Check if exam is currently ongoing.
     */
    public function isOngoing(): bool
    {
        $now = now();
        $examStart = $this->exam_date->setTimeFromTimeString($this->start_time->format('H:i:s'));
        $examEnd = $this->exam_date->setTimeFromTimeString($this->end_time->format('H:i:s'));

        return $now->between($examStart, $examEnd);
    }

    /**
     * Get formatted time range.
     */
    public function getTimeRangeAttribute(): string
    {
        return $this->start_time->format('H:i') . ' - ' . $this->end_time->format('H:i');
    }
}
