<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\Guardian;
use App\Models\Teacher;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RelationshipController extends Controller
{
    /**
     * Display relationships overview
     */
    public function index()
    {
        // Get all students with their guardians
        $students = Student::with(['user', 'guardians.user'])->get();
        
        // Get all guardians with their students
        $guardians = Guardian::with(['user', 'students.user'])->get();
        
        // Get all teachers
        $teachers = Teacher::with('user')->get();
        
        // Get guardian-student relationships with details
        $relationships = DB::table('guardian_student')
            ->join('guardians', 'guardian_student.guardian_id', '=', 'guardians.id')
            ->join('students', 'guardian_student.student_id', '=', 'students.id')
            ->join('users as guardian_users', 'guardians.user_id', '=', 'guardian_users.id')
            ->join('users as student_users', 'students.user_id', '=', 'student_users.id')
            ->select(
                'guardian_users.name as guardian_name',
                'student_users.name as student_name',
                'guardians.relationship as guardian_relationship',
                'guardian_student.relationship_type',
                'students.class',
                'students.section',
                'guardian_users.phone as guardian_phone',
                'guardian_users.email as guardian_email'
            )
            ->orderBy('guardian_users.name')
            ->get();
        
        // Get families (grouped by similar addresses or last names)
        $families = $this->getFamilyGroups();
        
        // Get statistics
        $stats = [
            'total_users' => User::count(),
            'total_students' => Student::count(),
            'total_guardians' => Guardian::count(),
            'total_teachers' => Teacher::count(),
            'total_relationships' => DB::table('guardian_student')->count(),
            'primary_relationships' => DB::table('guardian_student')->where('relationship_type', 'Primary')->count(),
            'secondary_relationships' => DB::table('guardian_student')->where('relationship_type', 'Secondary')->count(),
            'emergency_relationships' => DB::table('guardian_student')->where('relationship_type', 'Emergency')->count(),
        ];
        
        return view('admin.relationships.index', compact(
            'students', 
            'guardians', 
            'teachers', 
            'relationships', 
            'families', 
            'stats'
        ));
    }
    
    /**
     * Get family groups based on similar last names or addresses
     */
    private function getFamilyGroups()
    {
        $families = [];
        
        // Group students by similar last names
        $students = Student::with(['user', 'guardians.user'])->get();
        
        foreach ($students as $student) {
            $lastName = $this->extractLastName($student->user->name);
            
            if (!isset($families[$lastName])) {
                $families[$lastName] = [
                    'family_name' => $lastName,
                    'students' => [],
                    'guardians' => [],
                ];
            }
            
            $families[$lastName]['students'][] = $student;
            
            foreach ($student->guardians as $guardian) {
                // Check if guardian is already in this family
                $guardianExists = false;
                foreach ($families[$lastName]['guardians'] as $existingGuardian) {
                    if ($existingGuardian->id === $guardian->id) {
                        $guardianExists = true;
                        break;
                    }
                }
                
                if (!$guardianExists) {
                    $families[$lastName]['guardians'][] = $guardian;
                }
            }
        }
        
        // Filter out single-member families and sort
        $families = array_filter($families, function($family) {
            return count($family['students']) > 1 || count($family['guardians']) > 1;
        });
        
        ksort($families);
        
        return $families;
    }
    
    /**
     * Extract last name from full name
     */
    private function extractLastName($fullName)
    {
        $nameParts = explode(' ', trim($fullName));
        
        // For Malaysian names, often the first part is the family name
        // But for Western names, it's usually the last part
        // We'll use a simple heuristic: if it starts with common titles, use the second part
        $titles = ['Mr.', 'Mrs.', 'Ms.', 'Dr.', 'Prof.'];
        
        if (in_array($nameParts[0], $titles) && count($nameParts) > 2) {
            return $nameParts[1]; // Use second part after title
        } elseif (count($nameParts) > 1) {
            // For names like "Lim Wei Jun", "Ahmad Rahman", use first part
            return $nameParts[0];
        }
        
        return $fullName;
    }
    
    /**
     * Show detailed view of a specific family
     */
    public function showFamily($familyName)
    {
        $families = $this->getFamilyGroups();
        
        if (!isset($families[$familyName])) {
            abort(404, 'Family not found');
        }
        
        $family = $families[$familyName];
        
        return view('admin.relationships.family', compact('family', 'familyName'));
    }
}
