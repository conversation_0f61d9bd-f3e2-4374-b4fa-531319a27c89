<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Grade;
use App\Models\GradeCategory;
use App\Models\Student;
use App\Models\Subject;
use App\Models\Teacher;
use App\Models\AcademicTerm;
use App\Models\ClassModel;
use App\Models\Section;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class GradebookController extends Controller
{
    public function index(Request $request)
    {
        $search = $request->get('search', '');
        $subjectId = $request->get('subject_id', '');
        $classId = $request->get('class_id', '');
        $sectionId = $request->get('section_id', '');
        $termId = $request->get('academic_term_id', '');
        $categoryId = $request->get('category_id', '');
        $teacherId = $request->get('teacher_id', '');

        // Get filter options
        $subjects = Subject::orderBy('name')->get();
        $classes = ClassModel::orderBy('name')->get();
        $sections = Section::orderBy('name')->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::active()->orderBy('name')->get();
        $teachers = Teacher::with('user')->get();

        // Build grades query
        $gradesQuery = Grade::with([
            'student.user',
            'subject',
            'teacher.user',
            'gradeCategory',
            'academicTerm'
        ]);

        // Apply filters
        if ($search) {
            $gradesQuery->whereHas('student.user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('subject', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhere('assignment_name', 'like', "%{$search}%");
        }

        if ($subjectId) {
            $gradesQuery->where('subject_id', $subjectId);
        }

        if ($termId) {
            $gradesQuery->where('academic_term_id', $termId);
        }

        if ($categoryId) {
            $gradesQuery->where('grade_category_id', $categoryId);
        }

        if ($teacherId) {
            $gradesQuery->where('teacher_id', $teacherId);
        }

        if ($classId || $sectionId) {
            $gradesQuery->whereHas('student', function ($q) use ($classId, $sectionId) {
                if ($classId) {
                    $q->whereHas('classModel', function ($cq) use ($classId) {
                        $cq->where('id', $classId);
                    });
                }
                if ($sectionId) {
                    $q->whereHas('section', function ($sq) use ($sectionId) {
                        $sq->where('id', $sectionId);
                    });
                }
            });
        }

        $grades = $gradesQuery->latest('assignment_date')->paginate(25);

        // Calculate statistics
        $stats = [
            'total_grades' => Grade::count(),
            'published_grades' => Grade::published()->count(),
            'average_score' => Grade::published()->avg('percentage') ?? 0,
            'recent_grades' => Grade::where('created_at', '>=', now()->subDays(7))->count(),
        ];

        return view('admin.gradebook.index', compact(
            'grades',
            'stats',
            'subjects',
            'classes',
            'sections',
            'academicTerms',
            'gradeCategories',
            'teachers',
            'search',
            'subjectId',
            'classId',
            'sectionId',
            'termId',
            'categoryId',
            'teacherId'
        ));
    }

    public function create()
    {
        $subjects = Subject::orderBy('name')->get();
        $students = Student::with('user')
            ->join('users', 'students.user_id', '=', 'users.id')
            ->orderBy('users.name')
            ->select('students.*')
            ->get();
        $teachers = Teacher::with('user')
            ->join('users', 'teachers.user_id', '=', 'users.id')
            ->orderBy('users.name')
            ->select('teachers.*')
            ->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::active()->orderBy('name')->get();

        return view('admin.gradebook.create', compact(
            'subjects',
            'students',
            'teachers',
            'academicTerms',
            'gradeCategories'
        ));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'student_id' => 'required|exists:students,id',
            'subject_id' => 'required|exists:subjects,id',
            'teacher_id' => 'required|exists:teachers,id',
            'grade_category_id' => 'required|exists:grade_categories,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'assignment_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'score' => 'required|numeric|min:0',
            'max_score' => 'required|numeric|min:0.01',
            'assignment_date' => 'required|date',
            'due_date' => 'nullable|date',
            'comments' => 'nullable|string',
            'is_published' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $grade = Grade::create($validated);
            $grade->updateLetterGrade();

            // Load relationships for logging
            $grade->load(['student.user', 'subject', 'teacher.user']);

            // Log activity
            ActivityLog::logGrade(
                'created',
                $grade,
                "Created grade '{$grade->assignment_name}' for {$grade->student->user->name} in {$grade->subject->name}"
            );

            DB::commit();

            return redirect()->route('admin.gradebook.index')
                ->with('success', 'Grade added successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Failed to create grade. Please try again.');
        }
    }

    public function show(Grade $gradebook)
    {
        $gradebook->load([
            'student.user',
            'subject',
            'teacher.user',
            'gradeCategory',
            'academicTerm'
        ]);

        return view('admin.gradebook.show', compact('gradebook'));
    }

    public function edit(Grade $gradebook)
    {
        $gradebook->load([
            'student.user',
            'subject',
            'teacher.user',
            'gradeCategory',
            'academicTerm'
        ]);

        $subjects = Subject::orderBy('name')->get();
        $students = Student::with('user')
            ->join('users', 'students.user_id', '=', 'users.id')
            ->orderBy('users.name')
            ->select('students.*')
            ->get();
        $teachers = Teacher::with('user')
            ->join('users', 'teachers.user_id', '=', 'users.id')
            ->orderBy('users.name')
            ->select('teachers.*')
            ->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::active()->orderBy('name')->get();

        return view('admin.gradebook.edit', compact(
            'gradebook',
            'subjects',
            'students',
            'teachers',
            'academicTerms',
            'gradeCategories'
        ));
    }

    public function update(Request $request, Grade $gradebook)
    {
        $validated = $request->validate([
            'student_id' => 'required|exists:students,id',
            'subject_id' => 'required|exists:subjects,id',
            'teacher_id' => 'required|exists:teachers,id',
            'grade_category_id' => 'required|exists:grade_categories,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'assignment_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'score' => 'required|numeric|min:0',
            'max_score' => 'required|numeric|min:0.01',
            'assignment_date' => 'required|date',
            'due_date' => 'nullable|date',
            'comments' => 'nullable|string',
            'is_published' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            // Store old data for logging
            $oldData = $gradebook->toArray();
            $gradebook->load(['student.user', 'subject', 'teacher.user']);

            $gradebook->update($validated);
            $gradebook->updateLetterGrade();

            // Log activity
            ActivityLog::logGrade(
                'updated',
                $gradebook,
                "Updated grade '{$gradebook->assignment_name}' for {$gradebook->student->user->name} in {$gradebook->subject->name}",
                [
                    'old_score' => $oldData['score'],
                    'new_score' => $gradebook->score,
                    'old_percentage' => $oldData['percentage'],
                    'new_percentage' => $gradebook->percentage,
                    'old_published' => $oldData['is_published'],
                    'new_published' => $gradebook->is_published,
                ]
            );

            DB::commit();

            return redirect()->route('admin.gradebook.index')
                ->with('success', 'Grade updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Failed to update grade. Please try again.');
        }
    }

    public function destroy(Grade $gradebook)
    {
        try {
            DB::beginTransaction();

            // Load relationships for logging before deletion
            $gradebook->load(['student.user', 'subject', 'teacher.user']);

            // Store grade info for logging
            $gradeInfo = [
                'assignment_name' => $gradebook->assignment_name,
                'student_name' => $gradebook->student->user->name,
                'subject_name' => $gradebook->subject->name,
                'score' => $gradebook->score,
                'max_score' => $gradebook->max_score,
                'percentage' => $gradebook->percentage,
            ];

            $gradebook->delete();

            // Log activity
            ActivityLog::log(
                'deleted_grade',
                "Deleted grade '{$gradeInfo['assignment_name']}' for {$gradeInfo['student_name']} in {$gradeInfo['subject_name']}",
                'App\Models\Grade',
                null,
                $gradeInfo
            );

            DB::commit();

            return redirect()->route('admin.gradebook.index')
                ->with('success', 'Grade deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to delete grade. Please try again.');
        }
    }

    /**
     * Bulk import grades from CSV
     */
    public function bulkImport(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt',
            'grade_category_id' => 'required|exists:grade_categories,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'teacher_id' => 'required|exists:teachers,id',
            'is_published' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $file = $request->file('csv_file');
            $csvData = array_map('str_getcsv', file($file->getRealPath()));
            $header = array_shift($csvData);

            // Expected columns: student_id, subject_id, assignment_name, score, max_score, assignment_date
            $expectedColumns = ['student_id', 'subject_id', 'assignment_name', 'score', 'max_score', 'assignment_date'];

            // Validate CSV headers
            $missingColumns = array_diff($expectedColumns, $header);
            if (!empty($missingColumns)) {
                return back()->with('error', 'Missing required columns: ' . implode(', ', $missingColumns));
            }

            $importedCount = 0;
            $errors = [];
            $importedGrades = [];

            foreach ($csvData as $rowIndex => $row) {
                if (count($row) !== count($header)) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Invalid number of columns";
                    continue;
                }

                $rowData = array_combine($header, $row);

                try {
                    // Validate student exists
                    $student = Student::where('student_id', $rowData['student_id'])->first();
                    if (!$student) {
                        $errors[] = "Row " . ($rowIndex + 2) . ": Student ID {$rowData['student_id']} not found";
                        continue;
                    }

                    // Validate subject exists
                    $subject = Subject::find($rowData['subject_id']);
                    if (!$subject) {
                        $errors[] = "Row " . ($rowIndex + 2) . ": Subject ID {$rowData['subject_id']} not found";
                        continue;
                    }

                    // Create grade
                    $grade = Grade::create([
                        'student_id' => $student->id,
                        'subject_id' => $subject->id,
                        'teacher_id' => $request->teacher_id,
                        'grade_category_id' => $request->grade_category_id,
                        'academic_term_id' => $request->academic_term_id,
                        'assignment_name' => $rowData['assignment_name'],
                        'score' => (float) $rowData['score'],
                        'max_score' => (float) $rowData['max_score'],
                        'assignment_date' => $rowData['assignment_date'],
                        'is_published' => $request->boolean('is_published', false),
                    ]);

                    $grade->updateLetterGrade();
                    $importedGrades[] = $grade->assignment_name . ' (' . $student->user->name . ')';
                    $importedCount++;

                } catch (\Exception $e) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": " . $e->getMessage();
                }
            }

            // Log bulk import activity
            if ($importedCount > 0) {
                ActivityLog::logBulkGrades(
                    'imported',
                    $importedCount,
                    "Bulk imported {$importedCount} grades from CSV file",
                    [
                        'filename' => $file->getClientOriginalName(),
                        'teacher_id' => $request->teacher_id,
                        'grade_category_id' => $request->grade_category_id,
                        'academic_term_id' => $request->academic_term_id,
                        'is_published' => $request->boolean('is_published', false),
                        'imported_grades' => array_slice($importedGrades, 0, 10), // Store first 10 for reference
                        'total_rows_processed' => count($csvData),
                        'errors_count' => count($errors),
                    ]
                );
            }

            DB::commit();

            $message = "Successfully imported {$importedCount} grades.";
            if (!empty($errors)) {
                $message .= " " . count($errors) . " errors occurred.";
            }

            return redirect()->route('admin.gradebook.index')
                ->with('success', $message)
                ->with('import_errors', $errors);

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to import grades: ' . $e->getMessage());
        }
    }

    /**
     * Export grades to CSV or Excel
     */
    public function export(Request $request, $format = 'csv')
    {
        $search = $request->get('search', '');
        $subjectId = $request->get('subject_id', '');
        $classId = $request->get('class_id', '');
        $sectionId = $request->get('section_id', '');
        $termId = $request->get('academic_term_id', '');
        $categoryId = $request->get('category_id', '');
        $teacherId = $request->get('teacher_id', '');

        // Build grades query with same filters as index
        $gradesQuery = Grade::with([
            'student.user',
            'subject',
            'teacher.user',
            'gradeCategory',
            'academicTerm'
        ]);

        // Apply filters (same logic as index method)
        if ($search) {
            $gradesQuery->whereHas('student.user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('subject', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhere('assignment_name', 'like', "%{$search}%");
        }

        if ($subjectId) $gradesQuery->where('subject_id', $subjectId);
        if ($termId) $gradesQuery->where('academic_term_id', $termId);
        if ($categoryId) $gradesQuery->where('grade_category_id', $categoryId);
        if ($teacherId) $gradesQuery->where('teacher_id', $teacherId);

        if ($classId || $sectionId) {
            $gradesQuery->whereHas('student', function ($q) use ($classId, $sectionId) {
                if ($classId) {
                    $q->whereHas('classModel', function ($cq) use ($classId) {
                        $cq->where('id', $classId);
                    });
                }
                if ($sectionId) {
                    $q->whereHas('section', function ($sq) use ($sectionId) {
                        $sq->where('id', $sectionId);
                    });
                }
            });
        }

        $grades = $gradesQuery->latest('assignment_date')->get();

        $filename = 'gradebook_export_' . Carbon::now()->format('Y_m_d_H_i_s') . '.' . $format;

        // Log export activity
        ActivityLog::logBulkGrades(
            'exported',
            $grades->count(),
            "Exported {$grades->count()} grades to {$format} format",
            [
                'format' => $format,
                'filename' => $filename,
                'filters' => compact('search', 'subjectId', 'classId', 'sectionId', 'termId', 'categoryId', 'teacherId'),
            ]
        );

        if ($format === 'csv') {
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];

            $callback = function() use ($grades) {
                $file = fopen('php://output', 'w');

                // CSV headers
                fputcsv($file, [
                    'Student ID',
                    'Student Name',
                    'Subject',
                    'Assignment Name',
                    'Score',
                    'Max Score',
                    'Percentage',
                    'Letter Grade',
                    'Category',
                    'Term',
                    'Teacher',
                    'Assignment Date',
                    'Published',
                    'Comments'
                ]);

                // CSV data
                foreach ($grades as $grade) {
                    fputcsv($file, [
                        $grade->student->student_id ?? '',
                        $grade->student->user->name ?? '',
                        $grade->subject->name ?? '',
                        $grade->assignment_name,
                        $grade->score,
                        $grade->max_score,
                        round($grade->percentage, 2),
                        $grade->letter_grade,
                        $grade->gradeCategory->name ?? '',
                        $grade->academicTerm->name ?? '',
                        $grade->teacher->user->name ?? '',
                        $grade->assignment_date,
                        $grade->is_published ? 'Yes' : 'No',
                        $grade->comments ?? ''
                    ]);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        }

        // For other formats, redirect back with error
        return back()->with('error', 'Export format not supported.');
    }
}
