@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Section Details"
        description="View and manage section information"
        :back-route="route('admin.academic.sections.index')"
        back-label="Back to Sections">
        
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.academic.sections.edit', $section) }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Section
            </a>
        </div>
    </x-page-header>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Section Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="card">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    <div class="flex items-center space-x-2">
                        <span class="badge badge-{{ $section->class->level_badge_color }}">
                            {{ $section->class->level }}
                        </span>
                        <span class="badge badge-{{ $section->status_badge_color }}">
                            {{ $section->status_display }}
                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Section Name</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $section->name }}</dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Class</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $section->class->name }}</dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Capacity</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $section->capacity }} students</dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Current Enrollment</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $section->current_enrollment }} students</dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Available Spots</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $section->available_spots }} spots</dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500">Enrollment Percentage</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $section->enrollment_percentage }}%</dd>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="space-y-6">
            <!-- Statistics -->
            <div class="card">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Capacity</span>
                        <span class="text-sm font-medium text-gray-900">{{ $section->capacity }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Enrolled</span>
                        <span class="text-sm font-medium text-gray-900">{{ $section->current_enrollment }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Available</span>
                        <span class="text-sm font-medium text-gray-900">{{ $section->available_spots }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Utilization</span>
                        <span class="text-sm font-medium text-gray-900">{{ $section->enrollment_percentage }}%</span>
                    </div>
                </div>
            </div>

            <!-- Enrollment Status -->
            <div class="card">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Enrollment Status</h3>
                <div class="space-y-3">
                    @if($section->is_full)
                        <div class="flex items-center p-3 bg-red-50 rounded-lg">
                            <svg class="w-5 h-5 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <span class="text-sm text-red-700">Section is at full capacity</span>
                        </div>
                    @elseif($section->enrollment_percentage >= 90)
                        <div class="flex items-center p-3 bg-yellow-50 rounded-lg">
                            <svg class="w-5 h-5 text-yellow-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <span class="text-sm text-yellow-700">Section is nearly full</span>
                        </div>
                    @else
                        <div class="flex items-center p-3 bg-green-50 rounded-lg">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-sm text-green-700">Section has available spots</span>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
