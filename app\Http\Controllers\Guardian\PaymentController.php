<?php

namespace App\Http\Controllers\Guardian;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Services\BillplzService;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    protected $billplzService;

    public function __construct(BillplzService $billplzService)
    {
        $this->billplzService = $billplzService;
    }

    /**
     * Display a listing of payments made by the guardian
     */
    public function index(Request $request)
    {
        $guardian = auth()->user()->guardian;
        
        if (!$guardian) {
            return redirect()->route('guardian.dashboard')
                            ->with('error', 'Guardian profile not found.');
        }

        $query = Payment::with(['invoice.student.user'])
                       ->where('paid_by', auth()->id());

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Filter by student
        if ($request->filled('student_id')) {
            $query->whereHas('invoice', function ($invoiceQuery) use ($request) {
                $invoiceQuery->where('student_id', $request->student_id);
            });
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by payment reference or invoice number
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('payment_reference', 'like', "%{$search}%")
                  ->orWhere('billplz_bill_id', 'like', "%{$search}%")
                  ->orWhereHas('invoice', function ($invoiceQuery) use ($search) {
                      $invoiceQuery->where('invoice_number', 'like', "%{$search}%")
                                  ->orWhere('title', 'like', "%{$search}%");
                  });
            });
        }

        $payments = $query->latest()->paginate(15);

        $stats = [
            'total' => Payment::where('paid_by', auth()->id())->count(),
            'pending' => Payment::where('paid_by', auth()->id())->where('status', 'pending')->count(),
            'paid' => Payment::where('paid_by', auth()->id())->where('status', 'paid')->count(),
            'failed' => Payment::where('paid_by', auth()->id())->where('status', 'failed')->count(),
            'total_amount_paid' => Payment::where('paid_by', auth()->id())
                                         ->where('status', 'paid')
                                         ->sum('amount'),
            'pending_amount' => Payment::where('paid_by', auth()->id())
                                      ->where('status', 'pending')
                                      ->sum('amount'),
        ];

        $students = $guardian->students;

        return view('guardian.payments.index', compact('payments', 'stats', 'students'));
    }

    /**
     * Display the specified payment
     */
    public function show(Payment $payment)
    {
        // Check if this payment belongs to the authenticated guardian
        if ($payment->paid_by !== auth()->id()) {
            abort(403, 'Unauthorized access to this payment.');
        }

        $payment->load(['invoice.student.user', 'payer']);

        // Get Billplz bill information if available
        $billplzInfo = null;
        if ($payment->billplz_bill_id) {
            $billplzInfo = $this->billplzService->getBill($payment->billplz_bill_id);
        }

        return view('guardian.payments.show', compact('payment', 'billplzInfo'));
    }

    /**
     * Download payment receipt
     */
    public function receipt(Payment $payment)
    {
        // Check if this payment belongs to the authenticated guardian
        if ($payment->paid_by !== auth()->id()) {
            abort(403, 'Unauthorized access to this payment.');
        }

        // Only allow receipt download for successful payments
        if ($payment->status !== 'paid') {
            return redirect()->route('guardian.payments.show', $payment)
                            ->with('error', 'Receipt is only available for successful payments.');
        }

        $payment->load(['invoice.student.user', 'payer']);

        // TODO: Generate PDF receipt using a PDF library like DomPDF or wkhtmltopdf
        // For now, return a simple view
        return view('guardian.payments.receipt', compact('payment'));
    }

    /**
     * Get payment summary for dashboard
     */
    public function getSummary()
    {
        $guardian = auth()->user()->guardian;
        
        if (!$guardian) {
            return response()->json(['error' => 'Guardian profile not found'], 404);
        }

        $thisMonth = now()->startOfMonth();
        $thisYear = now()->startOfYear();

        $summary = [
            'this_month' => [
                'count' => Payment::where('paid_by', auth()->id())
                                 ->where('status', 'paid')
                                 ->where('paid_at', '>=', $thisMonth)
                                 ->count(),
                'amount' => Payment::where('paid_by', auth()->id())
                                  ->where('status', 'paid')
                                  ->where('paid_at', '>=', $thisMonth)
                                  ->sum('amount'),
            ],
            'this_year' => [
                'count' => Payment::where('paid_by', auth()->id())
                                 ->where('status', 'paid')
                                 ->where('paid_at', '>=', $thisYear)
                                 ->count(),
                'amount' => Payment::where('paid_by', auth()->id())
                                  ->where('status', 'paid')
                                  ->where('paid_at', '>=', $thisYear)
                                  ->sum('amount'),
            ],
            'pending' => [
                'count' => Payment::where('paid_by', auth()->id())
                                 ->where('status', 'pending')
                                 ->count(),
                'amount' => Payment::where('paid_by', auth()->id())
                                  ->where('status', 'pending')
                                  ->sum('amount'),
            ],
            'recent_payments' => Payment::where('paid_by', auth()->id())
                                       ->where('status', 'paid')
                                       ->with(['invoice.student.user'])
                                       ->latest('paid_at')
                                       ->take(5)
                                       ->get(),
        ];

        return response()->json($summary);
    }

    /**
     * Export payments to CSV
     */
    public function export(Request $request)
    {
        $guardian = auth()->user()->guardian;
        
        if (!$guardian) {
            return redirect()->route('guardian.dashboard')
                            ->with('error', 'Guardian profile not found.');
        }

        $query = Payment::with(['invoice.student.user'])
                       ->where('paid_by', auth()->id());

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('student_id')) {
            $query->whereHas('invoice', function ($invoiceQuery) use ($request) {
                $invoiceQuery->where('student_id', $request->student_id);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $payments = $query->get();

        $filename = 'my_payments_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($payments) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Payment Reference',
                'Invoice Number',
                'Student Name',
                'Amount',
                'Status',
                'Payment Method',
                'Billplz Bill ID',
                'Paid At',
                'Created At',
            ]);

            // CSV data
            foreach ($payments as $payment) {
                fputcsv($file, [
                    $payment->payment_reference,
                    $payment->invoice->invoice_number,
                    $payment->invoice->student->user->name,
                    $payment->amount,
                    $payment->status,
                    $payment->payment_method,
                    $payment->billplz_bill_id,
                    $payment->paid_at ? $payment->paid_at->format('Y-m-d H:i:s') : '',
                    $payment->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
