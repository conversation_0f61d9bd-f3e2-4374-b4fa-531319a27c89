<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Subject;
use App\Models\ClassModel;

class SubjectSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create some sample classes first
        $classes = [
            ['name' => 'Grade 1', 'level' => 'Primary', 'sort_order' => 1],
            ['name' => 'Grade 2', 'level' => 'Primary', 'sort_order' => 2],
            ['name' => 'Grade 3', 'level' => 'Primary', 'sort_order' => 3],
            ['name' => 'Grade 4', 'level' => 'Primary', 'sort_order' => 4],
            ['name' => 'Grade 5', 'level' => 'Primary', 'sort_order' => 5],
            ['name' => 'Grade 6', 'level' => 'Primary', 'sort_order' => 6],
            ['name' => 'Form 1', 'level' => 'Secondary', 'sort_order' => 7],
            ['name' => 'Form 2', 'level' => 'Secondary', 'sort_order' => 8],
            ['name' => 'Form 3', 'level' => 'Secondary', 'sort_order' => 9],
            ['name' => 'Form 4', 'level' => 'Secondary', 'sort_order' => 10],
            ['name' => 'Form 5', 'level' => 'Secondary', 'sort_order' => 11],
        ];

        foreach ($classes as $classData) {
            ClassModel::firstOrCreate(
                ['name' => $classData['name']],
                $classData
            );
        }

        // Core subjects for all levels
        $coreSubjects = [
            [
                'subject_code' => 'ENG001',
                'name' => 'English Language',
                'description' => 'Fundamental English language skills including reading, writing, speaking, and listening.',
                'category' => 'Core',
                'credits' => 4,
                'grade_levels' => ['Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'subject_code' => 'MATH001',
                'name' => 'Mathematics',
                'description' => 'Basic mathematical concepts, arithmetic, algebra, and problem-solving skills.',
                'category' => 'Core',
                'credits' => 4,
                'grade_levels' => ['Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'subject_code' => 'SCI001',
                'name' => 'Science',
                'description' => 'Introduction to scientific concepts, observation, and experimentation.',
                'category' => 'Core',
                'credits' => 3,
                'grade_levels' => ['Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6'],
            ],
            [
                'subject_code' => 'BM001',
                'name' => 'Bahasa Malaysia',
                'description' => 'National language proficiency and communication skills.',
                'category' => 'Core',
                'credits' => 4,
                'grade_levels' => ['Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
        ];

        // Secondary level core subjects
        $secondarySubjects = [
            [
                'subject_code' => 'PHY001',
                'name' => 'Physics',
                'description' => 'Study of matter, energy, and their interactions.',
                'category' => 'Core',
                'credits' => 3,
                'grade_levels' => ['Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'subject_code' => 'CHEM001',
                'name' => 'Chemistry',
                'description' => 'Study of chemical elements, compounds, and reactions.',
                'category' => 'Core',
                'credits' => 3,
                'grade_levels' => ['Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'subject_code' => 'BIO001',
                'name' => 'Biology',
                'description' => 'Study of living organisms and life processes.',
                'category' => 'Core',
                'credits' => 3,
                'grade_levels' => ['Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'subject_code' => 'HIST001',
                'name' => 'History',
                'description' => 'Study of past events and their significance.',
                'category' => 'Core',
                'credits' => 2,
                'grade_levels' => ['Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'subject_code' => 'GEO001',
                'name' => 'Geography',
                'description' => 'Study of Earth\'s physical features and human activities.',
                'category' => 'Core',
                'credits' => 2,
                'grade_levels' => ['Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
        ];

        // Elective subjects
        $electiveSubjects = [
            [
                'subject_code' => 'ART001',
                'name' => 'Visual Arts',
                'description' => 'Creative expression through drawing, painting, and design.',
                'category' => 'Elective',
                'credits' => 2,
                'grade_levels' => ['Grade 4', 'Grade 5', 'Grade 6', 'Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'subject_code' => 'MUS001',
                'name' => 'Music',
                'description' => 'Music theory, appreciation, and performance skills.',
                'category' => 'Elective',
                'credits' => 2,
                'grade_levels' => ['Grade 4', 'Grade 5', 'Grade 6', 'Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'subject_code' => 'ICT001',
                'name' => 'Information Technology',
                'description' => 'Computer literacy and digital skills.',
                'category' => 'Elective',
                'credits' => 2,
                'grade_levels' => ['Grade 4', 'Grade 5', 'Grade 6', 'Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'subject_code' => 'PE001',
                'name' => 'Physical Education',
                'description' => 'Physical fitness, sports, and health education.',
                'category' => 'Elective',
                'credits' => 1,
                'grade_levels' => ['Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
        ];

        // Extra-curricular subjects
        $extraSubjects = [
            [
                'subject_code' => 'CLUB001',
                'name' => 'Drama Club',
                'description' => 'Theater arts and performance skills.',
                'category' => 'Extra-curricular',
                'credits' => 1,
                'grade_levels' => ['Grade 4', 'Grade 5', 'Grade 6', 'Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'subject_code' => 'CLUB002',
                'name' => 'Debate Club',
                'description' => 'Public speaking and argumentation skills.',
                'category' => 'Extra-curricular',
                'credits' => 1,
                'grade_levels' => ['Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
            [
                'subject_code' => 'CLUB003',
                'name' => 'Science Club',
                'description' => 'Advanced scientific experiments and projects.',
                'category' => 'Extra-curricular',
                'credits' => 1,
                'grade_levels' => ['Grade 4', 'Grade 5', 'Grade 6', 'Form 1', 'Form 2', 'Form 3', 'Form 4', 'Form 5'],
            ],
        ];

        // Create all subjects
        $allSubjects = array_merge($coreSubjects, $secondarySubjects, $electiveSubjects, $extraSubjects);

        foreach ($allSubjects as $subjectData) {
            Subject::firstOrCreate(
                ['subject_code' => $subjectData['subject_code']],
                $subjectData
            );
        }

        // Set up some prerequisites (advanced subjects require basic ones)
        $mathSubject = Subject::where('subject_code', 'MATH001')->first();
        $physicsSubject = Subject::where('subject_code', 'PHY001')->first();
        $chemistrySubject = Subject::where('subject_code', 'CHEM001')->first();

        if ($mathSubject && $physicsSubject) {
            $physicsSubject->update(['prerequisites' => [$mathSubject->id]]);
        }

        if ($mathSubject && $chemistrySubject) {
            $chemistrySubject->update(['prerequisites' => [$mathSubject->id]]);
        }
    }
}
