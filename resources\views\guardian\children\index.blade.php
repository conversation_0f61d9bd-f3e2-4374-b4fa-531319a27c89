@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Children</h1>
                <p class="text-gray-600">View your children's information and progress</p>
            </div>
            <a href="{{ route('guardian.dashboard') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Children List -->
    @if($students->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            @foreach($students as $student)
                <div class="bg-white shadow rounded-lg overflow-hidden">
                    <div class="px-6 py-4 bg-blue-50 border-b border-blue-100">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span class="text-blue-600 font-medium text-lg">
                                        {{ substr($student->user->name, 0, 2) }}
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-900">{{ $student->user->name }}</h3>
                                <p class="text-sm text-gray-600">{{ $student->class }} - {{ $student->section }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="px-6 py-4">
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-3 sm:grid-cols-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Student ID</dt>
                                <dd class="text-sm text-gray-900">{{ $student->student_id }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Roll Number</dt>
                                <dd class="text-sm text-gray-900">{{ $student->roll_number }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Date of Birth</dt>
                                <dd class="text-sm text-gray-900">{{ $student->date_of_birth->format('M j, Y') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Gender</dt>
                                <dd class="text-sm text-gray-900">{{ ucfirst($student->gender) }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Blood Group</dt>
                                <dd class="text-sm text-gray-900">{{ $student->blood_group ?? 'N/A' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Admission Date</dt>
                                <dd class="text-sm text-gray-900">{{ $student->admission_date->format('M j, Y') }}</dd>
                            </div>
                        </dl>
                        
                        @if($student->medical_conditions)
                            <div class="mt-4">
                                <dt class="text-sm font-medium text-gray-500">Medical Conditions</dt>
                                <dd class="text-sm text-gray-900 mt-1">{{ $student->medical_conditions }}</dd>
                            </div>
                        @endif
                    </div>
                    
                    <!-- Attendance Summary -->
                    @php
                        $attendanceStats = \App\Models\StudentAttendance::getStudentStats(
                            $student->id,
                            now()->startOfMonth()->format('Y-m-d'),
                            now()->format('Y-m-d')
                        );
                    @endphp
                    <div class="px-6 py-4 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Attendance (Current Month)</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
                            <div class="text-center">
                                <div class="text-lg font-bold text-green-600">{{ $attendanceStats['present'] }}</div>
                                <div class="text-xs text-gray-500">Present</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-red-600">{{ $attendanceStats['absent'] }}</div>
                                <div class="text-xs text-gray-500">Absent</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-blue-600">{{ $attendanceStats['excused'] }}</div>
                                <div class="text-xs text-gray-500">Excused</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-bold text-purple-600">{{ $attendanceStats['attendance_percentage'] }}%</div>
                                <div class="text-xs text-gray-500">Rate</div>
                            </div>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $attendanceStats['attendance_percentage'] }}%"></div>
                        </div>
                    </div>

                    <!-- Academic Information -->
                    <div class="px-6 py-4 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Academic Information</h4>
                        <div class="text-sm text-gray-600">
                            <p>Class: {{ $student->class ?? 'Not assigned' }}</p>
                            <p>Section: {{ $student->section ?? 'Not assigned' }}</p>
                        </div>
                    </div>

                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                        <div class="flex space-x-3">
                            <a href="#" class="flex-1 bg-blue-50 text-blue-700 text-sm px-3 py-2 rounded hover:bg-blue-100 text-center inline-flex items-center justify-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                View Grades
                            </a>
                            <a href="{{ route('guardian.invoices.index', ['student_id' => $student->id]) }}" class="flex-1 bg-green-50 text-green-700 text-sm px-3 py-2 rounded hover:bg-green-100 text-center inline-flex items-center justify-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                View Invoices
                            </a>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <div class="bg-white shadow rounded-lg p-6">
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No children found</h3>
                <p class="mt-1 text-sm text-gray-500">No children are associated with your account.</p>
                <div class="mt-6">
                    <a href="{{ route('guardian.dashboard') }}" class="btn-secondary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    @endif


</div>
@endsection
