@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Edit Subject"
        description="Update subject information"
        :back-route="route('admin.academic.subjects.show', $subject)"
        back-label="Back to Subject">
    </x-page-header>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <form action="{{ route('admin.academic.subjects.update', $subject) }}" method="POST" class="space-y-6 p-6">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Subject Code -->
                <div>
                    <label for="subject_code" class="block text-sm font-medium text-gray-700 mb-2">
                        Subject Code <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="subject_code" 
                           name="subject_code" 
                           value="{{ old('subject_code', $subject->subject_code) }}"
                           class="form-input @error('subject_code') border-red-500 @enderror"
                           placeholder="e.g., MATH101, ENG201"
                           required>
                    @error('subject_code')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Subject Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Subject Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ old('name', $subject->name) }}"
                           class="form-input @error('name') border-red-500 @enderror"
                           placeholder="e.g., Mathematics, English Literature"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                        Category <span class="text-red-500">*</span>
                    </label>
                    <select id="category" 
                            name="category" 
                            class="form-select @error('category') border-red-500 @enderror"
                            required>
                        <option value="">Select Category</option>
                        <option value="Core" {{ old('category', $subject->category) === 'Core' ? 'selected' : '' }}>Core</option>
                        <option value="Elective" {{ old('category', $subject->category) === 'Elective' ? 'selected' : '' }}>Elective</option>
                        <option value="Extra-curricular" {{ old('category', $subject->category) === 'Extra-curricular' ? 'selected' : '' }}>Extra-curricular</option>
                    </select>
                    @error('category')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Credits -->
                <div>
                    <label for="credits" class="block text-sm font-medium text-gray-700 mb-2">
                        Credits <span class="text-red-500">*</span>
                    </label>
                    <input type="number" 
                           id="credits" 
                           name="credits" 
                           value="{{ old('credits', $subject->credits) }}"
                           min="1" 
                           max="10"
                           class="form-input @error('credits') border-red-500 @enderror"
                           required>
                    @error('credits')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="3"
                          class="form-textarea @error('description') border-red-500 @enderror"
                          placeholder="Brief description of the subject...">{{ old('description', $subject->description) }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Grade Levels -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Grade Levels
                </label>
                <p class="text-sm text-gray-500 mb-3">Select which grade levels this subject is available for (leave empty for all grades)</p>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    @foreach($classes as $class)
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   name="grade_levels[]" 
                                   value="{{ $class->name }}"
                                   {{ in_array($class->name, old('grade_levels', $subject->grade_levels ?? [])) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">{{ $class->name }}</span>
                        </label>
                    @endforeach
                </div>
                @error('grade_levels')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Prerequisites -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Prerequisites
                </label>
                <p class="text-sm text-gray-500 mb-3">Select subjects that are required before taking this subject</p>

                @if($existingSubjects->count() > 0)
                    <!-- Multiselect Search Component -->
                    <div x-data="multiselectSearch()" class="relative"
                         x-init="
                            items = [
                                @foreach($existingSubjects as $existingSubject)
                                {
                                    id: {{ $existingSubject->id }},
                                    name: '{{ addslashes($existingSubject->name) }}',
                                    subtitle: '{{ addslashes($existingSubject->subject_code . ' • ' . $existingSubject->category . ' • ' . $existingSubject->credits . ' credits') }}',
                                    searchText: '{{ addslashes($existingSubject->name . ' ' . $existingSubject->subject_code . ' ' . $existingSubject->category) }}'
                                },
                                @endforeach
                            ];
                            selected = [{{ implode(',', old('prerequisites', $subject->prerequisites ?? [])) }}];
                            name = 'prerequisites[]';
                            placeholder = 'Search and select prerequisite subjects...';
                            init();
                         ">

                        <!-- Search Input -->
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input
                                type="text"
                                x-model="searchQuery"
                                @focus="showDropdown = true"
                                @click.away="showDropdown = false"
                                class="search-input"
                                :placeholder="placeholder"
                                autocomplete="off"
                            >
                        </div>

                        <!-- Selected Items -->
                        <div x-show="selectedItems.length > 0" class="mt-3">
                            <div class="flex flex-wrap gap-2">
                                <template x-for="item in selectedItems" :key="item.id">
                                    <span class="inline-flex items-center px-3 py-1 rounded-md text-sm bg-blue-100 text-blue-800">
                                        <span x-text="item.name"></span>
                                        <button type="button" @click="removeItem(item.id)" class="ml-2 text-blue-600 hover:text-blue-800">
                                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </span>
                                </template>
                            </div>
                        </div>

                        <!-- Dropdown -->
                        <div x-show="showDropdown && filteredItems.length > 0"
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                            <template x-for="item in filteredItems" :key="item.id">
                                <div @click="toggleItem(item)"
                                     class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-blue-50">
                                    <div class="flex items-center">
                                        <span class="font-medium block truncate" x-text="item.name"></span>
                                    </div>
                                    <span class="text-gray-500 text-sm block truncate" x-text="item.subtitle"></span>
                                </div>
                            </template>
                        </div>

                        <!-- Hidden inputs for selected items -->
                        <template x-for="item in selectedItems" :key="item.id">
                            <input type="hidden" :name="name" :value="item.id">
                        </template>
                    </div>
                @else
                    <div class="text-center py-4">
                        <p class="text-sm text-gray-500">No other subjects available for prerequisites.</p>
                    </div>
                @endif

                @error('prerequisites')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Status -->
            <div>
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="is_active" 
                           name="is_active" 
                           value="1"
                           {{ old('is_active', $subject->is_active) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="is_active" class="ml-2 text-sm text-gray-700">
                        Active (subject is available for assignment)
                    </label>
                </div>
                @error('is_active')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.academic.subjects.show', $subject) }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Subject
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
function multiselectSearch() {
    return {
        items: [],
        selectedItems: [],
        searchQuery: '',
        showDropdown: false,
        name: 'items[]',
        placeholder: 'Search and select items...',
        selected: [],

        init() {
            // Initialize selected items based on this.selected
            if (this.selected && this.selected.length > 0) {
                this.selectedItems = this.items.filter(item =>
                    this.selected.includes(item.id)
                );
            }
        },

        get filteredItems() {
            if (!this.searchQuery) {
                return this.items.filter(item => !this.isSelected(item.id));
            }

            return this.items.filter(item => {
                const matchesSearch = item.searchText.toLowerCase().includes(this.searchQuery.toLowerCase());
                const notSelected = !this.isSelected(item.id);
                return matchesSearch && notSelected;
            });
        },

        isSelected(itemId) {
            return this.selectedItems.some(item => item.id == itemId);
        },

        toggleItem(item) {
            if (this.isSelected(item.id)) {
                this.removeItem(item.id);
            } else {
                this.selectedItems.push(item);
                this.searchQuery = '';
                this.showDropdown = false;
            }
        },

        removeItem(itemId) {
            this.selectedItems = this.selectedItems.filter(item => item.id != itemId);
        }
    }
}
</script>
@endpush

@endsection
