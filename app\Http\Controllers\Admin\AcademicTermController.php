<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AcademicTerm;
use App\Models\AcademicYear;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AcademicTermController extends Controller
{
    public function index()
    {
        $academicTerms = AcademicTerm::with('academicYear')
                                   ->orderBy('academic_year_id', 'desc')
                                   ->orderBy('sort_order')
                                   ->get();

        $academicYears = AcademicYear::where('is_active', true)->get();

        // Calculate statistics
        $stats = [
            'total' => $academicTerms->count(),
            'current' => $academicTerms->where('is_current', true)->count(),
            'active' => $academicTerms->where('is_active', true)->count(),
        ];

        return view('admin.academic-terms.index', compact('academicTerms', 'academicYears', 'stats'));
    }

    public function create()
    {
        $academicYears = AcademicYear::where('is_active', true)->get();
        return view('admin.academic-terms.create', compact('academicYears'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'sort_order' => 'required|integer|min:0',
            'is_current' => 'boolean',
        ]);

        DB::transaction(function () use ($request) {
            if ($request->is_current) {
                AcademicTerm::where('is_current', true)->update(['is_current' => false]);
            }

            AcademicTerm::create($request->all());
        });

        return redirect()->route('admin.academic-terms.index')
                        ->with('success', 'Academic term created successfully.');
    }

    public function show(AcademicTerm $academicTerm)
    {
        $academicTerm->load('academicYear');
        return view('admin.academic-terms.show', compact('academicTerm'));
    }

    public function edit(AcademicTerm $academicTerm)
    {
        $academicYears = AcademicYear::where('is_active', true)->get();
        return view('admin.academic-terms.edit', compact('academicTerm', 'academicYears'));
    }

    public function update(Request $request, AcademicTerm $academicTerm)
    {
        $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'sort_order' => 'required|integer|min:0',
            'is_current' => 'boolean',
        ]);

        DB::transaction(function () use ($request, $academicTerm) {
            if ($request->is_current && !$academicTerm->is_current) {
                AcademicTerm::where('is_current', true)->update(['is_current' => false]);
            }

            $academicTerm->update($request->all());
        });

        return redirect()->route('admin.academic-terms.index')
                        ->with('success', 'Academic term updated successfully.');
    }

    public function destroy(AcademicTerm $academicTerm)
    {
        if ($academicTerm->is_current) {
            return redirect()->route('admin.academic-terms.index')
                           ->with('error', 'Cannot delete the current academic term.');
        }

        $academicTerm->delete();

        return redirect()->route('admin.academic-terms.index')
                        ->with('success', 'Academic term deleted successfully.');
    }

    public function setCurrent(AcademicTerm $academicTerm)
    {
        DB::transaction(function () use ($academicTerm) {
            AcademicTerm::where('is_current', true)->update(['is_current' => false]);
            $academicTerm->update(['is_current' => true]);
        });

        return redirect()->route('admin.academic-terms.index')
                        ->with('success', 'Academic term set as current successfully.');
    }
}
