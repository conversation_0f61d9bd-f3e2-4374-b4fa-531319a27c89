@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Invoices</h1>
                <p class="text-gray-600">Create and manage student invoices</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('teacher.dashboard') }}" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Dashboard
                </a>
                <button class="btn-primary">Create Invoice</button>
            </div>
        </div>
    </div>

    <!-- Coming Soon Notice -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-medium text-blue-800">Invoice Creation Coming Soon</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <p>As a teacher, you'll be able to:</p>
                    <ul class="mt-2 list-disc list-inside space-y-1">
                        <li>Create invoices for your students</li>
                        <li>Set payment due dates</li>
                        <li>Add detailed line items</li>
                        <li>Send invoices to parents automatically</li>
                        <li>Track payment status</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>


</div>
@endsection
