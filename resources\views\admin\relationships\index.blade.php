@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Relationships Overview"
        description="Review all student, guardian, and teacher relationships in the system"
        :back-route="route('admin.dashboard')"
        back-label="Back to Dashboard">
        <button class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export Report
        </button>
    </x-page-header>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Users</dt>
                            <dd class="stat-card-value">{{ $stats['total_users'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Students</dt>
                            <dd class="stat-card-value">{{ $stats['total_students'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Guardians</dt>
                            <dd class="stat-card-value">{{ $stats['total_guardians'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Teachers</dt>
                            <dd class="stat-card-value">{{ $stats['total_teachers'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Relationship Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <span class="text-white font-bold text-sm">{{ $stats['total_relationships'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Relationships</dt>
                            <dd class="stat-card-value">{{ $stats['total_relationships'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <span class="text-white font-bold text-sm">{{ $stats['primary_relationships'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Primary Guardians</dt>
                            <dd class="stat-card-value">{{ $stats['primary_relationships'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <span class="text-white font-bold text-sm">{{ $stats['secondary_relationships'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Secondary Guardians</dt>
                            <dd class="stat-card-value">{{ $stats['secondary_relationships'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-red-500">
                            <span class="text-white font-bold text-sm">{{ $stats['emergency_relationships'] }}</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Emergency Contacts</dt>
                            <dd class="stat-card-value">{{ $stats['emergency_relationships'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="bg-white shadow rounded-lg">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                            <button onclick="showTab('families')" id="families-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h6m-2-4h4m-4 4h4m-4-8h4m-4-4h.01M9 16h.01"></path>
                                </svg>
                                Family Groups
                            </button>
                            <button onclick="showTab('relationships')" id="relationships-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                </svg>
                                All Relationships
                            </button>
                            <button onclick="showTab('students')" id="students-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                </svg>
                                Students
                            </button>
                            <button onclick="showTab('guardians')" id="guardians-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                Guardians
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-6">
                        <!-- Family Groups Tab -->
                        <div id="families-content" class="tab-content">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-semibold text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h6m-2-4h4m-4 4h4m-4-8h4m-4-4h.01M9 16h.01"></path>
                                    </svg>
                                    Family Groups
                                </h3>
                                <span class="badge badge-blue">
                                    {{ count($families) }} Families
                                </span>
                            </div>
                            @if(count($families) > 0)
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    @foreach($families as $familyName => $family)
                                        <div class="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-200 hover:border-blue-200">
                                            <div class="flex items-center mb-4">
                                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h6m-2-4h4m-4 4h4m-4-8h4m-4-4h.01M9 16h.01"></path>
                                                    </svg>
                                                </div>
                                                <h4 class="font-bold text-gray-900 ml-3 text-lg">{{ $familyName }} Family</h4>
                                            </div>

                                            <div class="mb-4">
                                                <div class="flex items-center justify-between mb-2">
                                                    <h5 class="text-sm font-semibold text-gray-700">
                                                        Students
                                                    </h5>
                                                    <span class="badge badge-green text-xs">{{ count($family['students']) }}</span>
                                                </div>
                                                @foreach($family['students'] as $student)
                                                    <div class="flex items-center text-sm text-gray-600 mb-1 pl-0">
                                                        <span class="w-1.5 h-1.5 bg-green-400 rounded-full mr-2"></span>
                                                        {{ $student->user->name }}
                                                        <span class="ml-auto bg-gray-100 text-gray-700 text-xs px-2 py-0.5 rounded">{{ $student->class }}</span>
                                                    </div>
                                                @endforeach
                                            </div>

                                            <div>
                                                <div class="flex items-center justify-between mb-2">
                                                    <h5 class="text-sm font-semibold text-gray-700">
                                                        Guardians
                                                    </h5>
                                                    <span class="badge badge-purple text-xs">{{ count($family['guardians']) }}</span>
                                                </div>
                                                @foreach($family['guardians'] as $guardian)
                                                    <div class="flex items-center text-sm text-gray-600 mb-1 pl-0">
                                                        <span class="w-1.5 h-1.5 bg-purple-400 rounded-full mr-2"></span>
                                                        {{ $guardian->user->name }}
                                                        <span class="ml-auto bg-gray-100 text-gray-700 text-xs px-2 py-0.5 rounded">{{ $guardian->relationship }}</span>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-12">
                                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h6m-2-4h4m-4 4h4m-4-8h4m-4-4h.01M9 16h.01"></path>
                                    </svg>
                                    <p class="text-gray-500 text-lg">No family groups found.</p>
                                    <p class="text-gray-400 text-sm mt-1">Family groups will appear here once relationships are established.</p>
                                </div>
                            @endif
                        </div>

                        <!-- All Relationships Tab -->
                        <div id="relationships-content" class="tab-content hidden">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-semibold text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                    </svg>
                                    All Guardian-Student Relationships
                                </h3>
                                <span class="badge badge-blue">
                                    {{ count($relationships) }} Relationships
                                </span>
                            </div>
                            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Guardian</th>
                                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Student</th>
                                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Relationship</th>
                                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Type</th>
                                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Class</th>
                                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Contact</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            @foreach($relationships as $relationship)
                                                <tr class="hover:bg-gray-50 transition-colors duration-150">
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="flex items-center">
                                                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                                                <span class="text-purple-600 font-semibold text-sm">{{ substr($relationship->guardian_name, 0, 1) }}</span>
                                                            </div>
                                                            <div>
                                                                <div class="text-sm font-medium text-gray-900">{{ $relationship->guardian_name }}</div>
                                                                <div class="text-sm text-gray-500">{{ $relationship->guardian_email }}</div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="flex items-center">
                                                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                                                <span class="text-green-600 font-semibold text-sm">{{ substr($relationship->student_name, 0, 1) }}</span>
                                                            </div>
                                                            <div class="text-sm font-medium text-gray-900">{{ $relationship->student_name }}</div>
                                                        </div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <span class="text-sm font-medium text-gray-900">{{ $relationship->guardian_relationship }}</span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <span class="badge @if($relationship->relationship_type === 'Primary') badge-green @elseif($relationship->relationship_type === 'Secondary') badge-yellow @else badge-red @endif">
                                                            {{ $relationship->relationship_type }}
                                                        </span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <span class="bg-gray-100 text-gray-800 text-sm font-medium px-2 py-1 rounded">
                                                            {{ $relationship->class }} {{ $relationship->section }}
                                                        </span>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                                            </svg>
                                                            {{ $relationship->guardian_phone }}
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Students Tab -->
                        <div id="students-content" class="tab-content hidden">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-semibold text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                    </svg>
                                    Students and Their Guardians
                                </h3>
                                <span class="badge badge-green">
                                    {{ count($students) }} Students
                                </span>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                @foreach($students as $student)
                                    <div class="bg-gradient-to-br from-white to-green-50 rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-200 hover:border-green-200">
                                        <div class="flex items-center mb-4">
                                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                                <span class="text-green-600 font-bold text-lg">{{ substr($student->user->name, 0, 1) }}</span>
                                            </div>
                                            <div class="ml-3">
                                                <h4 class="font-bold text-gray-900 text-lg">{{ $student->user->name }}</h4>
                                                <p class="text-sm text-gray-600 flex items-center">
                                                    <span class="bg-gray-100 text-gray-700 px-2 py-0.5 rounded text-xs mr-2">{{ $student->student_id }}</span>
                                                    <span class="bg-green-100 text-green-700 px-2 py-0.5 rounded text-xs">{{ $student->class }}</span>
                                                </p>
                                            </div>
                                        </div>

                                        <div>
                                            <div class="flex items-center justify-between mb-3">
                                                <h5 class="text-sm font-semibold text-gray-700">
                                                    Guardians
                                                </h5>
                                                <span class="badge badge-purple text-xs">{{ $student->guardians->count() }}</span>
                                            </div>
                                            @if($student->guardians->count() > 0)
                                                @foreach($student->guardians as $guardian)
                                                    <div class="flex items-center text-sm text-gray-600 mb-1 pl-0">
                                                        <span class="w-1.5 h-1.5 bg-purple-400 rounded-full mr-2"></span>
                                                        {{ $guardian->user->name }}
                                                        <span class="ml-auto bg-gray-100 text-gray-700 text-xs px-2 py-0.5 rounded">{{ $guardian->relationship }}</span>
                                                    </div>
                                                @endforeach
                                            @else
                                                <div class="text-center py-4">
                                                    <svg class="w-8 h-8 text-gray-300 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                                    </svg>
                                                    <p class="text-sm text-gray-500">No guardians assigned</p>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Guardians Tab -->
                        <div id="guardians-content" class="tab-content hidden">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-semibold text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                    Guardians and Their Children
                                </h3>
                                <span class="badge badge-purple">
                                    {{ count($guardians) }} Guardians
                                </span>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                @foreach($guardians as $guardian)
                                    <div class="bg-gradient-to-br from-white to-purple-50 rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-200 hover:border-purple-200">
                                        <div class="flex items-center mb-4">
                                            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                                                <span class="text-purple-600 font-bold text-lg">{{ substr($guardian->user->name, 0, 1) }}</span>
                                            </div>
                                            <div class="ml-3">
                                                <h4 class="font-bold text-gray-900 text-lg">{{ $guardian->user->name }}</h4>
                                                <p class="text-sm text-gray-600 flex items-center">
                                                    <span class="bg-purple-100 text-purple-700 px-2 py-0.5 rounded text-xs mr-2">{{ $guardian->relationship }}</span>
                                                    @if($guardian->occupation)
                                                        <span class="bg-gray-100 text-gray-700 px-2 py-0.5 rounded text-xs">{{ $guardian->occupation }}</span>
                                                    @endif
                                                </p>
                                            </div>
                                        </div>

                                        <div class="mb-4">
                                            <div class="flex items-center justify-between mb-3">
                                                <h5 class="text-sm font-semibold text-gray-700">
                                                    Children
                                                </h5>
                                                <span class="badge badge-green text-xs">{{ $guardian->students->count() }}</span>
                                            </div>
                                            @if($guardian->students->count() > 0)
                                                @foreach($guardian->students as $student)
                                                    <div class="flex items-center text-sm text-gray-600 mb-1 pl-0">
                                                        <span class="w-1.5 h-1.5 bg-green-400 rounded-full mr-2"></span>
                                                        {{ $student->user->name }}
                                                        <span class="ml-auto bg-gray-100 text-gray-700 text-xs px-2 py-0.5 rounded">{{ $student->class }}</span>
                                                    </div>
                                                @endforeach
                                            @else
                                                <div class="text-center py-4">
                                                    <svg class="w-8 h-8 text-gray-300 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                                    </svg>
                                                    <p class="text-sm text-gray-500">No children assigned</p>
                                                </div>
                                            @endif
                                        </div>

                                        <div class="pt-4 border-t border-gray-200">
                                            <div class="space-y-2">
                                                @if($guardian->user->phone)
                                                    <div class="flex items-center text-xs text-gray-600">
                                                        <svg class="w-3 h-3 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                                        </svg>
                                                        {{ $guardian->user->phone }}
                                                    </div>
                                                @endif
                                                @if($guardian->user->email)
                                                    <div class="flex items-center text-xs text-gray-600">
                                                        <svg class="w-3 h-3 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                        </svg>
                                                        {{ $guardian->user->email }}
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>

</div>

<script>
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all tabs
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });

    // Show selected tab content
    document.getElementById(tabName + '-content').classList.remove('hidden');

    // Add active class to selected tab
    const activeTab = document.getElementById(tabName + '-tab');
    activeTab.classList.remove('border-transparent', 'text-gray-500');
    activeTab.classList.add('border-blue-500', 'text-blue-600');
}

// Initialize with families tab active
document.addEventListener('DOMContentLoaded', function() {
    showTab('families');
});
</script>
@endsection
