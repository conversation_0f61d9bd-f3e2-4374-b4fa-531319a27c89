<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExamInvigilator extends Model
{
    use HasFactory;

    protected $fillable = [
        'exam_subject_id',
        'teacher_id',
        'role',
        'hall_assignment',
        'notes',
    ];

    /**
     * Get the exam subject.
     */
    public function examSubject(): BelongsTo
    {
        return $this->belongsTo(ExamSubject::class);
    }

    /**
     * Get the teacher.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    /**
     * Check if this is the chief invigilator.
     */
    public function isChief(): bool
    {
        return $this->role === 'chief';
    }

    /**
     * Get invigilators for a specific exam subject.
     */
    public static function forExamSubject($examSubjectId)
    {
        return static::where('exam_subject_id', $examSubjectId)
                    ->with('teacher')
                    ->orderBy('role')
                    ->get();
    }
}
