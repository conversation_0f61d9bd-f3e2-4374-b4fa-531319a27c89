<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\GradeCategory;
use App\Models\Subject;
use App\Models\AcademicTerm;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class GradeCategoryController extends Controller
{
    /**
     * Display a listing of grade categories
     */
    public function index(Request $request)
    {
        $search = $request->get('search', '');
        $subjectId = $request->get('subject_id', '');
        $termId = $request->get('academic_term_id', '');
        $status = $request->get('status', '');

        $query = GradeCategory::with(['subject', 'academicTerm']);

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('subject', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Apply filters
        if ($subjectId) {
            $query->where('subject_id', $subjectId);
        }

        if ($termId) {
            $query->where('academic_term_id', $termId);
        }

        if ($status !== '') {
            $isActive = $status === 'active';
            $query->where('is_active', $isActive);
        }

        $categories = $query->latest()->paginate(25);

        // Get filter options
        $subjects = Subject::orderBy('name')->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();

        // Statistics
        $stats = [
            'total_categories' => GradeCategory::count(),
            'active_categories' => GradeCategory::active()->count(),
            'inactive_categories' => GradeCategory::where('is_active', false)->count(),
            'subjects_with_categories' => GradeCategory::distinct('subject_id')->count(),
        ];

        return view('admin.grade-categories.index', compact(
            'categories',
            'subjects',
            'academicTerms',
            'stats',
            'search',
            'subjectId',
            'termId',
            'status'
        ));
    }

    /**
     * Show the form for creating a new grade category
     */
    public function create()
    {
        $subjects = Subject::active()->orderBy('name')->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        
        return view('admin.grade-categories.create', compact('subjects', 'academicTerms'));
    }

    /**
     * Store a newly created grade category
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'weight' => 'required|numeric|min:0|max:100',
            'subject_id' => 'required|exists:subjects,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $category = GradeCategory::create([
                'name' => $request->name,
                'description' => $request->description,
                'weight' => $request->weight,
                'subject_id' => $request->subject_id,
                'academic_term_id' => $request->academic_term_id,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Load relationships for logging
            $category->load(['subject', 'academicTerm']);

            // Log activity
            ActivityLog::log(
                'created_grade_category',
                "Created grade category: {$category->name} for {$category->subject->name}",
                'App\Models\GradeCategory',
                $category->id,
                [
                    'category_name' => $category->name,
                    'subject_name' => $category->subject->name,
                    'term_name' => $category->academicTerm->name,
                    'weight' => $category->weight,
                ]
            );

            DB::commit();

            return redirect()->route('admin.grade-categories.index')
                ->with('success', 'Grade category created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Failed to create grade category. Please try again.');
        }
    }

    /**
     * Display the specified grade category
     */
    public function show(GradeCategory $gradeCategory)
    {
        $gradeCategory->load(['subject', 'academicTerm', 'grades.student.user']);

        $stats = [
            'total_grades' => $gradeCategory->grades()->count(),
            'published_grades' => $gradeCategory->grades()->where('is_published', true)->count(),
            'average_score' => $gradeCategory->grades()->where('is_published', true)->avg('percentage') ?? 0,
            'highest_score' => $gradeCategory->grades()->where('is_published', true)->max('percentage') ?? 0,
            'lowest_score' => $gradeCategory->grades()->where('is_published', true)->min('percentage') ?? 0,
        ];

        return view('admin.grade-categories.show', compact('gradeCategory', 'stats'));
    }

    /**
     * Show the form for editing the specified grade category
     */
    public function edit(GradeCategory $gradeCategory)
    {
        $subjects = Subject::active()->orderBy('name')->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        
        return view('admin.grade-categories.edit', compact('gradeCategory', 'subjects', 'academicTerms'));
    }

    /**
     * Update the specified grade category
     */
    public function update(Request $request, GradeCategory $gradeCategory)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'weight' => 'required|numeric|min:0|max:100',
            'subject_id' => 'required|exists:subjects,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            // Store old data for logging
            $oldData = $gradeCategory->toArray();
            $gradeCategory->load(['subject', 'academicTerm']);

            $gradeCategory->update([
                'name' => $request->name,
                'description' => $request->description,
                'weight' => $request->weight,
                'subject_id' => $request->subject_id,
                'academic_term_id' => $request->academic_term_id,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Refresh to get updated relationships
            $gradeCategory->refresh();
            $gradeCategory->load(['subject', 'academicTerm']);

            // Log activity
            ActivityLog::log(
                'updated_grade_category',
                "Updated grade category: {$gradeCategory->name} for {$gradeCategory->subject->name}",
                'App\Models\GradeCategory',
                $gradeCategory->id,
                [
                    'category_name' => $gradeCategory->name,
                    'subject_name' => $gradeCategory->subject->name,
                    'term_name' => $gradeCategory->academicTerm->name,
                    'old_weight' => $oldData['weight'],
                    'new_weight' => $gradeCategory->weight,
                    'old_active' => $oldData['is_active'],
                    'new_active' => $gradeCategory->is_active,
                ]
            );

            DB::commit();

            return redirect()->route('admin.grade-categories.index')
                ->with('success', 'Grade category updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Failed to update grade category. Please try again.');
        }
    }

    /**
     * Remove the specified grade category
     */
    public function destroy(GradeCategory $gradeCategory)
    {
        try {
            DB::beginTransaction();

            // Check if category has any grades
            if ($gradeCategory->grades()->exists()) {
                return back()->with('error', 'Cannot delete grade category with existing grades.');
            }

            // Store category info for logging before deletion
            $categoryInfo = [
                'category_name' => $gradeCategory->name,
                'subject_name' => $gradeCategory->subject->name,
                'term_name' => $gradeCategory->academicTerm->name,
                'weight' => $gradeCategory->weight,
            ];

            $gradeCategory->delete();

            // Log activity
            ActivityLog::log(
                'deleted_grade_category',
                "Deleted grade category: {$categoryInfo['category_name']} for {$categoryInfo['subject_name']}",
                'App\Models\GradeCategory',
                null,
                $categoryInfo
            );

            DB::commit();

            return redirect()->route('admin.grade-categories.index')
                ->with('success', 'Grade category deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to delete grade category. Please try again.');
        }
    }

    /**
     * Toggle grade category status
     */
    public function toggleStatus(GradeCategory $gradeCategory)
    {
        try {
            $oldStatus = $gradeCategory->is_active;
            $gradeCategory->update(['is_active' => !$gradeCategory->is_active]);

            $status = $gradeCategory->is_active ? 'activated' : 'deactivated';

            // Log activity
            ActivityLog::log(
                $status . '_grade_category',
                "Grade category {$status}: {$gradeCategory->name}",
                'App\Models\GradeCategory',
                $gradeCategory->id,
                [
                    'category_name' => $gradeCategory->name,
                    'old_status' => $oldStatus ? 'active' : 'inactive',
                    'new_status' => $gradeCategory->is_active ? 'active' : 'inactive',
                ]
            );

            return back()->with('success', "Grade category {$status} successfully.");

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update grade category status. Please try again.');
        }
    }
}
