<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Schedule;
use App\Models\AcademicYear;
use App\Models\AcademicTerm;
use App\Models\ClassModel;
use App\Models\Section;
use App\Models\TimeSlot;
use App\Models\SchoolEvent;
use App\Models\Announcement;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AcademicCalendarController extends Controller
{
    public function index(Request $request)
    {
        $currentYear = AcademicYear::current();
        $currentTerm = AcademicTerm::current();
        
        // Get filter options
        $academicYears = AcademicYear::where('is_active', true)->get();
        $academicTerms = AcademicTerm::where('is_active', true)->get();
        $classes = ClassModel::where('is_active', true)->get();
        $sections = Section::where('is_active', true)->get();

        // Get selected filters
        $selectedYear = $request->academic_year_id ?? $currentYear?->id;
        $selectedTerm = $request->academic_term_id ?? $currentTerm?->id;
        $selectedClass = $request->class_id;
        $selectedSection = $request->section_id;
        $selectedWeek = $request->week ?? now()->startOfWeek()->format('Y-m-d');

        // Calculate week dates
        $weekStart = Carbon::parse($selectedWeek)->startOfWeek();
        $weekEnd = $weekStart->copy()->endOfWeek();
        $weekDates = [];
        for ($i = 0; $i < 7; $i++) {
            $weekDates[] = $weekStart->copy()->addDays($i);
        }

        // Get time slots
        $timeSlots = TimeSlot::classSlots()->get();
        
        // Get schedules for the week
        $schedules = collect();
        if ($selectedTerm) {
            $query = Schedule::where('academic_term_id', $selectedTerm)
                           ->where('is_active', true)
                           ->with(['subject', 'teacher', 'timeSlot', 'class', 'section']);

            if ($selectedClass) {
                $query->where('class_id', $selectedClass);
            }

            if ($selectedSection) {
                $query->where('section_id', $selectedSection);
            }

            $schedules = $query->get()->groupBy(['day_of_week', 'time_slot_id']);
        }

        // Get events for the week
        $events = SchoolEvent::forDateRange($weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d'))
                            ->get()
                            ->groupBy(function ($event) {
                                return $event->event_date->format('Y-m-d');
                            });

        // Get recent announcements
        $announcements = Announcement::recent(5)->get();

        // Get upcoming events
        $upcomingEvents = SchoolEvent::upcoming(5)->get();

        // Calendar data for the month view
        $currentMonth = Carbon::parse($selectedWeek);
        $monthStart = $currentMonth->copy()->startOfMonth()->startOfWeek();
        $monthEnd = $currentMonth->copy()->endOfMonth()->endOfWeek();

        // Get schedules for the month view
        $monthSchedules = collect();
        if ($selectedTerm) {
            $query = Schedule::where('academic_term_id', $selectedTerm)
                           ->where('is_active', true)
                           ->with(['subject', 'teacher', 'timeSlot', 'class', 'section']);

            if ($selectedClass) {
                $query->where('class_id', $selectedClass);
            }

            if ($selectedSection) {
                $query->where('section_id', $selectedSection);
            }

            $monthSchedules = $query->get();
        }

        $calendarDays = [];
        $current = $monthStart->copy();
        while ($current <= $monthEnd) {
            // Get schedules for this day of week
            $daySchedules = $monthSchedules->filter(function ($schedule) use ($current) {
                return strtolower($schedule->day_of_week) === strtolower($current->format('l'));
            });

            $calendarDays[] = [
                'date' => $current->copy(),
                'isCurrentMonth' => $current->month === $currentMonth->month,
                'isToday' => $current->isToday(),
                'events' => $events->get($current->format('Y-m-d'), collect()),
                'schedules' => $daySchedules
            ];
            $current->addDay();
        }

        // Year view data
        $currentYear = now()->year;
        $yearEvents = [];
        if (request('view') === 'year') {
            $yearEvents = SchoolEvent::whereYear('event_date', $currentYear)
                                   ->get()
                                   ->groupBy(function ($event) {
                                       return $event->event_date->format('Y-m-d');
                                   });
        }

        return view('admin.academic-calendar.index', compact(
            'schedules', 'timeSlots', 'weekDates', 'academicYears', 'academicTerms',
            'classes', 'sections', 'selectedYear', 'selectedTerm', 'selectedClass',
            'selectedSection', 'selectedWeek', 'events', 'announcements',
            'upcomingEvents', 'calendarDays', 'currentMonth', 'currentYear', 'yearEvents'
        ));
    }

    public function getScheduleData(Request $request)
    {
        $classId = $request->class_id;
        $sectionId = $request->section_id;
        $termId = $request->academic_term_id;

        if (!$classId || !$sectionId || !$termId) {
            return response()->json(['schedules' => []]);
        }

        $schedules = Schedule::where('class_id', $classId)
                           ->where('section_id', $sectionId)
                           ->where('academic_term_id', $termId)
                           ->where('is_active', true)
                           ->with(['subject', 'teacher', 'timeSlot'])
                           ->get()
                           ->groupBy(['day_of_week', 'time_slot_id']);

        return response()->json(['schedules' => $schedules]);
    }

    public function teacherSchedule(Request $request)
    {
        $teacherId = $request->teacher_id ?? auth()->id();
        $selectedWeek = $request->week ?? now()->startOfWeek()->format('Y-m-d');
        $currentTerm = AcademicTerm::current();

        // Calculate week dates
        $weekStart = Carbon::parse($selectedWeek)->startOfWeek();
        $weekDates = [];
        for ($i = 0; $i < 7; $i++) {
            $weekDates[] = $weekStart->copy()->addDays($i);
        }

        // Get time slots
        $timeSlots = TimeSlot::classSlots()->get();
        
        // Get teacher's schedules
        $schedules = Schedule::forTeacher($teacherId, $currentTerm?->id)
                           ->get()
                           ->groupBy(['day_of_week', 'time_slot_id']);

        return view('admin.academic-calendar.teacher-schedule', compact(
            'schedules', 'timeSlots', 'weekDates', 'selectedWeek', 'teacherId'
        ));
    }
}
