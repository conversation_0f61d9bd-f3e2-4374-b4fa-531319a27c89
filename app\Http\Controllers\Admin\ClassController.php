<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ClassModel;
use App\Models\Section;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ClassController extends Controller
{
    /**
     * Display a listing of classes
     */
    public function index(Request $request)
    {
        $query = ClassModel::with(['sections', 'students', 'subjects']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('level', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $classes = $query->ordered()->paginate(20);

        // Get filter options
        $levels = ClassModel::distinct()->pluck('level')->filter()->sort();

        // Statistics
        $stats = [
            'total_classes' => ClassModel::count(),
            'active_classes' => ClassModel::active()->count(),
            'total_sections' => Section::count(),
            'total_students' => \App\Models\Student::count(),
        ];

        return view('admin.classes.index', compact('classes', 'levels', 'stats'));
    }

    /**
     * Show the form for creating a new class
     */
    public function create()
    {
        return view('admin.classes.create');
    }

    /**
     * Store a newly created class
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:classes',
            'level' => 'required|string|max:100',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $class = ClassModel::create([
                'name' => $request->name,
                'level' => $request->level,
                'description' => $request->description,
                'sort_order' => $request->sort_order,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::log(
                'created_class',
                "Created class: {$class->name}",
                'App\Models\ClassModel',
                $class->id
            );

            DB::commit();

            return redirect()->route('admin.academic.classes.index')
                           ->with('success', 'Class created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Error creating class: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified class
     */
    public function show(ClassModel $class)
    {
        $class->load(['sections.students', 'students', 'subjects', 'teachers']);
        
        return view('admin.classes.show', compact('class'));
    }

    /**
     * Show the form for editing the specified class
     */
    public function edit(ClassModel $class)
    {
        return view('admin.classes.edit', compact('class'));
    }

    /**
     * Update the specified class
     */
    public function update(Request $request, ClassModel $class)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:classes,name,' . $class->id,
            'level' => 'required|string|max:100',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $oldData = $class->toArray();

            $class->update([
                'name' => $request->name,
                'level' => $request->level,
                'description' => $request->description,
                'sort_order' => $request->sort_order,
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Log activity
            ActivityLog::log(
                'updated_class',
                "Updated class: {$class->name}",
                'App\Models\ClassModel',
                $class->id,
                ['old_data' => $oldData, 'new_data' => $class->fresh()->toArray()]
            );

            DB::commit();

            return redirect()->route('admin.academic.classes.index')
                           ->with('success', 'Class updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Error updating class: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified class
     */
    public function destroy(ClassModel $class)
    {
        try {
            DB::beginTransaction();

            // Check if class has students or sections
            if ($class->students()->count() > 0 || $class->sections()->count() > 0) {
                return back()->with('error', 'Cannot delete class that has students or sections.');
            }

            $className = $class->name;

            $class->delete();

            // Log activity
            ActivityLog::log(
                'deleted_class',
                "Deleted class: {$className}",
                'App\Models\ClassModel',
                null
            );

            DB::commit();

            return redirect()->route('admin.academic.classes.index')
                           ->with('success', 'Class deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Error deleting class: ' . $e->getMessage());
        }
    }

    /**
     * Toggle class status
     */
    public function toggleStatus(ClassModel $class)
    {
        try {
            $class->update(['is_active' => !$class->is_active]);

            $status = $class->is_active ? 'activated' : 'deactivated';
            
            // Log activity
            ActivityLog::log(
                $status . '_class',
                "Class {$status}: {$class->name}",
                'App\Models\ClassModel',
                $class->id
            );

            return back()->with('success', "Class {$status} successfully.");

        } catch (\Exception $e) {
            return back()->with('error', 'Error updating class status: ' . $e->getMessage());
        }
    }
}
