<?php

namespace App\Http\Controllers\Guardian;

use App\Http\Controllers\Controller;
use App\Models\Grade;
use App\Models\Subject;
use App\Models\AcademicTerm;
use App\Models\GradeCategory;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GradeController extends Controller
{
    /**
     * Display grades for guardian's children
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $guardian = $user->guardian;

        if (!$guardian) {
            abort(403, 'Guardian profile not found.');
        }

        $search = $request->get('search', '');
        $studentId = $request->get('student_id', '');
        $subjectId = $request->get('subject_id', '');
        $termId = $request->get('academic_term_id', '');
        $categoryId = $request->get('category_id', '');

        // Get guardian's students
        $guardianStudents = $guardian->students()->with('user')->get();
        $studentIds = $guardianStudents->pluck('id');

        if ($studentIds->isEmpty()) {
            return view('guardian.grades.index', [
                'grades' => collect(),
                'students' => collect(),
                'subjects' => collect(),
                'academicTerms' => collect(),
                'gradeCategories' => collect(),
                'stats' => [
                    'total_grades' => 0,
                    'average_percentage' => 0,
                    'highest_score' => 0,
                    'students_count' => 0,
                ],
                'search' => $search,
                'studentId' => $studentId,
                'subjectId' => $subjectId,
                'termId' => $termId,
                'categoryId' => $categoryId,
                'guardianStudents' => $guardianStudents
            ]);
        }

        // Build grades query for guardian's students only
        $query = Grade::whereIn('student_id', $studentIds)
            ->where('is_published', true) // Only show published grades
            ->with([
                'student.user',
                'subject',
                'teacher.user',
                'gradeCategory',
                'academicTerm'
            ]);

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('assignment_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('student.user', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('subject', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('gradeCategory', function ($cq) use ($search) {
                      $cq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Apply filters
        if ($studentId) {
            $query->where('student_id', $studentId);
        }

        if ($subjectId) {
            $query->where('subject_id', $subjectId);
        }

        if ($termId) {
            $query->where('academic_term_id', $termId);
        }

        if ($categoryId) {
            $query->where('grade_category_id', $categoryId);
        }

        $grades = $query->latest('assignment_date')->paginate(25);

        // Get filter options (only for subjects guardian's students are enrolled in)
        $subjects = Subject::whereHas('grades', function ($q) use ($studentIds) {
            $q->whereIn('student_id', $studentIds)->where('is_published', true);
        })->orderBy('name')->get();

        $academicTerms = AcademicTerm::whereHas('grades', function ($q) use ($studentIds) {
            $q->whereIn('student_id', $studentIds)->where('is_published', true);
        })->orderBy('name')->get();

        $gradeCategories = GradeCategory::whereHas('grades', function ($q) use ($studentIds) {
            $q->whereIn('student_id', $studentIds)->where('is_published', true);
        })->orderBy('name')->get();

        // Calculate statistics
        $stats = [
            'total_grades' => Grade::whereIn('student_id', $studentIds)->where('is_published', true)->count(),
            'average_percentage' => Grade::whereIn('student_id', $studentIds)->where('is_published', true)->avg('percentage') ?? 0,
            'highest_score' => Grade::whereIn('student_id', $studentIds)->where('is_published', true)->max('percentage') ?? 0,
            'students_count' => $guardianStudents->count(),
        ];

        return view('guardian.grades.index', compact(
            'grades',
            'subjects',
            'academicTerms',
            'gradeCategories',
            'stats',
            'search',
            'studentId',
            'subjectId',
            'termId',
            'categoryId',
            'guardianStudents'
        ));
    }

    /**
     * Display a specific grade
     */
    public function show(Grade $grade)
    {
        $user = Auth::user();
        $guardian = $user->guardian;

        if (!$guardian) {
            abort(403, 'Guardian profile not found.');
        }

        // Check if this grade belongs to one of guardian's students
        $guardianStudentIds = $guardian->students()->pluck('students.id');
        
        if (!$guardianStudentIds->contains($grade->student_id)) {
            abort(403, 'Access denied.');
        }

        if (!$grade->is_published) {
            abort(404, 'Grade not found.');
        }

        $grade->load([
            'student.user',
            'subject',
            'teacher.user',
            'gradeCategory',
            'academicTerm'
        ]);

        return view('guardian.grades.show', compact('grade', 'guardian'));
    }

    /**
     * Get grades summary for each child
     */
    public function getChildrenGradesSummary()
    {
        $user = Auth::user();
        $guardian = $user->guardian;

        if (!$guardian) {
            return response()->json(['error' => 'Guardian profile not found'], 403);
        }

        $children = $guardian->students()->with('user')->get();
        
        $childrenSummary = $children->map(function ($student) {
            $grades = Grade::where('student_id', $student->id)
                ->where('is_published', true)
                ->with(['subject', 'gradeCategory'])
                ->get();

            $totalGpaPoints = $grades->sum('gpa_points');
            $gradeCount = $grades->count();
            $avgPercentage = $grades->avg('percentage') ?? 0;

            $subjectSummary = $grades->groupBy('subject.name')->map(function ($subjectGrades) {
                $subjectAvg = $subjectGrades->avg('percentage');
                $subjectGpa = $subjectGrades->avg('gpa_points');
                
                return [
                    'subject_name' => $subjectGrades->first()->subject->name,
                    'grade_count' => $subjectGrades->count(),
                    'average_percentage' => round($subjectAvg, 2),
                    'gpa' => round($subjectGpa, 2),
                    'latest_grade' => $subjectGrades->sortByDesc('assignment_date')->first(),
                ];
            });

            return [
                'student_id' => $student->id,
                'student_name' => $student->user->name,
                'student_id_number' => $student->student_id,
                'total_grades' => $gradeCount,
                'average_percentage' => round($avgPercentage, 2),
                'gpa' => $gradeCount > 0 ? round($totalGpaPoints / $gradeCount, 2) : 0,
                'subjects' => $subjectSummary,
            ];
        });

        return response()->json($childrenSummary);
    }

    /**
     * Export grades for a specific child
     */
    public function exportChild(Student $student, $format = 'csv')
    {
        $user = Auth::user();
        $guardian = $user->guardian;

        if (!$guardian) {
            abort(403, 'Guardian profile not found.');
        }

        // Check if this student belongs to the guardian
        $guardianStudentIds = $guardian->students()->pluck('students.id');
        
        if (!$guardianStudentIds->contains($student->id)) {
            abort(403, 'Access denied.');
        }

        $grades = Grade::where('student_id', $student->id)
            ->where('is_published', true)
            ->with([
                'subject',
                'teacher.user',
                'gradeCategory',
                'academicTerm'
            ])
            ->latest('assignment_date')
            ->get();

        $filename = $student->user->name . '_grades_' . now()->format('Y_m_d_H_i_s') . '.' . $format;

        if ($format === 'csv') {
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];

            $callback = function() use ($grades, $student) {
                $file = fopen('php://output', 'w');

                // CSV headers
                fputcsv($file, [
                    'Student Name',
                    'Student ID',
                    'Subject',
                    'Assignment Name',
                    'Score',
                    'Max Score',
                    'Percentage',
                    'Letter Grade',
                    'Category',
                    'Term',
                    'Teacher',
                    'Assignment Date',
                    'Comments'
                ]);

                // CSV data
                foreach ($grades as $grade) {
                    fputcsv($file, [
                        $student->user->name,
                        $student->student_id,
                        $grade->subject->name ?? '',
                        $grade->assignment_name,
                        $grade->score,
                        $grade->max_score,
                        round($grade->percentage, 2),
                        $grade->letter_grade,
                        $grade->gradeCategory->name ?? '',
                        $grade->academicTerm->name ?? '',
                        $grade->teacher->user->name ?? '',
                        $grade->assignment_date,
                        $grade->comments ?? ''
                    ]);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        }

        return back()->with('error', 'Export format not supported.');
    }
}
