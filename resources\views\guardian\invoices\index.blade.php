@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Invoices & Bills"
        description="View and pay your children's school fees"
        :back-route="route('guardian.dashboard')"
        back-label="Back to Dashboard">
    </x-page-header>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6 mb-6" x-data="{
        searchQuery: '',
        status: '',
        studentId: '',
        showAdvanced: false
    }">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            <!-- Search Input -->
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input
                        type="text"
                        x-model="searchQuery"
                        @input="filterGuardianInvoices()"
                        class="search-input"
                        placeholder="Search invoices by number, title, student name..."
                    >
                </div>
            </div>

            <!-- Filter Toggle and Clear Buttons -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                    <svg class="w-4 h-4 ml-1 transition-transform duration-200" :class="showAdvanced ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <!-- Clear All Filters -->
                <button
                    @click="clearGuardianInvoiceFilters()"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 transform translate-y-0" x-transition:leave-end="opacity-0 transform -translate-y-2" class="mt-4 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Status Filter -->
                <div>
                    <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                        id="status-filter"
                        x-model="status"
                        @change="filterGuardianInvoices()"
                        class="form-select"
                    >
                        <option value="">All Statuses</option>
                        <option value="sent">Pending</option>
                        <option value="paid">Paid</option>
                        <option value="overdue">Overdue</option>
                    </select>
                </div>

                <!-- Student Filter -->
                <div>
                    <label for="student-filter" class="block text-sm font-medium text-gray-700 mb-1">Child</label>
                    <select
                        id="student-filter"
                        x-model="studentId"
                        @change="filterGuardianInvoices()"
                        class="form-select"
                    >
                        <option value="">All Children</option>
                        @foreach($students as $student)
                            <option value="{{ $student->id }}">{{ $student->user->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid-stats-3">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Invoices</dt>
                            <dd class="stat-card-value">{{ $stats['total'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Pending</dt>
                            <dd class="stat-card-value">{{ $stats['pending'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-red-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Amount Due</dt>
                            <dd class="stat-card-value">RM {{ number_format($stats['total_amount_due'], 2) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoices Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Your Children's Invoices</h3>
        </div>

        @if($invoices->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($invoices as $invoice)
                            <tr class="hover:bg-gray-50"
                                data-filterable
                                data-search-text="{{ $invoice->invoice_number }} {{ $invoice->title }} {{ $invoice->description ?? '' }} {{ $invoice->student->user->name ?? '' }} {{ $invoice->student->student_id ?? '' }} {{ $invoice->student->class ?? '' }} {{ $invoice->student->section ?? '' }} {{ $invoice->total_amount }} {{ $invoice->status }} {{ $invoice->due_date ? $invoice->due_date->format('M d, Y') : '' }} {{ $invoice->created_at->format('M d, Y') }}"
                                data-status="{{ $invoice->status }}"
                                data-student-id="{{ $invoice->student_id }}"
                                data-date="{{ $invoice->created_at->format('Y-m-d') }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $invoice->invoice_number }}</div>
                                        <div class="text-sm text-gray-500">{{ $invoice->title }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8">
                                            <div class="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                <span class="text-blue-600 font-medium text-xs">
                                                    {{ substr($invoice->student->user->name ?? 'N/A', 0, 2) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">{{ $invoice->student->user->name ?? 'N/A' }}</div>
                                            <div class="text-sm text-gray-500">{{ $invoice->student->student_id ?? 'N/A' }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">RM {{ number_format($invoice->total_amount, 2) }}</div>
                                    @php
                                        $totalPaid = $invoice->payments->where('status', 'paid')->sum('amount');
                                        $remainingAmount = $invoice->total_amount - $totalPaid;
                                    @endphp
                                    @if($totalPaid > 0)
                                        <div class="text-sm text-gray-500">
                                            Paid: RM {{ number_format($totalPaid, 2) }}
                                            @if($remainingAmount > 0)
                                                <br>Remaining: RM {{ number_format($remainingAmount, 2) }}
                                            @endif
                                        </div>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $invoice->due_date->format('M d, Y') }}
                                    @if($invoice->due_date->isPast() && $invoice->status !== 'paid')
                                        <span class="text-red-500 text-xs block">(Overdue)</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @php
                                        $statusClasses = [
                                            'sent' => 'badge-yellow',
                                            'paid' => 'badge-green',
                                            'overdue' => 'badge-red',
                                        ];
                                    @endphp
                                    <span class="badge {{ $statusClasses[$invoice->status] ?? 'badge-gray' }}">
                                        {{ $invoice->status === 'sent' ? 'Pending' : ucfirst($invoice->status) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <a href="{{ route('guardian.invoices.show', $invoice) }}"
                                           class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition-colors duration-200"
                                           title="View Invoice">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            View
                                        </a>

                                        @if(in_array($invoice->status, ['sent', 'overdue']) && $remainingAmount > 0)
                                            <button onclick="quickPayment('{{ $invoice->id }}', '{{ number_format($remainingAmount, 2) }}', '{{ $invoice->invoice_number }}')"
                                                    class="inline-flex items-center px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition-colors duration-200"
                                                    title="Pay Now">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                                </svg>
                                                Pay Now
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $invoices->links() }}
            </div>
        @else
            <div class="px-6 py-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No invoices found</h3>
                <p class="mt-1 text-sm text-gray-500">There are no invoices for your children at this time.</p>
                <div class="mt-6">
                    <a href="{{ route('guardian.dashboard') }}" class="btn-secondary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        @endif
    </div>

</div>

@push('scripts')
<script>
// Store the base route URL for JavaScript use
const paymentRouteBase = '{{ route('guardian.invoices.index') }}';

async function quickPayment(invoiceId, amount, invoiceNumber) {
    const confirmed = await confirmPayment(`RM ${amount} for Invoice #${invoiceNumber}`);

    if (confirmed) {
        // Create a form and submit it to the payment route
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `${paymentRouteBase}/${invoiceId}/pay`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // Add amount
        const amountInput = document.createElement('input');
        amountInput.type = 'hidden';
        amountInput.name = 'amount';
        amountInput.value = amount.replace(',', ''); // Remove comma formatting
        form.appendChild(amountInput);

        // Submit form
        document.body.appendChild(form);
        form.submit();
    }
}

// Live filtering functions for guardian invoices
function filterGuardianInvoices() {
    const searchQuery = document.querySelector('[x-model="searchQuery"]').value.toLowerCase();
    const status = document.querySelector('[x-model="status"]')?.value;
    const studentId = document.querySelector('[x-model="studentId"]')?.value;

    // Get all filterable items
    const items = document.querySelectorAll('[data-filterable]');

    items.forEach(item => {
        let showItem = true;

        // Text search
        if (searchQuery) {
            const searchableText = item.getAttribute('data-search-text') || item.textContent;
            if (!searchableText.toLowerCase().includes(searchQuery)) {
                showItem = false;
            }
        }

        // Status filtering
        if (status) {
            const itemStatus = item.getAttribute('data-status');
            if (itemStatus !== status) {
                showItem = false;
            }
        }

        // Student filtering
        if (studentId) {
            const itemStudentId = item.getAttribute('data-student-id');
            if (itemStudentId !== studentId) {
                showItem = false;
            }
        }

        // Show/hide item
        item.style.display = showItem ? '' : 'none';
    });
}

function clearGuardianInvoiceFilters() {
    // Reset form inputs and trigger events
    const searchInput = document.querySelector('[x-model="searchQuery"]');
    const statusInput = document.querySelector('[x-model="status"]');
    const studentInput = document.querySelector('[x-model="studentId"]');

    if (searchInput) {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    }
    if (statusInput) {
        statusInput.value = '';
        statusInput.dispatchEvent(new Event('change'));
    }
    if (studentInput) {
        studentInput.value = '';
        studentInput.dispatchEvent(new Event('change'));
    }

    // Show all items
    const items = document.querySelectorAll('[data-filterable]');
    items.forEach(item => {
        item.style.display = '';
    });
}
</script>
@endpush
@endsection
