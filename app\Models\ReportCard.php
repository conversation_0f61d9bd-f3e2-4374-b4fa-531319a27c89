<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ReportCard extends Model
{
    use HasFactory;

    protected $fillable = [
        'report_number',
        'student_id',
        'academic_term_id',
        'template_id',
        'grade_data',
        'attendance_data',
        'overall_gpa',
        'overall_grade',
        'class_rank',
        'total_students',
        'teacher_remarks',
        'principal_remarks',
        'status',
        'generated_at',
        'published_at',
        'generated_by',
    ];

    protected $casts = [
        'grade_data' => 'array',
        'attendance_data' => 'array',
        'overall_gpa' => 'decimal:2',
        'generated_at' => 'datetime',
        'published_at' => 'datetime',
    ];

    /**
     * Get the student.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the academic term.
     */
    public function academicTerm(): BelongsTo
    {
        return $this->belongsTo(AcademicTerm::class);
    }

    /**
     * Get the template.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(ReportCardTemplate::class, 'template_id');
    }

    /**
     * Get the generator.
     */
    public function generator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    /**
     * Get the subject grades.
     */
    public function grades(): HasMany
    {
        return $this->hasMany(ReportCardGrade::class);
    }

    /**
     * Generate report number.
     */
    public static function generateReportNumber($studentId, $termId)
    {
        $term = AcademicTerm::find($termId);
        $student = Student::find($studentId);
        
        return 'RC-' . $term->academicYear->name . '-' . $term->name . '-' . $student->student_id;
    }

    /**
     * Check if report card can be edited.
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, ['draft', 'generated']);
    }

    /**
     * Check if report card is published.
     */
    public function isPublished(): bool
    {
        return in_array($this->status, ['published', 'distributed']);
    }

    /**
     * Get attendance percentage.
     */
    public function getAttendancePercentageAttribute(): float
    {
        $attendanceData = $this->attendance_data;
        
        if (!$attendanceData || !isset($attendanceData['total_days']) || $attendanceData['total_days'] == 0) {
            return 0;
        }

        return round(($attendanceData['present_days'] / $attendanceData['total_days']) * 100, 2);
    }

    /**
     * Get class position text.
     */
    public function getClassPositionAttribute(): string
    {
        if (!$this->class_rank || !$this->total_students) {
            return 'N/A';
        }

        return $this->class_rank . ' out of ' . $this->total_students;
    }

    /**
     * Generate report card data.
     */
    public static function generateForStudent($studentId, $termId, $templateId = null)
    {
        $student = Student::find($studentId);
        $term = AcademicTerm::find($termId);
        
        if (!$templateId) {
            $template = ReportCardTemplate::where('is_default', true)->first();
            $templateId = $template?->id;
        }

        // Compile grade data from gradebook
        $grades = Grade::where('student_id', $studentId)
                      ->where('academic_term_id', $termId)
                      ->where('is_published', true)
                      ->with('subject')
                      ->get()
                      ->groupBy('subject_id');

        $gradeData = [];
        $totalGpaPoints = 0;
        $totalSubjects = 0;

        foreach ($grades as $subjectId => $subjectGrades) {
            $subject = $subjectGrades->first()->subject;
            $totalMarks = $subjectGrades->sum('score');
            $maxMarks = $subjectGrades->sum('max_score');
            $percentage = $maxMarks > 0 ? ($totalMarks / $maxMarks) * 100 : 0;
            
            $grade = static::calculateGrade($percentage);
            $gpaPoints = static::calculateGpaPoints($percentage);

            $gradeData[$subjectId] = [
                'subject_name' => $subject->name,
                'total_marks' => $totalMarks,
                'max_marks' => $maxMarks,
                'percentage' => round($percentage, 2),
                'grade' => $grade,
                'gpa_points' => $gpaPoints,
            ];

            $totalGpaPoints += $gpaPoints;
            $totalSubjects++;
        }

        $overallGpa = $totalSubjects > 0 ? $totalGpaPoints / $totalSubjects : 0;
        $overallGrade = static::calculateGrade($overallGpa * 25); // Convert GPA to percentage for grade calculation

        // Compile attendance data
        $attendanceData = static::compileAttendanceData($studentId, $termId);

        return [
            'grade_data' => $gradeData,
            'attendance_data' => $attendanceData,
            'overall_gpa' => round($overallGpa, 2),
            'overall_grade' => $overallGrade,
        ];
    }

    /**
     * Calculate grade from percentage.
     */
    private static function calculateGrade($percentage): string
    {
        if ($percentage >= 90) return 'A+';
        if ($percentage >= 80) return 'A';
        if ($percentage >= 70) return 'B+';
        if ($percentage >= 60) return 'B';
        if ($percentage >= 50) return 'C+';
        if ($percentage >= 40) return 'C';
        if ($percentage >= 30) return 'D';
        return 'F';
    }

    /**
     * Calculate GPA points from percentage.
     */
    private static function calculateGpaPoints($percentage): float
    {
        if ($percentage >= 90) return 4.0;
        if ($percentage >= 80) return 3.7;
        if ($percentage >= 70) return 3.3;
        if ($percentage >= 60) return 3.0;
        if ($percentage >= 50) return 2.7;
        if ($percentage >= 40) return 2.3;
        if ($percentage >= 30) return 2.0;
        return 0.0;
    }

    /**
     * Compile attendance data for the term.
     */
    private static function compileAttendanceData($studentId, $termId)
    {
        $term = AcademicTerm::find($termId);
        
        $attendance = StudentAttendance::where('student_id', $studentId)
                                     ->whereBetween('date', [$term->start_date, $term->end_date])
                                     ->get();

        $totalDays = $attendance->count();
        $presentDays = $attendance->where('status', 'present')->count();
        $absentDays = $attendance->where('status', 'absent')->count();

        return [
            'total_days' => $totalDays,
            'present_days' => $presentDays,
            'absent_days' => $absentDays,
            'percentage' => $totalDays > 0 ? round(($presentDays / $totalDays) * 100, 2) : 0,
        ];
    }
}
