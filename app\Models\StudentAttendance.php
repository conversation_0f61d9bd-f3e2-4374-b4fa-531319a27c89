<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class StudentAttendance extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'marked_by',
        'date',
        'status',
        'subcategory',
        'notes',
        'class',
        'section',
    ];

    protected $casts = [
        'date' => 'date',
    ];

    /**
     * Get the student that owns the attendance record
     */
    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the user who marked the attendance
     */
    public function markedBy()
    {
        return $this->belongsTo(User::class, 'marked_by');
    }

    /**
     * Scope to filter by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by status
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by class
     */
    public function scopeClass($query, $class)
    {
        return $query->where('class', $class);
    }

    /**
     * Scope to filter by section
     */
    public function scopeSection($query, $section)
    {
        return $query->where('section', $section);
    }

    /**
     * Get attendance status badge color
     */
    public function getStatusBadgeColorAttribute()
    {
        return match($this->status) {
            'present' => 'green',
            'absent' => 'red',
            default => 'gray'
        };
    }

    /**
     * Get attendance status display text
     */
    public function getStatusDisplayAttribute()
    {
        if ($this->subcategory) {
            return $this->subcategory;
        }

        return match($this->status) {
            'present' => 'Present',
            'absent' => 'Absent',
            default => ucfirst($this->status)
        };
    }

    /**
     * Get available subcategories for students
     */
    public static function getSubcategories()
    {
        return [
            'present' => [],
            'absent' => [
                'Medical Leave',
                'Family Matters',
                'Other'
            ]
        ];
    }

    /**
     * Get full status display with subcategory
     */
    public function getFullStatusDisplayAttribute()
    {
        $status = ucfirst($this->status);
        if ($this->subcategory) {
            $status .= ' (' . $this->subcategory . ')';
        }
        return $status;
    }

    /**
     * Check if attendance is marked for today
     */
    public static function isMarkedToday($studentId)
    {
        return self::where('student_id', $studentId)
                   ->where('date', Carbon::today())
                   ->exists();
    }

    /**
     * Get attendance statistics for a student
     */
    public static function getStudentStats($studentId, $startDate = null, $endDate = null)
    {
        $query = self::where('student_id', $studentId);

        if ($startDate && $endDate) {
            $query->whereBetween('date', [$startDate, $endDate]);
        }

        $attendances = $query->get();
        $total = $attendances->count();

        if ($total === 0) {
            return [
                'total_days' => 0,
                'present' => 0,
                'absent' => 0,
                'late' => 0,
                'excused' => 0,
                'sick' => 0,
                'attendance_percentage' => 0
            ];
        }

        $stats = [
            'total_days' => $total,
            'present' => $attendances->where('status', 'present')->count(),
            'absent' => $attendances->where('status', 'absent')->count(),
            'late' => $attendances->where('status', 'late')->count(),
            'excused' => $attendances->where('status', 'excused')->count(),
            'sick' => $attendances->where('status', 'sick')->count(),
        ];

        // Calculate attendance percentage (only present counts as attended)
        $stats['attendance_percentage'] = $total > 0 ? round(($stats['present'] / $total) * 100, 2) : 0;

        return $stats;
    }

    /**
     * Get class attendance statistics for a specific date
     */
    public static function getClassStats($class, $section = null, $date = null)
    {
        $query = self::where('class', $class);
        
        if ($section) {
            $query->where('section', $section);
        }
        
        if ($date) {
            $query->where('date', $date);
        } else {
            $query->where('date', Carbon::today());
        }

        $attendances = $query->get();
        $total = $attendances->count();

        if ($total === 0) {
            return [
                'total_students' => 0,
                'present' => 0,
                'absent' => 0,
                'late' => 0,
                'excused' => 0,
                'sick' => 0,
                'attendance_percentage' => 0
            ];
        }

        $stats = [
            'total_students' => $total,
            'present' => $attendances->where('status', 'present')->count(),
            'absent' => $attendances->where('status', 'absent')->count(),
            'late' => $attendances->where('status', 'late')->count(),
            'excused' => $attendances->where('status', 'excused')->count(),
            'sick' => $attendances->where('status', 'sick')->count(),
        ];

        $attended = $stats['present'] + $stats['late'] + $stats['excused'];
        $stats['attendance_percentage'] = $total > 0 ? round(($attended / $total) * 100, 2) : 0;

        return $stats;
    }

    /**
     * Get overall student attendance statistics
     */
    public static function getOverallStats($startDate = null, $endDate = null)
    {
        $query = self::query();

        if ($startDate && $endDate) {
            $query->whereBetween('date', [$startDate, $endDate]);
        }

        $attendances = $query->get();
        $total = $attendances->count();

        if ($total === 0) {
            return [
                'total_records' => 0,
                'present' => 0,
                'absent' => 0,
                'late' => 0,
                'excused' => 0,
                'sick' => 0,
                'attendance_percentage' => 0
            ];
        }

        $stats = [
            'total_records' => $total,
            'present' => $attendances->where('status', 'present')->count(),
            'absent' => $attendances->where('status', 'absent')->count(),
            'late' => $attendances->where('status', 'late')->count(),
            'excused' => $attendances->where('status', 'excused')->count(),
            'sick' => $attendances->where('status', 'sick')->count(),
        ];

        $attended = $stats['present'] + $stats['late'] + $stats['excused'];
        $stats['attendance_percentage'] = $total > 0 ? round(($attended / $total) * 100, 2) : 0;

        return $stats;
    }
}
