<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ExamResult;
use App\Models\ExamSubject;
use App\Models\Student;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ExamResultController extends Controller
{
    public function index(Request $request)
    {
        $examSubjectId = $request->get('exam_subject_id');
        $studentId = $request->get('student_id');
        $status = $request->get('status');

        $query = ExamResult::with(['examSubject.exam', 'examSubject.subject', 'student.user']);

        if ($examSubjectId) {
            $query->where('exam_subject_id', $examSubjectId);
        }

        if ($studentId) {
            $query->where('student_id', $studentId);
        }

        if ($status) {
            $query->where('status', $status);
        }

        $examResults = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get filter options
        $examSubjects = ExamSubject::with(['exam', 'subject'])->get();
        $students = Student::with('user')->get();

        // Get statistics
        $stats = [
            'total' => ExamResult::count(),
            'present' => ExamResult::where('status', 'present')->count(),
            'absent' => ExamResult::where('status', 'absent')->count(),
            'average_marks' => ExamResult::where('status', 'present')->avg('marks_obtained') ?? 0,
        ];

        return view('admin.exam-results.index', compact(
            'examResults', 'examSubjects', 'students', 'stats',
            'examSubjectId', 'studentId', 'status'
        ));
    }

    public function create()
    {
        $examSubjects = ExamSubject::with(['exam', 'subject'])->get();
        $students = Student::with('user')->get();
        
        return view('admin.exam-results.create', compact('examSubjects', 'students'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'exam_subject_id' => 'required|exists:exam_subjects,id',
            'student_id' => 'required|exists:students,id',
            'marks_obtained' => 'nullable|numeric|min:0',
            'status' => 'required|in:present,absent',
            'remarks' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $examSubject = ExamSubject::findOrFail($request->exam_subject_id);

            // Validate marks against max marks
            if ($request->status === 'present' && $request->marks_obtained > $examSubject->max_marks) {
                return back()->withInput()
                            ->with('error', "Marks cannot exceed maximum marks ({$examSubject->max_marks}).");
            }

            // Check for duplicate result
            $existing = ExamResult::where('exam_subject_id', $request->exam_subject_id)
                                 ->where('student_id', $request->student_id)
                                 ->first();

            if ($existing) {
                return back()->withInput()
                            ->with('error', 'Result already exists for this student and exam subject.');
            }

            $marksObtained = $request->status === 'present' ? $request->marks_obtained : null;
            $grade = null;
            $isPassed = false;

            if ($request->status === 'present' && $marksObtained !== null) {
                $grade = $this->calculateGrade($marksObtained, $examSubject->max_marks);
                $isPassed = $marksObtained >= $examSubject->pass_marks;
            }

            $examResult = ExamResult::create([
                'exam_subject_id' => $request->exam_subject_id,
                'student_id' => $request->student_id,
                'marks_obtained' => $marksObtained,
                'grade' => $grade,
                'is_passed' => $isPassed,
                'status' => $request->status,
                'remarks' => $request->remarks,
                'recorded_by' => auth()->id(),
                'recorded_at' => now(),
            ]);

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'created',
                'description' => "Recorded exam result for {$examResult->student->user->name} in {$examResult->examSubject->subject->name}",
                'model_type' => ExamResult::class,
                'model_id' => $examResult->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.exam-results.show', $examResult)
                            ->with('success', 'Exam result recorded successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to record exam result: ' . $e->getMessage());
        }
    }

    public function show(ExamResult $examResult)
    {
        $examResult->load([
            'examSubject.exam.examType',
            'examSubject.subject',
            'student.user',
            'recordedBy'
        ]);

        return view('admin.exam-results.show', compact('examResult'));
    }

    public function edit(ExamResult $examResult)
    {
        if ($examResult->examSubject->exam->status === 'completed') {
            return redirect()->route('admin.exam-results.show', $examResult)
                            ->with('error', 'Cannot edit result for completed exam.');
        }

        $examSubjects = ExamSubject::with(['exam', 'subject'])->get();
        $students = Student::with('user')->get();
        
        return view('admin.exam-results.edit', compact('examResult', 'examSubjects', 'students'));
    }

    public function update(Request $request, ExamResult $examResult)
    {
        if ($examResult->examSubject->exam->status === 'completed') {
            return redirect()->route('admin.exam-results.show', $examResult)
                            ->with('error', 'Cannot edit result for completed exam.');
        }

        $request->validate([
            'marks_obtained' => 'nullable|numeric|min:0',
            'status' => 'required|in:present,absent',
            'remarks' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $examSubject = $examResult->examSubject;

            // Validate marks against max marks
            if ($request->status === 'present' && $request->marks_obtained > $examSubject->max_marks) {
                return back()->withInput()
                            ->with('error', "Marks cannot exceed maximum marks ({$examSubject->max_marks}).");
            }

            $marksObtained = $request->status === 'present' ? $request->marks_obtained : null;
            $grade = null;
            $isPassed = false;

            if ($request->status === 'present' && $marksObtained !== null) {
                $grade = $this->calculateGrade($marksObtained, $examSubject->max_marks);
                $isPassed = $marksObtained >= $examSubject->pass_marks;
            }

            $examResult->update([
                'marks_obtained' => $marksObtained,
                'grade' => $grade,
                'is_passed' => $isPassed,
                'status' => $request->status,
                'remarks' => $request->remarks,
                'updated_by' => auth()->id(),
                'updated_at' => now(),
            ]);

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'updated',
                'description' => "Updated exam result for {$examResult->student->user->name} in {$examResult->examSubject->subject->name}",
                'model_type' => ExamResult::class,
                'model_id' => $examResult->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.exam-results.show', $examResult)
                            ->with('success', 'Exam result updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to update exam result: ' . $e->getMessage());
        }
    }

    public function destroy(ExamResult $examResult)
    {
        if ($examResult->examSubject->exam->status === 'completed') {
            return redirect()->route('admin.exam-results.index')
                            ->with('error', 'Cannot delete result for completed exam.');
        }

        try {
            DB::beginTransaction();

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'deleted',
                'description' => "Deleted exam result for {$examResult->student->user->name} in {$examResult->examSubject->subject->name}",
                'model_type' => ExamResult::class,
                'model_id' => $examResult->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            $examResult->delete();

            DB::commit();

            return redirect()->route('admin.exam-results.index')
                            ->with('success', 'Exam result deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('admin.exam-results.index')
                            ->with('error', 'Failed to delete exam result: ' . $e->getMessage());
        }
    }

    public function bulkEntry(Request $request)
    {
        $examSubjectId = $request->get('exam_subject_id');
        
        if (!$examSubjectId) {
            $examSubjects = ExamSubject::with(['exam', 'subject'])->get();
            return view('admin.exam-results.bulk-entry', compact('examSubjects'));
        }

        $examSubject = ExamSubject::with(['exam', 'subject'])->findOrFail($examSubjectId);
        
        // Get enrolled students for this exam
        $students = Student::with('user')
                          ->whereHas('enrollments', function ($query) use ($examSubject) {
                              $query->where('exam_id', $examSubject->exam_id);
                          })
                          ->get();

        // Get existing results
        $existingResults = ExamResult::where('exam_subject_id', $examSubjectId)
                                   ->pluck('student_id')
                                   ->toArray();

        return view('admin.exam-results.bulk-entry', compact('examSubject', 'students', 'existingResults'));
    }

    public function storeBulk(Request $request)
    {
        $request->validate([
            'exam_subject_id' => 'required|exists:exam_subjects,id',
            'results' => 'required|array',
            'results.*.student_id' => 'required|exists:students,id',
            'results.*.marks_obtained' => 'nullable|numeric|min:0',
            'results.*.status' => 'required|in:present,absent',
            'results.*.remarks' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $examSubject = ExamSubject::findOrFail($request->exam_subject_id);
            $created = 0;
            $updated = 0;

            foreach ($request->results as $resultData) {
                // Validate marks against max marks
                if ($resultData['status'] === 'present' && 
                    isset($resultData['marks_obtained']) && 
                    $resultData['marks_obtained'] > $examSubject->max_marks) {
                    continue; // Skip invalid entries
                }

                $marksObtained = $resultData['status'] === 'present' ? $resultData['marks_obtained'] : null;
                $grade = null;
                $isPassed = false;

                if ($resultData['status'] === 'present' && $marksObtained !== null) {
                    $grade = $this->calculateGrade($marksObtained, $examSubject->max_marks);
                    $isPassed = $marksObtained >= $examSubject->pass_marks;
                }

                $examResult = ExamResult::updateOrCreate(
                    [
                        'exam_subject_id' => $request->exam_subject_id,
                        'student_id' => $resultData['student_id'],
                    ],
                    [
                        'marks_obtained' => $marksObtained,
                        'grade' => $grade,
                        'is_passed' => $isPassed,
                        'status' => $resultData['status'],
                        'remarks' => $resultData['remarks'] ?? null,
                        'recorded_by' => auth()->id(),
                        'recorded_at' => now(),
                    ]
                );

                if ($examResult->wasRecentlyCreated) {
                    $created++;
                } else {
                    $updated++;
                }
            }

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'bulk_entry',
                'description' => "Bulk entry: Created {$created}, Updated {$updated} exam results for {$examSubject->subject->name}",
                'model_type' => ExamResult::class,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.exam-results.index', ['exam_subject_id' => $request->exam_subject_id])
                            ->with('success', "Bulk entry completed: {$created} created, {$updated} updated.");

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to process bulk entry: ' . $e->getMessage());
        }
    }

    private function calculateGrade($marks, $maxMarks)
    {
        $percentage = ($marks / $maxMarks) * 100;

        if ($percentage >= 90) return 'A+';
        if ($percentage >= 80) return 'A';
        if ($percentage >= 70) return 'B+';
        if ($percentage >= 60) return 'B';
        if ($percentage >= 50) return 'C+';
        if ($percentage >= 40) return 'C';
        if ($percentage >= 30) return 'D';
        return 'F';
    }
}
