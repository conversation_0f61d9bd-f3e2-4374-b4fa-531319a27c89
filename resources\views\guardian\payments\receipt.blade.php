@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Payment Receipt"
        description="Receipt for payment #{{ $payment->payment_reference }}"
        :back-route="route('guardian.payments.show', $payment)"
        back-label="Back to Payment">
        
        <button onclick="window.print()" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
            </svg>
            Print Receipt
        </button>
    </x-page-header>

    <!-- Receipt Content -->
    <div class="bg-white shadow rounded-lg overflow-hidden" id="receipt-content">
        <!-- Receipt Header -->
        <div class="px-8 py-6 bg-blue-50 border-b border-blue-100">
            <div class="text-center">
                <h1 class="text-2xl font-bold text-gray-900">Payment Receipt</h1>
                <p class="text-sm text-gray-600 mt-1">{{ config('app.name') }}</p>
                <p class="text-xs text-gray-500">School Management System</p>
            </div>
        </div>

        <!-- Receipt Details -->
        <div class="px-8 py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Payment Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Information</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Receipt Number</dt>
                            <dd class="text-sm text-gray-900">{{ $payment->payment_reference }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Payment Date</dt>
                            <dd class="text-sm text-gray-900">{{ $payment->paid_at ? $payment->paid_at->format('F j, Y g:i A') : 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Payment Method</dt>
                            <dd class="text-sm text-gray-900">{{ ucfirst($payment->payment_method ?? 'Online Payment') }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Transaction ID</dt>
                            <dd class="text-sm text-gray-900">{{ $payment->billplz_transaction_id ?? $payment->billplz_bill_id ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="text-sm">
                                <span class="badge badge-green">Paid</span>
                            </dd>
                        </div>
                    </dl>
                </div>

                <!-- Student & Invoice Information -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Invoice Information</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Invoice Number</dt>
                            <dd class="text-sm text-gray-900">{{ $payment->invoice->invoice_number }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Student Name</dt>
                            <dd class="text-sm text-gray-900">{{ $payment->invoice->student->user->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Student ID</dt>
                            <dd class="text-sm text-gray-900">{{ $payment->invoice->student->student_id }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Invoice Title</dt>
                            <dd class="text-sm text-gray-900">{{ $payment->invoice->title }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Description</dt>
                            <dd class="text-sm text-gray-900">{{ $payment->invoice->description ?? 'N/A' }}</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Payment Amount -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex justify-between items-center">
                    <span class="text-lg font-medium text-gray-900">Amount Paid</span>
                    <span class="text-2xl font-bold text-green-600">RM {{ number_format($payment->amount, 2) }}</span>
                </div>
            </div>

            <!-- Paid By Information -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Paid By</h3>
                <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Name</dt>
                        <dd class="text-sm text-gray-900">{{ $payment->payer->name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                        <dd class="text-sm text-gray-900">{{ $payment->payer->email }}</dd>
                    </div>
                </dl>
            </div>

            <!-- Footer -->
            <div class="mt-8 pt-6 border-t border-gray-200 text-center">
                <p class="text-xs text-gray-500">
                    This is a computer-generated receipt. No signature is required.
                </p>
                <p class="text-xs text-gray-500 mt-1">
                    Generated on {{ now()->format('F j, Y g:i A') }}
                </p>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
@media print {
    body * {
        visibility: hidden;
    }
    
    #receipt-content, #receipt-content * {
        visibility: visible;
    }
    
    #receipt-content {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        box-shadow: none;
        border-radius: 0;
    }
    
    .btn-primary, .btn-secondary, nav, .sidebar {
        display: none !important;
    }
}
</style>
@endpush
@endsection
