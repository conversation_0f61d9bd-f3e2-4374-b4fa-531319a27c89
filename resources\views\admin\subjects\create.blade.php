@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Add New Subject"
        description="Create a new academic subject"
        :back-route="route('admin.academic.subjects.index')"
        back-label="Back to Subjects">
    </x-page-header>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <form action="{{ route('admin.academic.subjects.store') }}" method="POST" class="space-y-6 p-6">
            @csrf

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Subject Code -->
                <div>
                    <label for="subject_code" class="block text-sm font-medium text-gray-700 mb-2">
                        Subject Code <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="subject_code" 
                           name="subject_code" 
                           value="{{ old('subject_code') }}"
                           class="form-input @error('subject_code') border-red-500 @enderror"
                           placeholder="e.g., MATH101, ENG201"
                           required>
                    @error('subject_code')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Subject Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Subject Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ old('name') }}"
                           class="form-input @error('name') border-red-500 @enderror"
                           placeholder="e.g., Mathematics, English Literature"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Category -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                        Category <span class="text-red-500">*</span>
                    </label>
                    <select id="category" 
                            name="category" 
                            class="form-select @error('category') border-red-500 @enderror"
                            required>
                        <option value="">Select Category</option>
                        <option value="Core" {{ old('category') === 'Core' ? 'selected' : '' }}>Core</option>
                        <option value="Elective" {{ old('category') === 'Elective' ? 'selected' : '' }}>Elective</option>
                        <option value="Extra-curricular" {{ old('category') === 'Extra-curricular' ? 'selected' : '' }}>Extra-curricular</option>
                    </select>
                    @error('category')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Credits -->
                <div>
                    <label for="credits" class="block text-sm font-medium text-gray-700 mb-2">
                        Credits <span class="text-red-500">*</span>
                    </label>
                    <input type="number" 
                           id="credits" 
                           name="credits" 
                           value="{{ old('credits', 1) }}"
                           min="1" 
                           max="10"
                           class="form-input @error('credits') border-red-500 @enderror"
                           required>
                    @error('credits')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="3"
                          class="form-textarea @error('description') border-red-500 @enderror"
                          placeholder="Brief description of the subject...">{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Grade Levels -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Grade Levels
                </label>
                <p class="text-sm text-gray-500 mb-3">Select which grade levels this subject is available for (leave empty for all grades)</p>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                    @foreach($classes as $class)
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   name="grade_levels[]" 
                                   value="{{ $class->name }}"
                                   {{ in_array($class->name, old('grade_levels', [])) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">{{ $class->name }}</span>
                        </label>
                    @endforeach
                </div>
                @error('grade_levels')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Prerequisites -->
            <div>
                <label for="prerequisites" class="block text-sm font-medium text-gray-700 mb-2">
                    Prerequisites
                </label>
                <p class="text-sm text-gray-500 mb-3">Select subjects that are required before taking this subject</p>
                <select id="prerequisites" 
                        name="prerequisites[]" 
                        multiple
                        class="form-select @error('prerequisites') border-red-500 @enderror"
                        size="5">
                    @foreach($existingSubjects as $subject)
                        <option value="{{ $subject->id }}" 
                                {{ in_array($subject->id, old('prerequisites', [])) ? 'selected' : '' }}>
                            {{ $subject->name }} ({{ $subject->subject_code }})
                        </option>
                    @endforeach
                </select>
                <p class="mt-1 text-xs text-gray-500">Hold Ctrl/Cmd to select multiple subjects</p>
                @error('prerequisites')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Status -->
            <div>
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="is_active" 
                           name="is_active" 
                           value="1"
                           {{ old('is_active', true) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="is_active" class="ml-2 text-sm text-gray-700">
                        Active (subject is available for assignment)
                    </label>
                </div>
                @error('is_active')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.academic.subjects.index') }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Subject
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
