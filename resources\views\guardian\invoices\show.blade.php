@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Invoice Details"
        description="View invoice information and make payment"
        :back-route="route('guardian.invoices.index')"
        back-label="Back to Invoices">
    </x-page-header>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Invoice Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Invoice Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-start mb-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">{{ $invoice->title }}</h3>
                        <p class="text-sm text-gray-500">Invoice #{{ $invoice->invoice_number }}</p>
                    </div>
                    <div class="text-right">
                        @php
                            $statusClasses = [
                                'sent' => 'badge-yellow',
                                'paid' => 'badge-green',
                                'overdue' => 'badge-red',
                                'cancelled' => 'badge-purple',
                            ];
                        @endphp
                        <span class="badge {{ $statusClasses[$invoice->status] ?? 'badge-gray' }}">
                            {{ $invoice->status === 'sent' ? 'Pending' : ucfirst($invoice->status) }}
                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Student Information</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <p><strong>Name:</strong> {{ $invoice->student->user->name ?? 'N/A' }}</p>
                            <p><strong>Student ID:</strong> {{ $invoice->student->student_id ?? 'N/A' }}</p>
                            <p><strong>Class:</strong> {{ $invoice->student->class ?? 'N/A' }}</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Invoice Details</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <p><strong>Type:</strong> {{ ucfirst($invoice->type) }}</p>
                            <p><strong>Created:</strong> {{ $invoice->created_at->format('M d, Y') }}</p>
                            <p><strong>Due Date:</strong> 
                                <span class="{{ $invoice->due_date->isPast() && $invoice->status !== 'paid' ? 'text-red-600 font-medium' : '' }}">
                                    {{ $invoice->due_date->format('M d, Y') }}
                                    @if($invoice->due_date->isPast() && $invoice->status !== 'paid')
                                        (Overdue)
                                    @endif
                                </span>
                            </p>
                        </div>
                    </div>
                </div>

                @if($invoice->description)
                    <div class="mt-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Description</h4>
                        <p class="text-sm text-gray-600">{{ $invoice->description }}</p>
                    </div>
                @endif
            </div>

            <!-- Line Items -->
            @if($invoice->line_items && count($invoice->line_items) > 0)
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Line Items</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($invoice->line_items as $item)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item['description'] }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">RM {{ number_format($item['amount'], 2) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif

            <!-- Payment History -->
            @if($invoice->payments->count() > 0)
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Payment History</h3>
                    <div class="space-y-4">
                        @foreach($invoice->payments as $payment)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $payment->payment_reference }}</p>
                                        <p class="text-sm text-gray-500">
                                            @if($payment->paid_at)
                                                Paid on: {{ $payment->paid_at->format('M d, Y H:i') }}
                                            @else
                                                Created: {{ $payment->created_at->format('M d, Y H:i') }}
                                            @endif
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-medium text-gray-900">RM {{ number_format($payment->amount, 2) }}</p>
                                        @php
                                            $paymentStatusClasses = [
                                                'pending' => 'badge-yellow',
                                                'paid' => 'badge-green',
                                                'failed' => 'badge-red',
                                                'refunded' => 'badge-purple',
                                            ];
                                        @endphp
                                        <span class="badge {{ $paymentStatusClasses[$payment->status] ?? 'badge-gray' }}">
                                            {{ ucfirst($payment->status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Amount Summary -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Amount Summary</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Subtotal:</span>
                        <span class="text-sm font-medium text-gray-900">RM {{ number_format($invoice->amount, 2) }}</span>
                    </div>
                    @if($invoice->discount > 0)
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Discount:</span>
                            <span class="text-sm font-medium text-red-600">-RM {{ number_format($invoice->discount, 2) }}</span>
                        </div>
                    @endif
                    <div class="border-t border-gray-200 pt-3">
                        <div class="flex justify-between">
                            <span class="text-base font-medium text-gray-900">Total:</span>
                            <span class="text-base font-bold text-blue-600">RM {{ number_format($invoice->total_amount, 2) }}</span>
                        </div>
                    </div>
                    
                    @php
                        $totalPaid = $invoice->payments->where('status', 'paid')->sum('amount');
                        $remainingAmount = $invoice->total_amount - $totalPaid;
                    @endphp
                    
                    @if($totalPaid > 0)
                        <div class="border-t border-gray-200 pt-3 space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Paid:</span>
                                <span class="text-sm font-medium text-green-600">RM {{ number_format($totalPaid, 2) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900">Remaining:</span>
                                <span class="text-sm font-bold {{ $remainingAmount > 0 ? 'text-red-600' : 'text-green-600' }}">
                                    RM {{ number_format($remainingAmount, 2) }}
                                </span>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Payment Actions -->
            @if(in_array($invoice->status, ['sent', 'overdue']) && $remainingAmount > 0)
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Make Payment</h3>
                    
                    <form id="payment-form" method="POST" action="{{ route('guardian.invoices.pay', $invoice) }}" class="space-y-4">
                        @csrf
                        <div>
                            <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">Payment Amount (RM)</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">RM</span>
                                </div>
                                <input type="number" name="amount" id="amount" 
                                       value="{{ $remainingAmount }}" 
                                       step="0.01" min="0.01" max="{{ $remainingAmount }}"
                                       class="form-input pl-12" required>
                            </div>
                            <p class="mt-1 text-sm text-gray-500">Maximum: RM {{ number_format($remainingAmount, 2) }}</p>
                        </div>

                        <button type="button" id="pay-now-btn" class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            Pay Now via Billplz
                        </button>
                    </form>

                    <div class="mt-4 text-xs text-gray-500">
                        <p>Secure payment powered by Billplz</p>
                        <p>Supports FPX, Credit/Debit Cards, E-wallets</p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>



@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const payNowBtn = document.getElementById('pay-now-btn');
    const paymentForm = document.getElementById('payment-form');
    const amountInput = document.getElementById('amount');

    if (payNowBtn) {
        payNowBtn.addEventListener('click', async function() {
            const amount = parseFloat(amountInput.value);
            if (amount > 0) {
                const confirmed = await confirmPayment(`RM ${amount.toFixed(2)} for Invoice #{{ $invoice->invoice_number }}`);

                if (confirmed) {
                    paymentForm.submit();
                }
            } else {
                showToast('Please enter a valid payment amount', 'error');
            }
        });
    }
});
</script>
@endpush
@endsection
