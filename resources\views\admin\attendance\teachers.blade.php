@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Teacher Attendance</h1>
                <p class="text-gray-600">Mark and manage teacher attendance records</p>
            </div>
            <a href="{{ route('admin.attendance.index') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Filters and Bulk Actions -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <input type="text" id="search" name="search" placeholder="Search teachers..."
                       value="{{ request('search') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label for="date" class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                <input type="date" id="date" name="date" value="{{ $date }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="status" name="status"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Status</option>
                    <option value="present" {{ $status === 'present' ? 'selected' : '' }}>Present</option>
                    <option value="absent" {{ $status === 'absent' ? 'selected' : '' }}>Absent</option>
                    <option value="late" {{ $status === 'late' ? 'selected' : '' }}>Late</option>
                    <option value="sick_leave" {{ $status === 'sick_leave' ? 'selected' : '' }}>Sick Leave</option>
                    <option value="personal_leave" {{ $status === 'personal_leave' ? 'selected' : '' }}>Personal Leave</option>
                    <option value="official_duty" {{ $status === 'official_duty' ? 'selected' : '' }}>Official Duty</option>
                    <option value="not_marked" {{ $status === 'not_marked' ? 'selected' : '' }}>Not Marked</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="button" onclick="clearFilters()"
                        class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Bulk Selection Section -->
        <div class="border-t border-gray-200 pt-4">
            <div class="flex items-center gap-4">
                <div class="flex items-center">
                    <input type="checkbox" id="selectAll" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="selectAll" class="ml-2 text-sm text-gray-700">Select All</label>
                </div>
                <span id="selectedCount" class="text-sm text-gray-500">0 selected</span>
                <div class="text-sm text-gray-600">
                    <span class="italic">Select teachers and use individual action buttons to mark attendance</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="bg-white shadow rounded-lg p-4">
        <h3 class="text-lg font-medium text-gray-900 mb-3">Teacher Attendance Statistics for {{ $date }}</h3>
        <div class="grid grid-cols-4 gap-3 mb-3">
            <div class="text-center p-2 bg-gray-50 rounded">
                <div class="text-lg font-bold text-gray-900">{{ $attendanceStats['total_records'] }}</div>
                <div class="text-xs text-gray-500">Total</div>
            </div>
            <div class="text-center p-2 bg-green-50 rounded">
                <div class="text-lg font-bold text-green-600">{{ $attendanceStats['present'] }}</div>
                <div class="text-xs text-gray-500">Present</div>
            </div>
            <div class="text-center p-2 bg-red-50 rounded">
                <div class="text-lg font-bold text-red-600">{{ $attendanceStats['absent'] }}</div>
                <div class="text-xs text-gray-500">Absent</div>
            </div>
            <div class="text-center p-2 bg-purple-50 rounded">
                <div class="text-lg font-bold text-purple-600">{{ $attendanceStats['sick_leave'] }}</div>
                <div class="text-xs text-gray-500">Sick Leave</div>
            </div>
        </div>
        <div class="flex items-center justify-between text-sm">
            <span>Attendance Rate</span>
            <span class="font-semibold">{{ $attendanceStats['attendance_percentage'] }}%</span>
        </div>
    </div>

    <!-- Teacher List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Teachers</h3>
                <span class="text-sm text-gray-500">{{ $teachers->count() }} teachers</span>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="selectAllTable" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check-in</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check-out</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hours</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($teachers as $teacher)
                        @php
                            $attendance = $teacher->attendances->first();
                        @endphp
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="teacher-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                       value="{{ $teacher->id }}" data-teacher-name="{{ $teacher->user->name }}">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                            <span class="text-sm font-medium text-green-600">
                                                {{ substr($teacher->user->name, 0, 2) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $teacher->user->name }}</div>
                                        <div class="text-sm text-gray-500">ID: {{ $teacher->employee_id }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $teacher->specialization ?? 'General' }}</div>
                                <div class="text-sm text-gray-500">{{ $teacher->qualification ?? 'N/A' }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($attendance)
                                    <span class="badge badge-{{ $attendance->status_badge_color }}">
                                        {{ $attendance->status_display }}
                                    </span>
                                @else
                                    <span class="badge badge-gray">Not Marked</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $attendance && $attendance->check_in_time ? $attendance->check_in_time->format('H:i') : '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $attendance && $attendance->check_out_time ? $attendance->check_out_time->format('H:i') : '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $attendance && $attendance->hours_worked ? number_format($attendance->hours_worked, 1) . 'h' : '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $attendance ? Str::limit($attendance->notes, 20) : '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="markAttendance({{ $teacher->id }}, '{{ $teacher->user->name }}', '{{ $attendance ? $attendance->status : '' }}', '{{ $attendance && $attendance->check_in_time ? $attendance->check_in_time->format('H:i') : '' }}', '{{ $attendance && $attendance->check_out_time ? $attendance->check_out_time->format('H:i') : '' }}', '{{ $attendance ? $attendance->notes : '' }}')" 
                                        class="text-blue-600 hover:text-blue-900">
                                    {{ $attendance ? 'Edit' : 'Mark' }}
                                </button>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9" class="px-6 py-8 text-center text-sm text-gray-500">
                                No teachers found matching the selected criteria.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Attendance Modal -->
<div id="attendanceModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-6 w-96 shadow-xl rounded-lg bg-white">
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-6" id="modalTitle">Mark Attendance</h3>
            <form id="attendanceForm">
                <input type="hidden" id="teacherId" name="teacher_id">
                <input type="hidden" name="date" value="{{ $date }}">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Teacher</label>
                    <p id="teacherName" class="text-sm text-gray-900 font-medium"></p>
                </div>

                <div class="mb-4">
                    <label for="attendanceStatus" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="attendanceStatus" name="status" required class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none">
                        <option value="present">Present</option>
                        <option value="absent">Absent</option>
                        <option value="late">Late</option>
                        <option value="sick_leave">Sick Leave</option>
                        <option value="personal_leave">Personal Leave</option>
                        <option value="official_duty">Official Duty</option>
                    </select>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="checkInTime" class="block text-sm font-medium text-gray-700 mb-2">Check-in Time</label>
                        <input type="time" id="checkInTime" name="check_in_time"
                               class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none">
                    </div>
                    <div>
                        <label for="checkOutTime" class="block text-sm font-medium text-gray-700 mb-2">Check-out Time</label>
                        <input type="time" id="checkOutTime" name="check_out_time"
                               class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none">
                    </div>
                </div>

                <div class="mb-6">
                    <label for="attendanceNotes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea id="attendanceNotes" name="notes" rows="3"
                              class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none"
                              placeholder="Optional notes..."></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Save Attendance</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Attendance Modal -->
<div id="bulkAttendanceModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-6 w-96 shadow-xl rounded-lg bg-white">
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-6" id="bulkModalTitle">Bulk Mark Attendance</h3>
            <form id="bulkAttendanceForm">
                <input type="hidden" id="bulkDateHidden" name="date">
                <input type="hidden" id="bulkTeacherIds" name="teacher_ids">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Selected Teachers</label>
                    <p id="bulkTeacherCount" class="text-sm text-gray-900 font-medium"></p>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                    <p id="bulkModalDateDisplay" class="text-sm text-gray-900 font-medium"></p>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="bulkStatusSelect" name="status" required class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none">
                        <option value="present">Present</option>
                        <option value="absent">Absent</option>
                        <option value="late">Late</option>
                        <option value="sick_leave">Sick Leave</option>
                        <option value="personal_leave">Personal Leave</option>
                        <option value="official_duty">Official Duty</option>
                    </select>
                </div>

                <div class="mb-6">
                    <label for="bulkNotes" class="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                    <textarea id="bulkNotes" name="notes" rows="3"
                              class="block w-full px-4 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none"
                              placeholder="Add notes for this bulk attendance marking..."></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeBulkModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Mark Attendance</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function markAttendance(teacherId, teacherName, currentStatus, currentCheckIn, currentCheckOut, currentNotes) {
    // Check if there are multiple teachers selected
    const checkedBoxes = document.querySelectorAll('.teacher-checkbox:checked');

    if (checkedBoxes.length > 1) {
        // Bulk operation
        const teacherIds = Array.from(checkedBoxes).map(cb => cb.value);
        const date = document.getElementById('date').value;

        if (!date) {
            showToast('Please select a date from the filter', 'error');
            return;
        }

        // Show bulk attendance modal
        document.getElementById('bulkDateHidden').value = date;
        document.getElementById('bulkTeacherIds').value = JSON.stringify(teacherIds);
        document.getElementById('bulkTeacherCount').textContent = `${teacherIds.length} teacher(s) selected`;
        document.getElementById('bulkModalDateDisplay').textContent = new Date(date).toLocaleDateString();
        document.getElementById('bulkModalTitle').textContent = 'Bulk Mark Attendance';
        document.getElementById('bulkStatusSelect').value = 'present';
        document.getElementById('bulkNotes').value = '';
        document.getElementById('bulkAttendanceModal').classList.remove('hidden');
    } else {
        // Single operation
        document.getElementById('teacherId').value = teacherId;
        document.getElementById('teacherName').textContent = teacherName;
        document.getElementById('attendanceStatus').value = currentStatus || 'present';
        document.getElementById('checkInTime').value = currentCheckIn || '';
        document.getElementById('checkOutTime').value = currentCheckOut || '';
        document.getElementById('attendanceNotes').value = currentNotes || '';
        document.getElementById('modalTitle').textContent = currentStatus ? 'Edit Attendance' : 'Mark Attendance';
        document.getElementById('attendanceModal').classList.remove('hidden');
    }
}

function closeModal() {
    document.getElementById('attendanceModal').classList.add('hidden');
}

document.getElementById('attendanceForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    try {
        const response = await fetch('{{ route("admin.attendance.mark-teacher") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast('Attendance marked successfully!', 'success');
            closeModal();
            // Force reload with cache busting
            setTimeout(() => {
                window.location.href = window.location.href + (window.location.href.includes('?') ? '&' : '?') + '_t=' + Date.now();
            }, 1000);
        } else {
            showToast(result.message || 'Error marking attendance', 'error');
        }
    } catch (error) {
        showToast('Error marking attendance', 'error');
    }
});

// Close modal when clicking outside
document.getElementById('attendanceModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Bulk selection functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.teacher-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkButtons();
});

document.getElementById('selectAllTable').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.teacher-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    document.getElementById('selectAll').checked = this.checked;
    updateBulkButtons();
});

document.addEventListener('change', function(e) {
    if (e.target.classList.contains('teacher-checkbox')) {
        updateBulkButtons();

        // Update select all checkboxes
        const checkboxes = document.querySelectorAll('.teacher-checkbox');
        const checkedBoxes = document.querySelectorAll('.teacher-checkbox:checked');
        const selectAllMain = document.getElementById('selectAll');
        const selectAllTable = document.getElementById('selectAllTable');

        if (checkedBoxes.length === 0) {
            selectAllMain.indeterminate = false;
            selectAllMain.checked = false;
            selectAllTable.indeterminate = false;
            selectAllTable.checked = false;
        } else if (checkedBoxes.length === checkboxes.length) {
            selectAllMain.indeterminate = false;
            selectAllMain.checked = true;
            selectAllTable.indeterminate = false;
            selectAllTable.checked = true;
        } else {
            selectAllMain.indeterminate = true;
            selectAllTable.indeterminate = true;
        }
    }
});

function updateBulkButtons() {
    const checkedBoxes = document.querySelectorAll('.teacher-checkbox:checked');
    const selectedCount = document.getElementById('selectedCount');

    const count = checkedBoxes.length;
    selectedCount.textContent = `${count} selected`;
}

function closeBulkModal() {
    document.getElementById('bulkAttendanceModal').classList.add('hidden');
}

// Handle bulk attendance form submission
document.addEventListener('DOMContentLoaded', function() {
    const bulkForm = document.getElementById('bulkAttendanceForm');
    if (bulkForm) {
        bulkForm.addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const teacherIds = JSON.parse(formData.get('teacher_ids'));
    const status = document.getElementById('bulkStatusSelect').value;
    const data = {
        teacher_ids: teacherIds,
        date: formData.get('date'),
        status: status,
        notes: formData.get('notes') || ''
    };

    try {
        console.log('Sending bulk teacher attendance data:', data);

        const response = await fetch('{{ route("admin.attendance.bulk-mark-teachers") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify(data)
        });

        console.log('Response status:', response.status);
        const result = await response.json();
        console.log('Response data:', result);

        if (result.success) {
            showToast(result.message, 'success');
            closeBulkModal();
            location.reload();
        } else {
            showToast(result.message || 'Error marking attendance', 'error');
            if (result.errors) {
                console.error('Validation errors:', result.errors);
            }
        }
    } catch (error) {
        console.error('Bulk teacher attendance error:', error);
        showToast('Error marking attendance: ' + error.message, 'error');
    }
});
    }

    // Close modal when clicking outside
    const bulkModal = document.getElementById('bulkAttendanceModal');
    if (bulkModal) {
        bulkModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeBulkModal();
            }
        });
    }
});

// Instant filtering functionality
function applyFilters() {
    const search = document.getElementById('search').value;
    const date = document.getElementById('date').value;
    const status = document.getElementById('status').value;

    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (date) params.append('date', date);
    if (status) params.append('status', status);

    window.location.href = '{{ route("admin.attendance.teachers") }}?' + params.toString();
}

function clearFilters() {
    window.location.href = '{{ route("admin.attendance.teachers") }}';
}

// Add event listeners for instant filtering
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    const dateInput = document.getElementById('date');
    const statusSelect = document.getElementById('status');

    let searchTimeout;

    // Search with debounce
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(applyFilters, 500);
    });

    // Date filter changes - apply filter
    dateInput.addEventListener('change', applyFilters);

    // Status change instantly
    statusSelect.addEventListener('change', applyFilters);
});
</script>
@endpush
@endsection
