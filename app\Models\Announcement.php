<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Announcement extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'type',
        'target_audience',
        'target_classes',
        'publish_date',
        'expire_date',
        'is_published',
        'is_pinned',
        'created_by',
    ];

    protected $casts = [
        'target_classes' => 'array',
        'publish_date' => 'date',
        'expire_date' => 'date',
        'is_published' => 'boolean',
        'is_pinned' => 'boolean',
    ];

    /**
     * Get the user who created this announcement.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get published announcements.
     */
    public static function published()
    {
        return static::where('is_published', true)
                    ->where('publish_date', '<=', now())
                    ->where(function ($query) {
                        $query->whereNull('expire_date')
                              ->orWhere('expire_date', '>=', now());
                    });
    }

    /**
     * Get pinned announcements.
     */
    public static function pinned()
    {
        return static::published()->where('is_pinned', true);
    }

    /**
     * Get recent announcements.
     */
    public static function recent($limit = 5)
    {
        return static::published()
                    ->orderBy('is_pinned', 'desc')
                    ->orderBy('publish_date', 'desc')
                    ->limit($limit);
    }

    /**
     * Get announcements for specific audience.
     */
    public static function forAudience($audience)
    {
        return static::published()
                    ->where(function ($query) use ($audience) {
                        $query->where('target_audience', 'all')
                              ->orWhere('target_audience', $audience);
                    });
    }

    /**
     * Get type badge color.
     */
    public function getTypeBadgeColorAttribute(): string
    {
        return match($this->type) {
            'urgent' => 'bg-red-100 text-red-800',
            'academic' => 'bg-blue-100 text-blue-800',
            'event' => 'bg-green-100 text-green-800',
            'reminder' => 'bg-yellow-100 text-yellow-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get formatted publish date.
     */
    public function getFormattedPublishDateAttribute(): string
    {
        return $this->publish_date->format('M d, Y');
    }

    /**
     * Check if announcement is active.
     */
    public function isActive(): bool
    {
        return $this->is_published && 
               $this->publish_date <= now() && 
               (!$this->expire_date || $this->expire_date >= now());
    }

    /**
     * Check if announcement is expired.
     */
    public function isExpired(): bool
    {
        return $this->expire_date && $this->expire_date < now();
    }
}
