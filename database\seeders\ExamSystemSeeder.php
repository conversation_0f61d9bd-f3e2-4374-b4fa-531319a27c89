<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ExamType;
use App\Models\ReportCardTemplate;

class ExamSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Exam Types
        $examTypes = [
            [
                'name' => 'Midterm Exam',
                'description' => 'Mid-semester examination',
                'weight_percentage' => 30.00,
                'is_active' => true,
            ],
            [
                'name' => 'Final Exam',
                'description' => 'End of semester examination',
                'weight_percentage' => 50.00,
                'is_active' => true,
            ],
            [
                'name' => 'Quiz',
                'description' => 'Short assessment quiz',
                'weight_percentage' => 10.00,
                'is_active' => true,
            ],
            [
                'name' => 'Assignment',
                'description' => 'Course assignment evaluation',
                'weight_percentage' => 10.00,
                'is_active' => true,
            ],
        ];

        foreach ($examTypes as $examType) {
            ExamType::create($examType);
        }

        // Create Report Card Templates
        $templates = [
            [
                'name' => 'Standard Report Card',
                'description' => 'Standard academic report card template',
                'layout_config' => [
                    'header' => [
                        'school_name' => true,
                        'school_logo' => true,
                        'academic_year' => true,
                        'term' => true,
                    ],
                    'student_info' => [
                        'name' => true,
                        'student_id' => true,
                        'class' => true,
                        'section' => true,
                    ],
                    'grades_table' => [
                        'subject' => true,
                        'assignment_marks' => true,
                        'exam_marks' => true,
                        'total_marks' => true,
                        'percentage' => true,
                        'grade' => true,
                        'gpa_points' => true,
                    ],
                    'summary' => [
                        'overall_gpa' => true,
                        'overall_grade' => true,
                        'class_rank' => true,
                        'attendance_percentage' => true,
                    ],
                    'remarks' => [
                        'teacher_remarks' => true,
                        'principal_remarks' => true,
                    ],
                ],
                'grade_components' => [
                    'assignments' => true,
                    'exams' => true,
                    'quizzes' => true,
                ],
                'calculation_rules' => [
                    'assignment_weight' => 30,
                    'exam_weight' => 60,
                    'quiz_weight' => 10,
                    'pass_percentage' => 40,
                ],
                'include_attendance' => true,
                'include_remarks' => true,
                'is_default' => true,
                'is_active' => true,
            ],
            [
                'name' => 'Simplified Report Card',
                'description' => 'Simplified report card with basic information',
                'layout_config' => [
                    'header' => [
                        'school_name' => true,
                        'academic_year' => true,
                        'term' => true,
                    ],
                    'student_info' => [
                        'name' => true,
                        'class' => true,
                    ],
                    'grades_table' => [
                        'subject' => true,
                        'total_marks' => true,
                        'percentage' => true,
                        'grade' => true,
                    ],
                    'summary' => [
                        'overall_grade' => true,
                        'attendance_percentage' => true,
                    ],
                ],
                'grade_components' => [
                    'exams' => true,
                ],
                'calculation_rules' => [
                    'exam_weight' => 100,
                    'pass_percentage' => 40,
                ],
                'include_attendance' => true,
                'include_remarks' => false,
                'is_default' => false,
                'is_active' => true,
            ],
        ];

        foreach ($templates as $template) {
            ReportCardTemplate::create($template);
        }

        echo "Exam system data seeded successfully!\n";
    }
}
