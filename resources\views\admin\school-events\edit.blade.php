@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Edit School Event"
        description="Update school event information"
        :back-route="route('admin.school-events.show', $schoolEvent)"
        back-label="Back to Event Details">
    </x-page-header>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Event Information</h3>
        </div>
        <form method="POST" action="{{ route('admin.school-events.update', $schoolEvent) }}" class="p-6">
            @csrf
            @method('PUT')
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Title -->
                <div class="md:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700">Event Title *</label>
                    <input type="text" id="title" name="title" value="{{ old('title', $schoolEvent->title) }}" class="form-input mt-1" required placeholder="e.g., Annual Sports Day">
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Event Date -->
                <div>
                    <label for="event_date" class="block text-sm font-medium text-gray-700">Event Date *</label>
                    <input type="date" id="event_date" name="event_date" value="{{ old('event_date', $schoolEvent->event_date->format('Y-m-d')) }}" class="form-input mt-1" required>
                    @error('event_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Location -->
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
                    <input type="text" id="location" name="location" value="{{ old('location', $schoolEvent->location) }}" class="form-input mt-1" placeholder="e.g., School Auditorium">
                    @error('location')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Start Time -->
                <div>
                    <label for="start_time" class="block text-sm font-medium text-gray-700">Start Time</label>
                    <input type="time" id="start_time" name="start_time" value="{{ old('start_time', $schoolEvent->start_time ? $schoolEvent->start_time->format('H:i') : '') }}" class="form-input mt-1">
                    @error('start_time')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- End Time -->
                <div>
                    <label for="end_time" class="block text-sm font-medium text-gray-700">End Time</label>
                    <input type="time" id="end_time" name="end_time" value="{{ old('end_time', $schoolEvent->end_time ? $schoolEvent->end_time->format('H:i') : '') }}" class="form-input mt-1">
                    @error('end_time')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Type -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700">Event Type *</label>
                    <select id="type" name="type" class="form-select mt-1" required>
                        <option value="">Select Type</option>
                        <option value="holiday" {{ old('type', $schoolEvent->type) == 'holiday' ? 'selected' : '' }}>Holiday</option>
                        <option value="exam" {{ old('type', $schoolEvent->type) == 'exam' ? 'selected' : '' }}>Exam</option>
                        <option value="sports" {{ old('type', $schoolEvent->type) == 'sports' ? 'selected' : '' }}>Sports</option>
                        <option value="cultural" {{ old('type', $schoolEvent->type) == 'cultural' ? 'selected' : '' }}>Cultural</option>
                        <option value="meeting" {{ old('type', $schoolEvent->type) == 'meeting' ? 'selected' : '' }}>Meeting</option>
                        <option value="other" {{ old('type', $schoolEvent->type) == 'other' ? 'selected' : '' }}>Other</option>
                    </select>
                    @error('type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Scope -->
                <div>
                    <label for="scope" class="block text-sm font-medium text-gray-700">Scope *</label>
                    <select id="scope" name="scope" class="form-select mt-1" required onchange="toggleTargetClasses()">
                        <option value="">Select Scope</option>
                        <option value="all" {{ old('scope', $schoolEvent->scope) == 'all' ? 'selected' : '' }}>All</option>
                        <option value="class" {{ old('scope', $schoolEvent->scope) == 'class' ? 'selected' : '' }}>Specific Classes</option>
                        <option value="section" {{ old('scope', $schoolEvent->scope) == 'section' ? 'selected' : '' }}>Specific Sections</option>
                        <option value="teachers" {{ old('scope', $schoolEvent->scope) == 'teachers' ? 'selected' : '' }}>Teachers Only</option>
                        <option value="parents" {{ old('scope', $schoolEvent->scope) == 'parents' ? 'selected' : '' }}>Parents Only</option>
                    </select>
                    @error('scope')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Target Classes (conditional) -->
            <div id="target-classes-section" class="mt-6">
                <label class="block text-sm font-medium text-gray-700">Target Classes</label>
                <div class="mt-2 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    @foreach($classes as $class)
                        <div class="flex items-center">
                            <input type="checkbox" id="class_{{ $class->id }}" name="target_classes[]" value="{{ $class->id }}" 
                                   {{ in_array($class->id, old('target_classes', $schoolEvent->target_classes ?? [])) ? 'checked' : '' }} class="form-checkbox">
                            <label for="class_{{ $class->id }}" class="ml-2 text-sm text-gray-700">{{ $class->name }}</label>
                        </div>
                    @endforeach
                </div>
                @error('target_classes')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Description -->
            <div class="mt-6">
                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                <textarea id="description" name="description" rows="4" class="form-textarea mt-1" placeholder="Event description and details...">{{ old('description', $schoolEvent->description) }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Status -->
            <div class="mt-6">
                <div class="flex items-center">
                    <input type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', $schoolEvent->is_active) ? 'checked' : '' }} class="form-checkbox">
                    <label for="is_active" class="ml-2 text-sm text-gray-700">Active</label>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-8 flex items-center justify-end space-x-3">
                <a href="{{ route('admin.school-events.show', $schoolEvent) }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Event
                </button>
            </div>
        </form>
    </div>

    <!-- Danger Zone -->
    <div class="bg-white shadow rounded-lg border border-red-200">
        <div class="px-6 py-4 border-b border-red-200">
            <h3 class="text-lg font-medium text-red-900">Danger Zone</h3>
        </div>
        <div class="p-6">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-red-900">Delete School Event</h4>
                    <p class="text-sm text-red-700 mt-1">
                        Once you delete this school event, there is no going back. This action cannot be undone.
                    </p>
                    <div class="mt-4">
                        <button type="button" onclick="deleteEvent('{{ $schoolEvent->id }}', '{{ $schoolEvent->title }}')" class="btn-danger">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete School Event
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Toggle target classes section based on scope
function toggleTargetClasses() {
    const scope = document.getElementById('scope').value;
    const targetClassesSection = document.getElementById('target-classes-section');
    
    if (scope === 'class' || scope === 'section') {
        targetClassesSection.style.display = 'block';
    } else {
        targetClassesSection.style.display = 'none';
        // Uncheck all checkboxes when hiding
        const checkboxes = targetClassesSection.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => checkbox.checked = false);
    }
}

// Validate end time is after start time
document.getElementById('start_time').addEventListener('change', function() {
    const startTime = this.value;
    const endTimeInput = document.getElementById('end_time');
    
    if (startTime && endTimeInput.value && endTimeInput.value <= startTime) {
        alert('End time must be after start time');
        endTimeInput.value = '';
    }
});

document.getElementById('end_time').addEventListener('change', function() {
    const endTime = this.value;
    const startTime = document.getElementById('start_time').value;
    
    if (startTime && endTime && endTime <= startTime) {
        alert('End time must be after start time');
        this.value = '';
    }
});

// Delete event function
async function deleteEvent(eventId, eventTitle) {
    const confirmed = await confirmAction('Delete School Event', `Are you sure you want to delete the event "${eventTitle}"? This action cannot be undone.`);

    if (confirmed) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ route('admin.school-events.index') }}/${eventId}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // Add method override
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        document.body.appendChild(form);
        form.submit();
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleTargetClasses();
});
</script>
@endpush
