<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\Grade;
use App\Models\Subject;
use App\Models\Student;
use App\Models\AcademicTerm;
use App\Models\GradeCategory;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GradeController extends Controller
{
    /**
     * Display grades for teacher's classes
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $teacher = $user->teacher;

        if (!$teacher) {
            abort(403, 'Teacher profile not found.');
        }

        $search = $request->get('search', '');
        $subjectId = $request->get('subject_id', '');
        $termId = $request->get('academic_term_id', '');
        $categoryId = $request->get('category_id', '');
        $studentId = $request->get('student_id', '');

        // Build grades query for this teacher only
        $query = Grade::where('teacher_id', $teacher->id)
            ->with([
                'student.user',
                'subject',
                'gradeCategory',
                'academicTerm'
            ]);

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('assignment_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('student.user', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('subject', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('gradeCategory', function ($cq) use ($search) {
                      $cq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Apply filters
        if ($subjectId) {
            $query->where('subject_id', $subjectId);
        }

        if ($termId) {
            $query->where('academic_term_id', $termId);
        }

        if ($categoryId) {
            $query->where('grade_category_id', $categoryId);
        }

        if ($studentId) {
            $query->where('student_id', $studentId);
        }

        $grades = $query->latest('assignment_date')->paginate(25);

        // Get filter options (only for subjects this teacher teaches)
        $subjects = Subject::whereHas('grades', function ($q) use ($teacher) {
            $q->where('teacher_id', $teacher->id);
        })->orderBy('name')->get();

        $academicTerms = AcademicTerm::whereHas('grades', function ($q) use ($teacher) {
            $q->where('teacher_id', $teacher->id);
        })->orderBy('name')->get();

        $gradeCategories = GradeCategory::whereHas('grades', function ($q) use ($teacher) {
            $q->where('teacher_id', $teacher->id);
        })->orderBy('name')->get();

        $students = Student::whereHas('grades', function ($q) use ($teacher) {
            $q->where('teacher_id', $teacher->id);
        })->with('user')->get();

        // Calculate statistics
        $stats = [
            'total_grades' => Grade::where('teacher_id', $teacher->id)->count(),
            'published_grades' => Grade::where('teacher_id', $teacher->id)->where('is_published', true)->count(),
            'draft_grades' => Grade::where('teacher_id', $teacher->id)->where('is_published', false)->count(),
            'students_count' => Student::whereHas('grades', function ($q) use ($teacher) {
                $q->where('teacher_id', $teacher->id);
            })->count(),
        ];

        return view('teacher.grades.index', compact(
            'grades',
            'subjects',
            'academicTerms',
            'gradeCategories',
            'students',
            'stats',
            'search',
            'subjectId',
            'termId',
            'categoryId',
            'studentId',
            'teacher'
        ));
    }

    /**
     * Show the form for creating a new grade
     */
    public function create()
    {
        $user = Auth::user();
        $teacher = $user->teacher;

        if (!$teacher) {
            abort(403, 'Teacher profile not found.');
        }

        // Get subjects this teacher can grade for
        $subjects = Subject::orderBy('name')->get();
        $students = Student::with('user')
            ->join('users', 'students.user_id', '=', 'users.id')
            ->orderBy('users.name')
            ->select('students.*')
            ->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::active()->orderBy('name')->get();

        return view('teacher.grades.create', compact(
            'subjects',
            'students',
            'academicTerms',
            'gradeCategories',
            'teacher'
        ));
    }

    /**
     * Store a newly created grade
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        $teacher = $user->teacher;

        if (!$teacher) {
            abort(403, 'Teacher profile not found.');
        }

        $validated = $request->validate([
            'student_id' => 'required|exists:students,id',
            'subject_id' => 'required|exists:subjects,id',
            'grade_category_id' => 'required|exists:grade_categories,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'assignment_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'score' => 'required|numeric|min:0',
            'max_score' => 'required|numeric|min:0.01',
            'assignment_date' => 'required|date',
            'due_date' => 'nullable|date',
            'comments' => 'nullable|string',
            'is_published' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $grade = Grade::create([
                ...$validated,
                'teacher_id' => $teacher->id,
                'is_published' => $request->boolean('is_published', false),
            ]);

            $grade->updateLetterGrade();

            // Load relationships for logging
            $grade->load(['student.user', 'subject']);

            // Log activity
            ActivityLog::logGrade(
                'created',
                $grade,
                "Created grade '{$grade->assignment_name}' for {$grade->student->user->name} in {$grade->subject->name}",
                [
                    'score' => $grade->score,
                    'max_score' => $grade->max_score,
                    'percentage' => $grade->percentage,
                    'is_published' => $grade->is_published,
                ]
            );

            DB::commit();

            return redirect()->route('teacher.grades.index')
                ->with('success', 'Grade created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Failed to create grade. Please try again.');
        }
    }

    /**
     * Display the specified grade
     */
    public function show(Grade $grade)
    {
        $user = Auth::user();
        $teacher = $user->teacher;

        if (!$teacher || $grade->teacher_id !== $teacher->id) {
            abort(403, 'Access denied.');
        }

        $grade->load([
            'student.user',
            'subject',
            'gradeCategory',
            'academicTerm'
        ]);

        return view('teacher.grades.show', compact('grade', 'teacher'));
    }

    /**
     * Show the form for editing the specified grade
     */
    public function edit(Grade $grade)
    {
        $user = Auth::user();
        $teacher = $user->teacher;

        if (!$teacher || $grade->teacher_id !== $teacher->id) {
            abort(403, 'Access denied.');
        }

        $grade->load([
            'student.user',
            'subject',
            'gradeCategory',
            'academicTerm'
        ]);

        $subjects = Subject::orderBy('name')->get();
        $students = Student::with('user')
            ->join('users', 'students.user_id', '=', 'users.id')
            ->orderBy('users.name')
            ->select('students.*')
            ->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::active()->orderBy('name')->get();

        return view('teacher.grades.edit', compact(
            'grade',
            'subjects',
            'students',
            'academicTerms',
            'gradeCategories',
            'teacher'
        ));
    }

    /**
     * Update the specified grade
     */
    public function update(Request $request, Grade $grade)
    {
        $user = Auth::user();
        $teacher = $user->teacher;

        if (!$teacher || $grade->teacher_id !== $teacher->id) {
            abort(403, 'Access denied.');
        }

        $validated = $request->validate([
            'student_id' => 'required|exists:students,id',
            'subject_id' => 'required|exists:subjects,id',
            'grade_category_id' => 'required|exists:grade_categories,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'assignment_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'score' => 'required|numeric|min:0',
            'max_score' => 'required|numeric|min:0.01',
            'assignment_date' => 'required|date',
            'due_date' => 'nullable|date',
            'comments' => 'nullable|string',
            'is_published' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            // Store old data for logging
            $oldData = $grade->toArray();
            $grade->load(['student.user', 'subject']);

            $grade->update([
                ...$validated,
                'is_published' => $request->boolean('is_published', false),
            ]);
            
            $grade->updateLetterGrade();

            // Log activity
            ActivityLog::logGrade(
                'updated',
                $grade,
                "Updated grade '{$grade->assignment_name}' for {$grade->student->user->name} in {$grade->subject->name}",
                [
                    'old_score' => $oldData['score'],
                    'new_score' => $grade->score,
                    'old_percentage' => $oldData['percentage'],
                    'new_percentage' => $grade->percentage,
                    'old_published' => $oldData['is_published'],
                    'new_published' => $grade->is_published,
                ]
            );

            DB::commit();

            return redirect()->route('teacher.grades.index')
                ->with('success', 'Grade updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'Failed to update grade. Please try again.');
        }
    }

    /**
     * Remove the specified grade
     */
    public function destroy(Grade $grade)
    {
        $user = Auth::user();
        $teacher = $user->teacher;

        if (!$teacher || $grade->teacher_id !== $teacher->id) {
            abort(403, 'Access denied.');
        }

        try {
            DB::beginTransaction();

            // Load relationships for logging before deletion
            $grade->load(['student.user', 'subject']);

            // Store grade info for logging
            $gradeInfo = [
                'assignment_name' => $grade->assignment_name,
                'student_name' => $grade->student->user->name,
                'subject_name' => $grade->subject->name,
                'score' => $grade->score,
                'max_score' => $grade->max_score,
                'percentage' => $grade->percentage,
            ];

            $grade->delete();

            // Log activity
            ActivityLog::log(
                'deleted_grade',
                "Deleted grade '{$gradeInfo['assignment_name']}' for {$gradeInfo['student_name']} in {$gradeInfo['subject_name']}",
                'App\Models\Grade',
                null,
                $gradeInfo
            );

            DB::commit();

            return redirect()->route('teacher.grades.index')
                ->with('success', 'Grade deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to delete grade. Please try again.');
        }
    }
}
