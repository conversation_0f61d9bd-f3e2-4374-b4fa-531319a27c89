@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Report Card Details</h1>
                    <p class="mt-2 text-gray-600">{{ $reportCard->student->user->name }} - {{ $reportCard->academicTerm->name }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.report-cards.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Report Cards
                    </a>
                    @if($reportCard->canBeEdited())
                        <a href="{{ route('admin.report-cards.edit', $reportCard) }}" 
                           class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit
                        </a>
                    @endif
                </div>
            </div>
        </div>

        <!-- Report Card Information -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Report Card Information</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Student Details</h4>
                        <div class="space-y-2">
                            <p><span class="font-medium">Name:</span> {{ $reportCard->student->user->name }}</p>
                            <p><span class="font-medium">Student ID:</span> {{ $reportCard->student->student_id }}</p>
                            <p><span class="font-medium">Email:</span> {{ $reportCard->student->user->email }}</p>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Academic Information</h4>
                        <div class="space-y-2">
                            <p><span class="font-medium">Academic Year:</span> {{ $reportCard->academicTerm->academicYear->name }}</p>
                            <p><span class="font-medium">Term:</span> {{ $reportCard->academicTerm->name }}</p>
                            <p><span class="font-medium">Template:</span> {{ $reportCard->template->name ?? 'N/A' }}</p>
                        </div>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Report Details</h4>
                        <div class="space-y-2">
                            <p><span class="font-medium">Report Number:</span> {{ $reportCard->report_number }}</p>
                            <p><span class="font-medium">Status:</span> 
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($reportCard->status === 'draft') bg-gray-100 text-gray-800
                                    @elseif($reportCard->status === 'generated') bg-yellow-100 text-yellow-800
                                    @elseif($reportCard->status === 'published') bg-green-100 text-green-800
                                    @else bg-purple-100 text-purple-800
                                    @endif">
                                    {{ ucfirst($reportCard->status) }}
                                </span>
                            </p>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Academic Performance</h4>
                        <div class="space-y-2">
                            <p><span class="font-medium">Overall GPA:</span> {{ $reportCard->overall_gpa ?? 'N/A' }}</p>
                            <p><span class="font-medium">Overall Grade:</span> {{ $reportCard->overall_grade ?? 'N/A' }}</p>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Timestamps</h4>
                        <div class="space-y-2">
                            <p><span class="font-medium">Generated:</span> {{ $reportCard->generated_at ? $reportCard->generated_at->format('M d, Y H:i') : 'Not generated' }}</p>
                            <p><span class="font-medium">Published:</span> {{ $reportCard->published_at ? $reportCard->published_at->format('M d, Y H:i') : 'Not published' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Grade Data -->
        @if($reportCard->grade_data)
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Academic Performance</h3>
                </div>
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Points</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($reportCard->grade_data as $subjectData)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ $subjectData['subject_name'] ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $subjectData['grade'] ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $subjectData['points'] ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $subjectData['remarks'] ?? '-' }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif

        <!-- Attendance Data -->
        @if($reportCard->attendance_data)
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Attendance Summary</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ $reportCard->attendance_data['total_days'] ?? 0 }}</div>
                            <div class="text-sm text-gray-500">Total Days</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ $reportCard->attendance_data['present_days'] ?? 0 }}</div>
                            <div class="text-sm text-gray-500">Present Days</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-red-600">{{ $reportCard->attendance_data['absent_days'] ?? 0 }}</div>
                            <div class="text-sm text-gray-500">Absent Days</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">{{ $reportCard->attendance_data['attendance_percentage'] ?? 0 }}%</div>
                            <div class="text-sm text-gray-500">Attendance Rate</div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Remarks -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Remarks</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Teacher's Remarks</h4>
                        <div class="bg-gray-50 rounded-md p-3 min-h-[100px]">
                            <p class="text-sm text-gray-700">{{ $reportCard->teacher_remarks ?: 'No remarks provided.' }}</p>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Principal's Remarks</h4>
                        <div class="bg-gray-50 rounded-md p-3 min-h-[100px]">
                            <p class="text-sm text-gray-700">{{ $reportCard->principal_remarks ?: 'No remarks provided.' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4">
                <div class="flex justify-between items-center">
                    <div class="flex space-x-3">
                        @if($reportCard->status === 'generated')
                            <form method="POST" action="{{ route('admin.report-cards.publish', $reportCard) }}" class="inline">
                                @csrf
                                <button type="submit" 
                                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h5v-5H4v5zM13 3H4v5h9V3z"></path>
                                    </svg>
                                    Publish Report Card
                                </button>
                            </form>
                        @endif
                        
                        <a href="{{ route('admin.report-cards.pdf', $reportCard) }}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Download PDF
                        </a>
                    </div>

                    @if($reportCard->status !== 'distributed')
                        <form method="POST" action="{{ route('admin.report-cards.destroy', $reportCard) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this report card? This action cannot be undone.')" 
                              class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Delete
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
