<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class TeacherAttendance extends Model
{
    use HasFactory;

    protected $fillable = [
        'teacher_id',
        'marked_by',
        'date',
        'status',
        'subcategory',
        'notes',
    ];

    protected $casts = [
        'date' => 'date',
    ];

    /**
     * Get the teacher that owns the attendance record
     */
    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    /**
     * Get the user who marked the attendance
     */
    public function markedBy()
    {
        return $this->belongsTo(User::class, 'marked_by');
    }

    /**
     * Scope to filter by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by status
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Get attendance status badge color
     */
    public function getStatusBadgeColorAttribute()
    {
        return match($this->status) {
            'present' => 'green',
            'absent' => 'red',
            default => 'gray'
        };
    }

    /**
     * Get attendance status display text
     */
    public function getStatusDisplayAttribute()
    {
        if ($this->subcategory) {
            return $this->subcategory;
        }

        return match($this->status) {
            'present' => 'Present',
            'absent' => 'Absent',
            default => ucfirst($this->status)
        };
    }

    /**
     * Get available subcategories for teachers
     */
    public static function getSubcategories()
    {
        return [
            'present' => [
                'Outstation'
            ],
            'absent' => [
                'Medical Leave',
                'Annual Leave',
                'Emergency Leave',
                'Other'
            ]
        ];
    }

    /**
     * Get full status display with subcategory
     */
    public function getFullStatusDisplayAttribute()
    {
        $status = ucfirst($this->status);
        if ($this->subcategory) {
            $status .= ' (' . $this->subcategory . ')';
        }
        return $status;
    }

    /**
     * Check if attendance is marked for today
     */
    public static function isMarkedToday($teacherId)
    {
        return self::where('teacher_id', $teacherId)
                   ->where('date', Carbon::today())
                   ->exists();
    }

    /**
     * Get attendance statistics for a teacher
     */
    public static function getTeacherStats($teacherId, $startDate = null, $endDate = null)
    {
        $query = self::where('teacher_id', $teacherId);
        
        if ($startDate && $endDate) {
            $query->whereBetween('date', [$startDate, $endDate]);
        }

        $attendances = $query->get();
        $total = $attendances->count();

        if ($total === 0) {
            return [
                'total_days' => 0,
                'present' => 0,
                'absent' => 0,
                'late' => 0,
                'sick_leave' => 0,
                'personal_leave' => 0,
                'official_duty' => 0,
                'attendance_percentage' => 0,
                'total_hours' => 0,
                'average_hours' => 0
            ];
        }

        $stats = [
            'total_days' => $total,
            'present' => $attendances->where('status', 'present')->count(),
            'absent' => $attendances->where('status', 'absent')->count(),
            'late' => $attendances->where('status', 'late')->count(),
            'sick_leave' => $attendances->where('status', 'sick_leave')->count(),
            'personal_leave' => $attendances->where('status', 'personal_leave')->count(),
            'official_duty' => $attendances->where('status', 'official_duty')->count(),
        ];

        // Calculate attendance percentage (present + late + official_duty as attended)
        $attended = $stats['present'] + $stats['late'] + $stats['official_duty'];
        $stats['attendance_percentage'] = $total > 0 ? round(($attended / $total) * 100, 2) : 0;

        // Calculate hours worked
        $stats['total_hours'] = $attendances->sum('hours_worked') ?? 0;
        $stats['average_hours'] = $total > 0 ? round($stats['total_hours'] / $total, 2) : 0;

        return $stats;
    }

    /**
     * Calculate hours worked based on check-in and check-out times
     */
    public function calculateHoursWorked()
    {
        if ($this->check_in_time && $this->check_out_time) {
            $checkIn = Carbon::parse($this->check_in_time);
            $checkOut = Carbon::parse($this->check_out_time);
            
            if ($checkOut->greaterThan($checkIn)) {
                $this->hours_worked = $checkOut->diffInHours($checkIn, true);
                $this->save();
            }
        }
    }

    /**
     * Get overall teacher attendance statistics
     */
    public static function getOverallStats($startDate = null, $endDate = null)
    {
        $query = self::query();

        if ($startDate && $endDate) {
            $query->whereBetween('date', [$startDate, $endDate]);
        }

        $attendances = $query->get();
        $total = $attendances->count();

        if ($total === 0) {
            return [
                'total_records' => 0,
                'present' => 0,
                'absent' => 0,
                'late' => 0,
                'excused' => 0,
                'sick' => 0,
                'official_duty' => 0,
                'attendance_percentage' => 0
            ];
        }

        $stats = [
            'total_records' => $total,
            'present' => $attendances->where('status', 'present')->count(),
            'absent' => $attendances->where('status', 'absent')->count(),
            'late' => $attendances->where('status', 'late')->count(),
            'excused' => $attendances->where('status', 'excused')->count(),
            'sick' => $attendances->where('status', 'sick')->count(),
            'official_duty' => $attendances->where('status', 'official_duty')->count(),
        ];

        // Calculate attendance percentage (only present counts as attended)
        $stats['attendance_percentage'] = $total > 0 ? round(($stats['present'] / $total) * 100, 2) : 0;

        return $stats;
    }
}
