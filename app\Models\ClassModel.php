<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClassModel extends Model
{
    use HasFactory;

    protected $table = 'classes';

    protected $fillable = [
        'name',
        'level',
        'description',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the sections for this class
     */
    public function sections()
    {
        return $this->hasMany(Section::class, 'class_id');
    }

    /**
     * Get the active sections for this class
     */
    public function activeSections()
    {
        return $this->sections()->where('is_active', true);
    }

    /**
     * Get the subjects for this class
     */
    public function subjects()
    {
        return $this->belongsToMany(Subject::class, 'class_subjects', 'class_id', 'subject_id')
                    ->withPivot('is_mandatory', 'hours_per_week')
                    ->withTimestamps();
    }

    /**
     * Get the students in this class through enrollments
     */
    public function students()
    {
        return $this->belongsToMany(Student::class, 'student_enrollments', 'class_id', 'student_id')
                    ->withPivot('section_id', 'enrollment_date', 'end_date', 'is_active')
                    ->withTimestamps();
    }

    /**
     * Get active students in this class
     */
    public function activeStudents()
    {
        return $this->students()->wherePivot('is_active', true);
    }

    /**
     * Get the teachers assigned to this class
     */
    public function teachers()
    {
        return $this->belongsToMany(Teacher::class, 'teacher_subjects', 'class_id', 'teacher_id')
                    ->withPivot('subject_id', 'section_id', 'is_primary')
                    ->withTimestamps()
                    ->distinct();
    }

    /**
     * Scope to filter active classes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by level
     */
    public function scopeLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Scope to order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the status badge color
     */
    public function getStatusBadgeColorAttribute()
    {
        return $this->is_active ? 'green' : 'red';
    }

    /**
     * Get the status display text
     */
    public function getStatusDisplayAttribute()
    {
        return $this->is_active ? 'Active' : 'Inactive';
    }

    /**
     * Get the level badge color
     */
    public function getLevelBadgeColorAttribute()
    {
        return match($this->level) {
            'Primary' => 'blue',
            'Secondary' => 'green',
            'Pre-school' => 'yellow',
            default => 'gray'
        };
    }

    /**
     * Get total students count
     */
    public function getTotalStudentsAttribute()
    {
        return $this->students()->count();
    }

    /**
     * Get total sections count
     */
    public function getTotalSectionsAttribute()
    {
        return $this->sections()->count();
    }

    /**
     * Get total subjects count
     */
    public function getTotalSubjectsAttribute()
    {
        return $this->subjects()->count();
    }
}
