@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Academic Term Details"
        description="View academic term information and related schedules"
        :back-route="route('admin.academic-terms.index')"
        back-label="Back to Academic Terms">
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.academic-terms.edit', $academicTerm) }}" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Academic Term
            </a>
            @if(!$academicTerm->is_current)
                <form method="POST" action="{{ route('admin.academic-terms.set-current', $academicTerm) }}" class="inline">
                    @csrf
                    <button type="submit" class="btn-secondary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Set as Current
                    </button>
                </form>
            @endif
        </div>
    </x-page-header>

    <!-- Academic Term Details -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Academic Term Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Term Name</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $academicTerm->name }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Academic Year</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $academicTerm->academicYear->name }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Status</label>
                    <div class="mt-1">
                        @if($academicTerm->is_current)
                            <span class="badge badge-green">Current</span>
                        @endif
                        <span class="badge {{ $academicTerm->is_active ? 'badge-blue' : 'badge-gray' }}">
                            {{ $academicTerm->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Sort Order</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $academicTerm->sort_order }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Start Date</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $academicTerm->start_date->format('F d, Y') }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">End Date</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $academicTerm->end_date->format('F d, Y') }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Duration</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $academicTerm->start_date->diffInDays($academicTerm->end_date) }} days</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Created</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $academicTerm->created_at->format('F d, Y g:i A') }}</p>
                </div>
            </div>
            
            @if($academicTerm->description)
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700">Description</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $academicTerm->description }}</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Related Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Schedules -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Schedules</h3>
                    <a href="{{ route('admin.schedules.index', ['academic_term_id' => $academicTerm->id]) }}" class="text-sm text-blue-600 hover:text-blue-800">View all</a>
                </div>
            </div>
            <div class="p-6">
                @if($academicTerm->schedules && $academicTerm->schedules->count() > 0)
                    <div class="space-y-3">
                        @foreach($academicTerm->schedules->take(5) as $schedule)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ $schedule->subject->name }}</p>
                                    <p class="text-xs text-gray-600">{{ $schedule->class->name }} - {{ $schedule->teacher->name }}</p>
                                </div>
                                <div class="text-xs text-gray-500">
                                    {{ ucfirst($schedule->day_of_week) }}
                                </div>
                            </div>
                        @endforeach
                        @if($academicTerm->schedules->count() > 5)
                            <p class="text-sm text-gray-500 text-center">
                                +{{ $academicTerm->schedules->count() - 5 }} more schedules
                            </p>
                        @endif
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <p class="text-sm text-gray-500 mt-2">No schedules found</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Statistics -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Statistics</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Total Schedules</span>
                        <span class="text-sm text-gray-900">{{ $academicTerm->schedules ? $academicTerm->schedules->count() : 0 }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Unique Classes</span>
                        <span class="text-sm text-gray-900">{{ $academicTerm->schedules ? $academicTerm->schedules->pluck('class_id')->unique()->count() : 0 }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Unique Subjects</span>
                        <span class="text-sm text-gray-900">{{ $academicTerm->schedules ? $academicTerm->schedules->pluck('subject_id')->unique()->count() : 0 }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Unique Teachers</span>
                        <span class="text-sm text-gray-900">{{ $academicTerm->schedules ? $academicTerm->schedules->pluck('teacher_id')->unique()->count() : 0 }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Days Until Start</span>
                        <span class="text-sm text-gray-900">
                            @if($academicTerm->start_date->isFuture())
                                {{ now()->diffInDays($academicTerm->start_date) }} days
                            @elseif($academicTerm->start_date->isPast() && $academicTerm->end_date->isFuture())
                                <span class="text-green-600">In Progress</span>
                            @else
                                <span class="text-gray-500">Completed</span>
                            @endif
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700">Days Until End</span>
                        <span class="text-sm text-gray-900">
                            @if($academicTerm->end_date->isFuture())
                                {{ now()->diffInDays($academicTerm->end_date) }} days
                            @else
                                <span class="text-gray-500">Ended</span>
                            @endif
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
