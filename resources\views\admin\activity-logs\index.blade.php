@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Activity Logs"
        description="Monitor all user activities and system events">
        
        <div class="flex items-center space-x-3">
            <button onclick="showExportModal()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export
            </button>
            
            <button onclick="showClearModal()" class="inline-flex items-center bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 cursor-pointer">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Clear Old Logs
            </button>
        </div>
    </x-page-header>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Activities</dt>
                            <dd class="stat-card-value">{{ number_format($stats['total_activities']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Today</dt>
                            <dd class="stat-card-value">{{ number_format($stats['today_activities']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">This Week</dt>
                            <dd class="stat-card-value">{{ number_format($stats['this_week_activities']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">This Month</dt>
                            <dd class="stat-card-value">{{ number_format($stats['this_month_activities']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6"
         x-data="{
             searchQuery: '{{ $search }}',
             action: '{{ $action }}',
             userId: '{{ $userId }}',
             startDate: '{{ $startDate }}',
             endDate: '{{ $endDate }}',
             showAdvanced: {{ ($action || $userId || $startDate || $endDate) ? 'true' : 'false' }}
         }">

        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
            <!-- Search Input -->
            <div class="flex-1">
                <input
                    type="text"
                    id="search"
                    value="{{ $search }}"
                    placeholder="Search activities by description, user name, action..."
                    class="form-input w-full"
                >
            </div>

            <!-- Filter Toggle and Clear Buttons -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                </button>

                <button
                    onclick="clearFilters()"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="action" class="block text-sm font-medium text-gray-700 mb-1">Action</label>
                <select id="action" class="form-select w-full">
                    <option value="">All Actions</option>
                    @foreach($actions as $actionOption)
                        <option value="{{ $actionOption['value'] }}" {{ $action === $actionOption['value'] ? 'selected' : '' }}>
                            {{ $actionOption['label'] }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div>
                <label for="user_id" class="block text-sm font-medium text-gray-700 mb-1">User</label>
                <select id="user_id" class="form-select w-full">
                    <option value="">All Users</option>
                    @foreach($users as $user)
                        <option value="{{ $user->id }}" {{ $userId == $user->id ? 'selected' : '' }}>
                            {{ $user->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                <input type="date" id="start_date" value="{{ $startDate }}"
                       class="form-input w-full">
            </div>

            <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                <input type="date" id="end_date" value="{{ $endDate }}"
                       class="form-input w-full">
            </div>
        </div>
    </div>

    <!-- Activity Logs Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Activity Logs</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-500">Show:</span>
                    <select id="per_page" class="form-select text-sm w-20">
                        <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100</option>
                    </select>
                    <span class="text-sm text-gray-500">per page</span>
                </div>
            </div>
        </div>

        @if($logs->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date/Time</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Affected Users</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($logs as $log)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $log->created_at->format('M d, Y H:i:s') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8">
                                            <div class="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                                                <span class="text-xs font-medium text-gray-600">
                                                    {{ substr($log->user_name, 0, 2) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">{{ $log->user_name }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="badge badge-{{ $log->action_badge_color }}">
                                        {{ $log->formatted_action }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <div class="max-w-xs truncate" title="{{ $log->description }}">
                                        {{ $log->description }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    @if($log->properties && isset($log->properties['names']) && is_array($log->properties['names']))
                                        <div class="flex flex-wrap gap-1">
                                            @foreach(array_slice($log->properties['names'], 0, 3) as $name)
                                                <span class="badge badge-blue">
                                                    {{ $name }}
                                                </span>
                                            @endforeach
                                            @if(count($log->properties['names']) > 3)
                                                <button onclick="showLogDetails({{ $log->id }})" class="badge badge-gray hover:bg-gray-200 transition-colors duration-200 cursor-pointer" title="Click to view all affected users">
                                                    +{{ count($log->properties['names']) - 3 }} more
                                                </button>
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-gray-400">-</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <button onclick="showLogDetails({{ $log->id }})"
                                               class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition-colors duration-200 cursor-pointer"
                                               title="View Log Details">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            View
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $logs->appends(request()->query())->links() }}
            </div>
        @else
            <div class="px-6 py-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No activity logs found</h3>
                <p class="mt-1 text-sm text-gray-500">No activities match your current filters.</p>
            </div>
        @endif
    </div>
</div>

<!-- Log Details Modal -->
<div id="logDetailsModal" class="fixed inset-0 overflow-y-auto h-full w-full hidden z-50" style="background-color: rgba(0, 0, 0, 0.6);">
    <div class="relative top-20 mx-auto p-0" style="width: 600px; max-width: 90vw;">
        <div class="bg-white rounded-lg shadow-xl">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Activity Log Details</h3>
                    <button onclick="closeLogDetailsModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="px-6 py-4">
                <div id="logDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 overflow-y-auto h-full w-full hidden z-50" style="background-color: rgba(17, 24, 39, 0.6);">
    <div class="relative top-20 mx-auto p-6 w-96 shadow-xl rounded-lg bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Export Activity Logs</h3>
            <form id="exportForm" action="{{ route('admin.activity-logs.export') }}" method="GET">
                <input type="hidden" name="search" value="{{ $search }}">
                <input type="hidden" name="action" value="{{ $action }}">
                <input type="hidden" name="user_id" value="{{ $userId }}">
                <input type="hidden" name="start_date" value="{{ $startDate }}">
                <input type="hidden" name="end_date" value="{{ $endDate }}">

                <p class="text-sm text-gray-600 mb-4">
                    This will export all activity logs matching your current filters to a CSV file.
                </p>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeExportModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Export CSV</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Clear Logs Modal -->
<div id="clearModal" class="fixed inset-0 overflow-y-auto h-full w-full hidden z-50" style="background-color: rgba(17, 24, 39, 0.6);">
    <div class="relative top-20 mx-auto p-6 w-96 shadow-xl rounded-lg bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Clear Old Activity Logs</h3>
            <form id="clearForm" action="{{ route('admin.activity-logs.clear') }}" method="POST">
                @csrf

                <div class="mb-4">
                    <label for="days" class="block text-sm font-medium text-gray-700 mb-2">
                        Delete logs older than (days):
                    </label>
                    <input type="number" id="days" name="days" min="1" max="365" value="90" required
                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <p class="text-xs text-gray-500 mt-1">
                        This action cannot be undone. Recommended: 90 days or more.
                    </p>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeClearModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                        Clear Logs
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Live filtering functionality
function applyFilters() {
    const search = document.getElementById('search').value;
    const action = document.getElementById('action').value;
    const userId = document.getElementById('user_id').value;
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const perPage = document.getElementById('per_page').value;

    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (action) params.append('action', action);
    if (userId) params.append('user_id', userId);
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (perPage) params.append('per_page', perPage);

    window.location.href = '{{ route("admin.activity-logs.index") }}?' + params.toString();
}

function clearFilters() {
    window.location.href = '{{ route("admin.activity-logs.index") }}';
}

// Modal functions
function showLogDetails(logId) {
    fetch(`{{ route('admin.activity-logs.index') }}/${logId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const log = data.log;
                const content = `
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Date/Time</label>
                                <p class="text-sm text-gray-900 font-mono">${new Date(log.created_at).toLocaleString()}</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <label class="block text-sm font-medium text-gray-700 mb-1">User</label>
                                <p class="text-sm text-gray-900">${log.user_name}</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Action</label>
                                <span class="badge badge-blue">
                                    ${log.formatted_action}
                                </span>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <label class="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
                                <p class="text-sm text-gray-900 font-mono">${log.ip_address || 'N/A'}</p>
                            </div>
                            ${log.model_name ? `
                                <div class="bg-gray-50 p-3 rounded-lg md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Model Type</label>
                                    <p class="text-sm text-gray-900">${log.model_name}</p>
                                </div>
                            ` : ''}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <div class="bg-blue-50 border border-blue-200 p-3 rounded-lg">
                                <p class="text-sm text-gray-900">${log.description}</p>
                            </div>
                        </div>
                        ${log.properties && log.properties.names && log.properties.names.length > 0 ? `
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    ${log.properties.type === 'student' ? 'Students' : 'Teachers'} Affected
                                </label>
                                <div class="bg-gray-50 p-3 rounded-lg">
                                    <div class="flex flex-wrap gap-1">
                                        ${log.properties.names.map(name => `
                                            <span class="badge badge-green">
                                                ${name}
                                            </span>
                                        `).join('')}
                                    </div>
                                    ${log.properties.count > 1 ? `
                                        <p class="text-xs text-gray-600 mt-2">Total: ${log.properties.count} ${log.properties.type}(s)</p>
                                    ` : ''}
                                </div>
                            </div>
                        ` : ''}
                        ${log.properties && log.properties.notes ? `
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                                <div class="bg-yellow-50 p-3 rounded-lg">
                                    <p class="text-sm text-gray-900">${log.properties.notes}</p>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
                document.getElementById('logDetailsContent').innerHTML = content;
                document.getElementById('logDetailsModal').classList.remove('hidden');
            }
        })
        .catch(error => {
            showToast('Error loading log details', 'error');
        });
}

function closeLogDetailsModal() {
    document.getElementById('logDetailsModal').classList.add('hidden');
}

function showExportModal() {
    document.getElementById('exportModal').classList.remove('hidden');
}

function closeExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
}

function showClearModal() {
    document.getElementById('clearModal').classList.remove('hidden');
}

function closeClearModal() {
    document.getElementById('clearModal').classList.add('hidden');
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    const actionSelect = document.getElementById('action');
    const userSelect = document.getElementById('user_id');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const perPageSelect = document.getElementById('per_page');

    let searchTimeout;

    // Search with debounce
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(applyFilters, 500);
    });

    // Other filters change instantly
    actionSelect.addEventListener('change', applyFilters);
    userSelect.addEventListener('change', applyFilters);
    startDateInput.addEventListener('change', applyFilters);
    endDateInput.addEventListener('change', applyFilters);
    perPageSelect.addEventListener('change', applyFilters);

    // Close modals when clicking outside
    document.getElementById('logDetailsModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeLogDetailsModal();
        }
    });

    document.getElementById('exportModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeExportModal();
        }
    });

    document.getElementById('clearModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeClearModal();
        }
    });
});
</script>
@endpush
@endsection
