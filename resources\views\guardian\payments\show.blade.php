@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header with <PERSON> Button -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Payment Details</h1>
                <p class="text-gray-600">View payment information and status</p>
            </div>
            <a href="{{ route('guardian.payments.index') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Payments
            </a>
        </div>
    </div>

    <!-- Payment Status -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-medium text-gray-900">Payment Status</h2>
            <div class="flex items-center">
                @if($payment->status === 'paid')
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        Paid
                    </span>
                @elseif($payment->status === 'pending')
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        Pending
                    </span>
                @elseif($payment->status === 'failed')
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                        Failed
                    </span>
                @else
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                        {{ ucfirst($payment->status) }}
                    </span>
                @endif
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Payment Reference</h3>
                <p class="text-lg font-medium text-gray-900">{{ $payment->payment_reference }}</p>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Amount</h3>
                <p class="text-lg font-medium text-gray-900">RM {{ number_format($payment->amount, 2) }}</p>
            </div>
            @if($payment->payment_method)
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Payment Method</h3>
                <p class="text-lg font-medium text-gray-900">{{ strtoupper($payment->payment_method) }}</p>
            </div>
            @endif
            @if($payment->paid_at)
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Paid At</h3>
                <p class="text-lg font-medium text-gray-900">{{ $payment->paid_at->format('d M Y, h:i A') }}</p>
            </div>
            @endif
            @if($payment->billplz_transaction_id)
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Transaction ID</h3>
                <p class="text-lg font-medium text-gray-900">{{ $payment->billplz_transaction_id }}</p>
            </div>
            @endif
        </div>
    </div>

    <!-- Invoice Information -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Invoice Information</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Invoice Number</h3>
                <p class="text-lg font-medium text-gray-900">{{ $payment->invoice->invoice_number }}</p>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Student</h3>
                <p class="text-lg font-medium text-gray-900">{{ $payment->invoice->student->user->name }}</p>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Invoice Title</h3>
                <p class="text-lg font-medium text-gray-900">{{ $payment->invoice->title }}</p>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Due Date</h3>
                <p class="text-lg font-medium text-gray-900">{{ $payment->invoice->due_date->format('d M Y') }}</p>
            </div>
        </div>

        @if($payment->invoice->description)
        <div class="mt-6">
            <h3 class="text-sm font-medium text-gray-500 mb-2">Description</h3>
            <p class="text-gray-900">{{ $payment->invoice->description }}</p>
        </div>
        @endif
    </div>

    <!-- Actions -->
    @if($payment->status === 'paid')
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Actions</h2>
        
        <div class="flex space-x-4">
            <a href="{{ route('guardian.payments.receipt', $payment) }}" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Download Receipt
            </a>
            <a href="{{ route('guardian.invoices.show', $payment->invoice) }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                View Invoice
            </a>
        </div>
    </div>
    @endif

    @if($billplzInfo && isset($billplzInfo['url']))
    <!-- Billplz Information -->
    <div class="bg-white shadow rounded-lg p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Payment Gateway Information</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Billplz Bill ID</h3>
                <p class="text-lg font-medium text-gray-900">{{ $payment->billplz_bill_id }}</p>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">Gateway Status</h3>
                <p class="text-lg font-medium text-gray-900">{{ $billplzInfo['state'] ?? 'Unknown' }}</p>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
