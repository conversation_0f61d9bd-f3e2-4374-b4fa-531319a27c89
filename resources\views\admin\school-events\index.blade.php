@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="School Events Management"
        description="Manage school events, holidays, and activities"
        :back-route="route('admin.academic-calendar.index')"
        back-label="Back to Academic Calendar">
        <a href="{{ route('admin.school-events.create') }}" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add School Event
        </a>
    </x-page-header>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6" x-data="{
        searchQuery: '',
        type: '',
        scope: '',
        showAdvanced: false
    }">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            <!-- Search Input -->
            <div class="flex-1">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input
                        type="text"
                        x-model="searchQuery"
                        @input="filterEvents()"
                        class="search-input"
                        placeholder="Search events by title, description, location..."
                    >
                </div>
            </div>

            <!-- Filter Toggle and Clear Buttons -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                    <svg class="w-4 h-4 ml-1 transition-transform duration-200" :class="showAdvanced ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <!-- Clear All Filters -->
                <button
                    @click="clearEventFilters()"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 transform translate-y-0" x-transition:leave-end="opacity-0 transform -translate-y-2" class="mt-4 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Type Filter -->
                <div>
                    <label for="type-filter" class="block text-sm font-medium text-gray-700 mb-1">Event Type</label>
                    <select
                        id="type-filter"
                        x-model="type"
                        @change="filterEvents()"
                        class="form-select"
                    >
                        <option value="">All Types</option>
                        <option value="holiday">Holiday</option>
                        <option value="exam">Exam</option>
                        <option value="sports">Sports</option>
                        <option value="cultural">Cultural</option>
                        <option value="meeting">Meeting</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <!-- Scope Filter -->
                <div>
                    <label for="scope-filter" class="block text-sm font-medium text-gray-700 mb-1">Scope</label>
                    <select
                        id="scope-filter"
                        x-model="scope"
                        @change="filterEvents()"
                        class="form-select"
                    >
                        <option value="">All Scopes</option>
                        <option value="all">All</option>
                        <option value="class">Class</option>
                        <option value="section">Section</option>
                        <option value="teachers">Teachers</option>
                        <option value="parents">Parents</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Events</dt>
                            <dd class="stat-card-value">{{ $events->total() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Upcoming</dt>
                            <dd class="stat-card-value">{{ $events->where('event_date', '>=', now()->toDateString())->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">This Month</dt>
                            <dd class="stat-card-value">{{ $events->whereBetween('event_date', [now()->startOfMonth(), now()->endOfMonth()])->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Active</dt>
                            <dd class="stat-card-value">{{ $events->where('is_active', true)->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- School Events List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">School Events</h3>
                <span class="text-sm text-gray-500">
                    <span data-results-count>{{ $events->count() }}</span> events found
                </span>
            </div>
        </div>
        <div class="p-6">
            @if($events->count() > 0)
                <div class="space-y-4">
                    @foreach($events as $event)
                        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200"
                             data-filterable
                             data-search-text="{{ $event->title }} {{ $event->description ?? '' }} {{ $event->location ?? '' }} {{ $event->type }} {{ $event->scope }}"
                             data-type="{{ $event->type }}"
                             data-scope="{{ $event->scope }}">

                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3">
                                        <h4 class="text-lg font-medium text-gray-900">{{ $event->title }}</h4>
                                        <span class="badge badge-purple">
                                            {{ ucfirst($event->type) }}
                                        </span>
                                        <span class="badge badge-blue">
                                            {{ ucfirst($event->scope) }}
                                        </span>
                                        @if(!$event->is_active)
                                            <span class="badge badge-gray">Inactive</span>
                                        @endif
                                    </div>

                                    @if($event->description)
                                        <p class="text-sm text-gray-600 mt-2">{{ Str::limit($event->description, 150) }}</p>
                                    @endif

                                    <div class="mt-3 grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600">
                                        <div>
                                            <span class="font-medium">Date:</span>
                                            <br>{{ $event->event_date->format('M d, Y') }}
                                            @if($event->event_date->isToday())
                                                <span class="text-green-600 font-medium">(Today)</span>
                                            @elseif($event->event_date->isTomorrow())
                                                <span class="text-blue-600 font-medium">(Tomorrow)</span>
                                            @elseif($event->event_date->isPast())
                                                <span class="text-gray-500">(Past)</span>
                                            @endif
                                        </div>
                                        @if($event->start_time && $event->end_time)
                                            <div>
                                                <span class="font-medium">Time:</span>
                                                <br>{{ $event->start_time->format('g:i A') }} - {{ $event->end_time->format('g:i A') }}
                                            </div>
                                        @endif
                                        @if($event->location)
                                            <div>
                                                <span class="font-medium">Location:</span>
                                                <br>{{ $event->location }}
                                            </div>
                                        @endif
                                        <div>
                                            <span class="font-medium">Created by:</span>
                                            <br>{{ $event->creator->name ?? 'Unknown' }}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('admin.school-events.show', $event) }}"
                                       class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        View
                                    </a>
                                    <a href="{{ route('admin.school-events.edit', $event) }}"
                                       class="inline-flex items-center px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        Edit
                                    </a>
                                    <button type="button"
                                            onclick="deleteEvent('{{ $event->id }}', '{{ $event->title }}')"
                                            class="inline-flex items-center px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    {{ $events->links() }}
                </div>
            @else
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No school events</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating your first school event.</p>
                    <div class="mt-6">
                        <a href="{{ route('admin.school-events.create') }}" class="btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add School Event
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Store the base route URL for JavaScript use
const deleteRouteBase = '{{ route('admin.school-events.index') }}';

async function deleteEvent(eventId, eventTitle) {
    const confirmed = await confirmAction('Delete Event', `Are you sure you want to delete the event "${eventTitle}"? This action cannot be undone.`);

    if (confirmed) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `${deleteRouteBase}/${eventId}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // Add method override
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        document.body.appendChild(form);
        form.submit();
    }
}

// Live filtering functions for events
function filterEvents() {
    const searchQuery = document.querySelector('[x-model="searchQuery"]').value.toLowerCase();
    const typeFilter = document.querySelector('[x-model="type"]').value;
    const scopeFilter = document.querySelector('[x-model="scope"]').value;

    const events = document.querySelectorAll('[data-filterable]');
    let visibleCount = 0;

    events.forEach(event => {
        const searchText = event.getAttribute('data-search-text').toLowerCase();
        const eventType = event.getAttribute('data-type');
        const eventScope = event.getAttribute('data-scope');

        let isVisible = true;

        // Search filter
        if (searchQuery && !searchText.includes(searchQuery)) {
            isVisible = false;
        }

        // Type filter
        if (typeFilter && eventType !== typeFilter) {
            isVisible = false;
        }

        // Scope filter
        if (scopeFilter && eventScope !== scopeFilter) {
            isVisible = false;
        }

        // Show/hide event
        event.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = visibleCount;
    }
}

// Clear all filters
function clearEventFilters() {
    // Reset form inputs and trigger events
    const searchInput = document.querySelector('[x-model="searchQuery"]');
    const typeInput = document.querySelector('[x-model="type"]');
    const scopeInput = document.querySelector('[x-model="scope"]');

    if (searchInput) {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    }
    if (typeInput) {
        typeInput.value = '';
        typeInput.dispatchEvent(new Event('change'));
    }
    if (scopeInput) {
        scopeInput.value = '';
        scopeInput.dispatchEvent(new Event('change'));
    }

    // Show all events
    const events = document.querySelectorAll('[data-filterable]');
    events.forEach(event => {
        event.style.display = '';
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = events.length;
    }
}
</script>
@endpush
