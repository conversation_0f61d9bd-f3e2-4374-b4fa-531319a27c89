<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExamEnrollment extends Model
{
    use HasFactory;

    protected $fillable = [
        'exam_id',
        'student_id',
        'status',
        'notes',
        'enrolled_at',
    ];

    protected $casts = [
        'enrolled_at' => 'datetime',
    ];

    /**
     * Get the exam.
     */
    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    /**
     * Get the student.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Check if student is enrolled and active.
     */
    public function isActive(): bool
    {
        return $this->status === 'enrolled';
    }

    /**
     * Bulk enroll students for an exam.
     */
    public static function bulkEnroll($examId, $studentIds)
    {
        $enrollments = [];
        foreach ($studentIds as $studentId) {
            $enrollments[] = [
                'exam_id' => $examId,
                'student_id' => $studentId,
                'status' => 'enrolled',
                'enrolled_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        return static::insert($enrollments);
    }
}
