<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeacherSubject extends Model
{
    use HasFactory;

    protected $fillable = [
        'teacher_id',
        'subject_id',
        'class_id',
        'section_id',
        'is_primary',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
    ];

    /**
     * Get the teacher for this assignment
     */
    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    /**
     * Get the subject for this assignment
     */
    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the class for this assignment
     */
    public function class()
    {
        return $this->belongsTo(ClassModel::class, 'class_id');
    }

    /**
     * Get the section for this assignment
     */
    public function section()
    {
        return $this->belongsTo(Section::class);
    }

    /**
     * Scope to filter primary teachers
     */
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    /**
     * Get students for this teaching assignment
     */
    public function students()
    {
        $query = Student::whereHas('currentEnrollment', function ($q) {
            $q->where('class_id', $this->class_id);
            
            if ($this->section_id) {
                $q->where('section_id', $this->section_id);
            }
        });

        return $query;
    }
}
