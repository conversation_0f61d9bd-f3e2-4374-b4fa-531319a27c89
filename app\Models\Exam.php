<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Exam extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'exam_type_id',
        'academic_term_id',
        'start_date',
        'end_date',
        'status',
        'instructions',
        'is_published',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'instructions' => 'array',
        'is_published' => 'boolean',
    ];

    /**
     * Get the exam type.
     */
    public function examType(): BelongsTo
    {
        return $this->belongsTo(ExamType::class);
    }

    /**
     * Get the academic term.
     */
    public function academicTerm(): BelongsTo
    {
        return $this->belongsTo(AcademicTerm::class);
    }

    /**
     * Get the creator.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the exam subjects.
     */
    public function examSubjects(): HasMany
    {
        return $this->hasMany(ExamSubject::class);
    }

    /**
     * Get the exam enrollments.
     */
    public function enrollments(): HasMany
    {
        return $this->hasMany(ExamEnrollment::class);
    }

    /**
     * Get the students enrolled in this exam.
     */
    public function students(): BelongsToMany
    {
        return $this->belongsToMany(Student::class, 'exam_enrollments')
                    ->withPivot('status', 'notes', 'enrolled_at')
                    ->withTimestamps();
    }

    /**
     * Get upcoming exams.
     */
    public static function upcoming($limit = 10)
    {
        return static::where('start_date', '>=', now()->toDateString())
                    ->where('status', '!=', 'cancelled')
                    ->orderBy('start_date')
                    ->limit($limit);
    }

    /**
     * Get ongoing exams.
     */
    public static function ongoing()
    {
        return static::where('start_date', '<=', now()->toDateString())
                    ->where('end_date', '>=', now()->toDateString())
                    ->where('status', 'ongoing');
    }

    /**
     * Check if exam is active.
     */
    public function isActive(): bool
    {
        return in_array($this->status, ['scheduled', 'ongoing']);
    }

    /**
     * Check if exam can be edited.
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, ['draft', 'scheduled']);
    }

    /**
     * Get exam duration in days.
     */
    public function getDurationAttribute(): int
    {
        return $this->start_date->diffInDays($this->end_date) + 1;
    }
}
