<?php

namespace App\Http\Controllers\Guardian;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Payment;
use App\Services\BillplzService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class InvoiceController extends Controller
{
    protected $billplzService;

    public function __construct(BillplzService $billplzService)
    {
        $this->billplzService = $billplzService;
    }

    /**
     * Display a listing of invoices for guardian's children
     */
    public function index(Request $request)
    {
        $guardian = auth()->user()->guardian;
        
        if (!$guardian) {
            return redirect()->route('guardian.dashboard')
                            ->with('error', 'Guardian profile not found.');
        }

        $studentIds = $guardian->students->pluck('id');
        
        $query = Invoice::with(['student.user', 'payments'])
                       ->whereIn('student_id', $studentIds);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by student
        if ($request->filled('student_id')) {
            $query->where('student_id', $request->student_id);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Search by invoice number or title
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%");
            });
        }

        $invoices = $query->latest()->paginate(15);

        $stats = [
            'total' => Invoice::whereIn('student_id', $studentIds)->count(),
            'pending' => Invoice::whereIn('student_id', $studentIds)->where('status', 'sent')->count(),
            'paid' => Invoice::whereIn('student_id', $studentIds)->where('status', 'paid')->count(),
            'overdue' => Invoice::whereIn('student_id', $studentIds)->where('status', 'overdue')->count(),
            'total_amount_due' => Invoice::whereIn('student_id', $studentIds)
                                        ->where('status', 'sent')
                                        ->sum('total_amount'),
        ];

        $students = $guardian->students;

        return view('guardian.invoices.index', compact('invoices', 'stats', 'students'));
    }

    /**
     * Display the specified invoice
     */
    public function show(Invoice $invoice)
    {
        $guardian = auth()->user()->guardian;
        
        if (!$guardian) {
            return redirect()->route('guardian.dashboard')
                            ->with('error', 'Guardian profile not found.');
        }

        // Check if this invoice belongs to guardian's children
        $studentIds = $guardian->students->pluck('id');
        if (!$studentIds->contains($invoice->student_id)) {
            abort(403, 'Unauthorized access to this invoice.');
        }

        $invoice->load(['student.user', 'creator', 'payments']);

        return view('guardian.invoices.show', compact('invoice'));
    }

    /**
     * Initiate payment for an invoice
     */
    public function pay(Request $request, Invoice $invoice)
    {
        $guardian = auth()->user()->guardian;
        
        if (!$guardian) {
            return redirect()->route('guardian.dashboard')
                            ->with('error', 'Guardian profile not found.');
        }

        // Check if this invoice belongs to guardian's children
        $studentIds = $guardian->students->pluck('id');
        if (!$studentIds->contains($invoice->student_id)) {
            abort(403, 'Unauthorized access to this invoice.');
        }

        // Check if invoice can be paid
        if (!in_array($invoice->status, ['sent', 'overdue'])) {
            return redirect()->route('guardian.invoices.show', $invoice)
                            ->with('error', 'This invoice cannot be paid.');
        }

        // Check if invoice is already fully paid
        $totalPaid = $invoice->payments()->where('status', 'paid')->sum('amount');
        if ($totalPaid >= $invoice->total_amount) {
            return redirect()->route('guardian.invoices.show', $invoice)
                            ->with('error', 'This invoice is already fully paid.');
        }

        $request->validate([
            'amount' => 'required|numeric|min:0.01|max:' . ($invoice->total_amount - $totalPaid),
        ]);

        try {
            // Create payment record
            $payment = Payment::create([
                'invoice_id' => $invoice->id,
                'paid_by' => auth()->id(),
                'payment_reference' => $this->generatePaymentReference(),
                'amount' => $request->amount,
                'status' => 'pending',
            ]);

            // Create Billplz bill
            $billData = [
                'email' => auth()->user()->email,
                'mobile' => auth()->user()->phone ?? null,
                'name' => auth()->user()->name,
                'amount' => $request->amount,
                'description' => $invoice->title . ' - ' . $invoice->student->user->name,
                'callback_url' => route('billplz.webhook'),
                'redirect_url' => route('payment.return', $payment),
                'invoice_id' => $invoice->id,
            ];

            $billplzResponse = $this->billplzService->createBill($billData);

            if ($billplzResponse) {
                // Update payment with Billplz details
                $payment->update([
                    'billplz_bill_id' => $billplzResponse['id'],
                    'billplz_response' => $billplzResponse,
                ]);

                // Redirect to Billplz payment page
                return redirect($billplzResponse['url']);
            } else {
                // Delete the payment record if Billplz bill creation failed
                $payment->delete();
                
                return redirect()->route('guardian.invoices.show', $invoice)
                                ->with('error', 'Failed to create payment. Please try again.');
            }

        } catch (\Exception $e) {
            Log::error('Payment initiation failed', [
                'invoice_id' => $invoice->id,
                'user_id' => auth()->id(),
                'amount' => $request->amount,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('guardian.invoices.show', $invoice)
                            ->with('error', 'Failed to initiate payment. Please try again.');
        }
    }

    /**
     * Download invoice PDF
     */
    public function download(Invoice $invoice)
    {
        $guardian = auth()->user()->guardian;
        
        if (!$guardian) {
            return redirect()->route('guardian.dashboard')
                            ->with('error', 'Guardian profile not found.');
        }

        // Check if this invoice belongs to guardian's children
        $studentIds = $guardian->students->pluck('id');
        if (!$studentIds->contains($invoice->student_id)) {
            abort(403, 'Unauthorized access to this invoice.');
        }

        $invoice->load(['student.user', 'creator', 'payments']);

        // TODO: Generate PDF using a PDF library like DomPDF or wkhtmltopdf
        // For now, return a simple view
        return view('guardian.invoices.pdf', compact('invoice'));
    }

    /**
     * Get payment history for an invoice
     */
    public function payments(Invoice $invoice)
    {
        $guardian = auth()->user()->guardian;
        
        if (!$guardian) {
            return redirect()->route('guardian.dashboard')
                            ->with('error', 'Guardian profile not found.');
        }

        // Check if this invoice belongs to guardian's children
        $studentIds = $guardian->students->pluck('id');
        if (!$studentIds->contains($invoice->student_id)) {
            abort(403, 'Unauthorized access to this invoice.');
        }

        $payments = $invoice->payments()->with('payer')->latest()->get();

        return view('guardian.invoices.payments', compact('invoice', 'payments'));
    }

    /**
     * Generate unique payment reference
     */
    private function generatePaymentReference()
    {
        do {
            $reference = 'PAY-' . date('Ymd') . '-' . strtoupper(Str::random(6));
        } while (Payment::where('payment_reference', $reference)->exists());

        return $reference;
    }
}
