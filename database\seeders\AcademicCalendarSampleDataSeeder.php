<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SchoolEvent;
use App\Models\Announcement;
use App\Models\Schedule;
use App\Models\AcademicYear;
use App\Models\AcademicTerm;
use App\Models\ClassModel;
use App\Models\Section;
use App\Models\Subject;
use App\Models\User;
use App\Models\TimeSlot;
use Carbon\Carbon;

class AcademicCalendarSampleDataSeeder extends Seeder
{
    public function run(): void
    {
        // Get current academic year and term
        $currentYear = AcademicYear::current();
        $currentTerm = AcademicTerm::current();
        
        if (!$currentYear || !$currentTerm) {
            $this->command->warn('No current academic year or term found. Please run AcademicSystemSeeder first.');
            return;
        }

        // Get admin user for creating events and announcements
        $admin = User::where('role', 'admin')->first();
        if (!$admin) {
            // Try to get any user if no admin exists
            $admin = User::first();
            if (!$admin) {
                $this->command->warn('No users found.');
                return;
            }
        }

        // Create sample school events
        $this->createSchoolEvents($admin);
        
        // Create sample announcements
        $this->createAnnouncements($admin);
        
        // Create sample schedules
        $this->createSchedules($currentYear, $currentTerm);

        $this->command->info('Academic calendar sample data created successfully!');
    }

    private function createSchoolEvents($admin)
    {
        $events = [
            [
                'title' => 'New Year Holiday',
                'description' => 'School closed for New Year celebration',
                'event_date' => Carbon::now()->addDays(5),
                'type' => 'holiday',
                'scope' => 'all',
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Mid-Term Examinations',
                'description' => 'Mid-term examinations for all classes',
                'event_date' => Carbon::now()->addDays(10),
                'start_time' => '08:00',
                'end_time' => '12:00',
                'type' => 'exam',
                'scope' => 'all',
                'location' => 'Main Hall',
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Sports Day',
                'description' => 'Annual sports day competition',
                'event_date' => Carbon::now()->addDays(15),
                'start_time' => '09:00',
                'end_time' => '16:00',
                'type' => 'sports',
                'scope' => 'all',
                'location' => 'School Playground',
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Parent-Teacher Meeting',
                'description' => 'Monthly parent-teacher meeting',
                'event_date' => Carbon::now()->addDays(20),
                'start_time' => '14:00',
                'end_time' => '17:00',
                'type' => 'meeting',
                'scope' => 'parents',
                'location' => 'Conference Room',
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Cultural Festival',
                'description' => 'Annual cultural festival and performances',
                'event_date' => Carbon::now()->addDays(25),
                'start_time' => '10:00',
                'end_time' => '18:00',
                'type' => 'cultural',
                'scope' => 'all',
                'location' => 'School Auditorium',
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Science Fair',
                'description' => 'Student science project exhibition',
                'event_date' => Carbon::now()->addDays(30),
                'start_time' => '09:00',
                'end_time' => '15:00',
                'type' => 'other',
                'scope' => 'all',
                'location' => 'Science Laboratory',
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Teacher Training Workshop',
                'description' => 'Professional development workshop for teachers',
                'event_date' => Carbon::now()->addDays(35),
                'start_time' => '09:00',
                'end_time' => '16:00',
                'type' => 'meeting',
                'scope' => 'teachers',
                'location' => 'Training Room',
                'created_by' => $admin->id,
            ],
        ];

        foreach ($events as $eventData) {
            SchoolEvent::create($eventData);
        }

        $this->command->info('Created ' . count($events) . ' school events');
    }

    private function createAnnouncements($admin)
    {
        $announcements = [
            [
                'title' => 'Welcome Back to School!',
                'content' => 'We are excited to welcome all students back for the new academic term. Please ensure you have all required materials and uniforms ready.',
                'type' => 'academic',
                'target_audience' => 'all',
                'publish_date' => Carbon::now()->subDays(2),
                'is_published' => true,
                'is_pinned' => true,
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Library Hours Extended',
                'content' => 'The school library will now be open until 6:00 PM on weekdays to accommodate students who need extra study time.',
                'type' => 'academic',
                'target_audience' => 'students',
                'publish_date' => Carbon::now()->subDays(1),
                'is_published' => true,
                'is_pinned' => false,
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Upcoming Parent-Teacher Conference',
                'content' => 'Please mark your calendars for the upcoming parent-teacher conference. Individual appointment slots will be available for booking next week.',
                'type' => 'event',
                'target_audience' => 'parents',
                'publish_date' => Carbon::now(),
                'is_published' => true,
                'is_pinned' => true,
                'created_by' => $admin->id,
            ],
            [
                'title' => 'New Safety Protocols',
                'content' => 'We have implemented new safety protocols for student pickup and drop-off. Please review the updated guidelines sent via email.',
                'type' => 'urgent',
                'target_audience' => 'all',
                'publish_date' => Carbon::now(),
                'is_published' => true,
                'is_pinned' => false,
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Exam Schedule Released',
                'content' => 'The mid-term examination schedule has been released. Students can check their individual timetables on the student portal.',
                'type' => 'academic',
                'target_audience' => 'students',
                'publish_date' => Carbon::now()->addDays(1),
                'is_published' => true,
                'is_pinned' => false,
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Sports Day Registration Open',
                'content' => 'Registration for the annual sports day is now open. Students can sign up for various events through their class teachers.',
                'type' => 'event',
                'target_audience' => 'students',
                'publish_date' => Carbon::now()->addDays(2),
                'is_published' => true,
                'is_pinned' => false,
                'created_by' => $admin->id,
            ],
        ];

        foreach ($announcements as $announcementData) {
            Announcement::create($announcementData);
        }

        $this->command->info('Created ' . count($announcements) . ' announcements');
    }

    private function createSchedules($currentYear, $currentTerm)
    {
        // Get required data
        $classes = ClassModel::where('is_active', true)->take(3)->get();
        $sections = Section::where('is_active', true)->take(2)->get();
        $subjects = Subject::where('is_active', true)->take(6)->get();
        $teachers = User::where('role', 'teacher')->take(6)->get();
        if ($teachers->isEmpty()) {
            // If no teachers found, use any available users
            $teachers = User::take(6)->get();
        }
        $timeSlots = TimeSlot::where('type', 'class')->where('is_active', true)->take(6)->get();

        if ($classes->isEmpty() || $sections->isEmpty() || $subjects->isEmpty() || $teachers->isEmpty() || $timeSlots->isEmpty()) {
            $this->command->warn('Insufficient data to create schedules. Please ensure you have classes, sections, subjects, teachers, and time slots.');
            return;
        }

        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        $scheduleCount = 0;

        foreach ($classes as $class) {
            foreach ($sections as $section) {
                foreach ($days as $dayIndex => $day) {
                    // Create 3-4 periods per day for each class-section combination
                    $periodsPerDay = rand(3, 4);
                    $usedTimeSlots = [];
                    
                    for ($period = 0; $period < $periodsPerDay; $period++) {
                        // Get available time slot
                        $availableTimeSlots = $timeSlots->whereNotIn('id', $usedTimeSlots);
                        if ($availableTimeSlots->isEmpty()) {
                            break;
                        }
                        
                        $timeSlot = $availableTimeSlots->random();
                        $usedTimeSlots[] = $timeSlot->id;
                        
                        // Assign subject and teacher
                        $subject = $subjects->random();
                        $teacher = $teachers->random();
                        
                        // Check if schedule already exists
                        $existingSchedule = Schedule::where([
                            'academic_year_id' => $currentYear->id,
                            'academic_term_id' => $currentTerm->id,
                            'class_id' => $class->id,
                            'section_id' => $section->id,
                            'day_of_week' => $day,
                            'time_slot_id' => $timeSlot->id,
                        ])->first();
                        
                        if (!$existingSchedule) {
                            Schedule::create([
                                'academic_year_id' => $currentYear->id,
                                'academic_term_id' => $currentTerm->id,
                                'class_id' => $class->id,
                                'section_id' => $section->id,
                                'subject_id' => $subject->id,
                                'teacher_id' => $teacher->id,
                                'time_slot_id' => $timeSlot->id,
                                'day_of_week' => $day,
                                'room_number' => 'Room ' . rand(101, 120),
                                'is_active' => true,
                            ]);
                            $scheduleCount++;
                        }
                    }
                }
            }
        }

        $this->command->info('Created ' . $scheduleCount . ' schedule entries');
    }
}
