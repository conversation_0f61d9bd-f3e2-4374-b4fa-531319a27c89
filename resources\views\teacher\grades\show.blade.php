@extends('layouts.app')

@section('title', 'Grade Details')

@section('content')
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Grade Details</h1>
                <p class="text-gray-600">View grade information</p>
            </div>
            <a href="{{ route('teacher.grades.index') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                Back to My Grades
            </a>
        </div>

        <!-- Grade Information Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">{{ $grade->assignment_name }}</h2>
                <p class="text-sm text-gray-600">{{ $grade->description ?? 'No description provided' }}</p>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Student Information -->
                    <div class="space-y-4">
                        <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Student Information</h3>
                        <div class="space-y-2">
                            <div>
                                <span class="text-sm text-gray-500">Name:</span>
                                <p class="font-medium">{{ $grade->student->user->name }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Student ID:</span>
                                <p class="font-medium">{{ $grade->student->student_id }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Class:</span>
                                <p class="font-medium">{{ $grade->student->class_section }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Academic Information -->
                    <div class="space-y-4">
                        <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Academic Information</h3>
                        <div class="space-y-2">
                            <div>
                                <span class="text-sm text-gray-500">Subject:</span>
                                <p class="font-medium">{{ $grade->subject->name }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Category:</span>
                                <p class="font-medium">{{ $grade->gradeCategory->name }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Term:</span>
                                <p class="font-medium">{{ $grade->academicTerm->name }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Grade Information -->
                    <div class="space-y-4">
                        <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide">Grade Information</h3>
                        <div class="space-y-2">
                            <div>
                                <span class="text-sm text-gray-500">Score:</span>
                                <p class="font-medium">{{ $grade->score }} / {{ $grade->max_score }}</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Percentage:</span>
                                <p class="font-medium">{{ number_format($grade->percentage, 2) }}%</p>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">Letter Grade:</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($grade->letter_grade === 'A' || $grade->letter_grade === 'A+') bg-green-100 text-green-800
                                    @elseif($grade->letter_grade === 'B' || $grade->letter_grade === 'B+') bg-blue-100 text-blue-800
                                    @elseif($grade->letter_grade === 'C' || $grade->letter_grade === 'C+') bg-yellow-100 text-yellow-800
                                    @elseif($grade->letter_grade === 'D' || $grade->letter_grade === 'D+') bg-orange-100 text-orange-800
                                    @else bg-red-100 text-red-800
                                    @endif">
                                    {{ $grade->letter_grade }}
                                </span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">GPA Points:</span>
                                <p class="font-medium">{{ number_format($grade->gpa_points, 2) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dates and Status -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div>
                            <span class="text-sm text-gray-500">Assignment Date:</span>
                            <p class="font-medium">{{ $grade->assignment_date ? $grade->assignment_date->format('M d, Y') : 'Not set' }}</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">Due Date:</span>
                            <p class="font-medium">{{ $grade->due_date ? $grade->due_date->format('M d, Y') : 'Not set' }}</p>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">Status:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {{ $grade->is_published ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                {{ $grade->is_published ? 'Published' : 'Draft' }}
                            </span>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">Created:</span>
                            <p class="font-medium">{{ $grade->created_at->format('M d, Y') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Comments -->
                @if($grade->comments)
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-900 uppercase tracking-wide mb-2">Comments</h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-700">{{ $grade->comments }}</p>
                    </div>
                </div>
                @endif

                <!-- Actions -->
                <div class="mt-6 pt-6 border-t border-gray-200 flex justify-end space-x-3">
                    <a href="{{ route('teacher.grades.edit', $grade) }}" 
                       class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        Edit Grade
                    </a>
                    <form action="{{ route('teacher.grades.destroy', $grade) }}" method="POST" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                onclick="return confirm('Are you sure you want to delete this grade?')"
                                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            Delete Grade
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
