<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('grades', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->foreignId('subject_id')->constrained()->onDelete('cascade');
            $table->foreignId('teacher_id')->constrained()->onDelete('cascade');
            $table->foreignId('grade_category_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_term_id')->constrained()->onDelete('cascade');
            $table->string('assignment_name');
            $table->text('description')->nullable();
            $table->decimal('score', 5, 2);
            $table->decimal('max_score', 5, 2);
            $table->decimal('percentage', 5, 2)->storedAs('(score / max_score) * 100');
            $table->string('letter_grade', 5)->nullable();
            $table->decimal('gpa_points', 3, 2)->nullable();
            $table->date('assignment_date');
            $table->date('due_date')->nullable();
            $table->text('comments')->nullable();
            $table->boolean('is_published')->default(false);
            $table->timestamps();

            $table->index(['student_id', 'subject_id', 'academic_term_id']);
            $table->index(['teacher_id', 'subject_id']);
            $table->index(['assignment_date', 'due_date']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('grades');
    }
};
