<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\User;
use App\Models\Guardian;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class StudentController extends Controller
{
    /**
     * Display a listing of students
     */
    public function index(Request $request)
    {
        $query = Student::with(['user', 'guardians.user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                              ->orWhere('email', 'like', "%{$search}%")
                              ->orWhere('phone', 'like', "%{$search}%")
                              ->orWhere('address', 'like', "%{$search}%");
                })->orWhere('student_id', 'like', "%{$search}%")
                  ->orWhere('class', 'like', "%{$search}%")
                  ->orWhere('section', 'like', "%{$search}%")
                  ->orWhere('roll_number', 'like', "%{$search}%")
                  ->orWhere('blood_group', 'like', "%{$search}%")
                  ->orWhere('medical_conditions', 'like', "%{$search}%")
                  ->orWhere('emergency_contact', 'like', "%{$search}%")
                  ->orWhere('gender', 'like', "%{$search}%")
                  ->orWhereHas('guardians.user', function ($guardianQuery) use ($search) {
                      $guardianQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by class
        if ($request->filled('class')) {
            $query->where('class', $request->class);
        }

        // Filter by section
        if ($request->filled('section')) {
            $query->where('section', $request->section);
        }

        // Filter by status
        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->whereHas('user', function ($q) use ($isActive) {
                $q->where('is_active', $isActive);
            });
        }

        // Filter by admission date range
        if ($request->filled('date_from')) {
            $query->whereDate('admission_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('admission_date', '<=', $request->date_to);
        }

        $students = $query->latest()->paginate(20);

        // Get filter options
        $classes = Student::distinct()->pluck('class')->filter()->sort();
        $sections = Student::distinct()->pluck('section')->filter()->sort();

        $stats = [
            'total' => Student::count(),
            'active' => Student::whereHas('user', function ($q) {
                $q->where('is_active', true);
            })->count(),
            'inactive' => Student::whereHas('user', function ($q) {
                $q->where('is_active', false);
            })->count(),
            'new_this_month' => Student::whereMonth('admission_date', now()->month)
                                     ->whereYear('admission_date', now()->year)
                                     ->count(),
        ];

        return view('admin.students.index', compact('students', 'classes', 'sections', 'stats'));
    }

    /**
     * Show the form for creating a new student
     */
    public function create()
    {
        $guardians = Guardian::with('user')->get();
        $classes = Student::distinct()->pluck('class')->filter()->sort();
        $sections = Student::distinct()->pluck('section')->filter()->sort();
        
        return view('admin.students.create', compact('guardians', 'classes', 'sections'));
    }

    /**
     * Store a newly created student
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'student_id' => 'required|string|unique:students',
            'date_of_birth' => 'required|date|before:today',
            'gender' => 'required|in:male,female',
            'class' => 'nullable|string|max:50',
            'section' => 'nullable|string|max:50',
            'roll_number' => 'nullable|string|max:50',
            'admission_date' => 'required|date',
            'blood_group' => 'nullable|string|max:10',
            'medical_conditions' => 'nullable|string',
            'emergency_contact' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'guardian_ids' => 'nullable|array',
            'guardian_ids.*' => 'exists:guardians,id',
        ]);

        try {
            DB::beginTransaction();

            // Create user account
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => 'student',
                'is_active' => true,
                'address' => $request->address,
                'phone' => $request->phone,
            ]);

            // Create student profile
            $student = Student::create([
                'user_id' => $user->id,
                'student_id' => $request->student_id,
                'date_of_birth' => $request->date_of_birth,
                'gender' => $request->gender,
                'class' => $request->class,
                'section' => $request->section,
                'roll_number' => $request->roll_number,
                'admission_date' => $request->admission_date,
                'blood_group' => $request->blood_group,
                'medical_conditions' => $request->medical_conditions,
                'emergency_contact' => $request->emergency_contact,
            ]);

            // Attach guardians if provided
            if ($request->filled('guardian_ids')) {
                $student->guardians()->attach($request->guardian_ids);
            }

            // Log activity
            ActivityLog::log(
                'created_student',
                "Created student: {$user->name} (ID: {$student->student_id})",
                'App\Models\Student',
                $student->id,
                [
                    'student_name' => $user->name,
                    'student_id' => $student->student_id,
                    'class' => $student->class,
                    'section' => $student->section,
                    'guardian_count' => $request->filled('guardian_ids') ? count($request->guardian_ids) : 0,
                ]
            );

            DB::commit();

            return redirect()->route('admin.students.index')
                           ->with('success', 'Student created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to create student. Please try again.');
        }
    }

    /**
     * Display the specified student
     */
    public function show(Student $student)
    {
        $student->load([
            'user',
            'guardians.user',
            'currentEnrollment.class',
            'currentEnrollment.section',
            'activeSubjects',
            'invoices' => function ($query) {
                $query->latest()->take(10);
            },
            'attendances' => function ($query) {
                $query->with('markedBy')->latest()->take(20);
            },
            'grades' => function ($query) {
                $query->with(['subject', 'teacher.user', 'gradeCategory', 'academicTerm'])
                      ->where('is_published', true)
                      ->latest('assignment_date')
                      ->take(10);
            }
        ]);

        $stats = [
            'total_invoices' => $student->invoices()->count(),
            'paid_invoices' => $student->invoices()->where('status', 'paid')->count(),
            'pending_invoices' => $student->invoices()->where('status', 'sent')->count(),
            'overdue_invoices' => $student->invoices()->where('status', 'overdue')->count(),
            'total_amount_paid' => $student->invoices()
                                          ->where('status', 'paid')
                                          ->sum('total_amount'),
            'total_amount_due' => $student->invoices()
                                         ->whereIn('status', ['sent', 'overdue'])
                                         ->sum('total_amount'),
        ];

        // Get grade statistics
        $gradeStats = [
            'total_grades' => $student->grades()->where('is_published', true)->count(),
            'average_percentage' => $student->grades()->where('is_published', true)->avg('percentage') ?? 0,
            'latest_gpa' => $student->grades()->where('is_published', true)->avg('gpa_points') ?? 0,
            'assignments_this_month' => $student->grades()
                                               ->where('is_published', true)
                                               ->whereMonth('assignment_date', now()->month)
                                               ->whereYear('assignment_date', now()->year)
                                               ->count(),
        ];

        // Get attendance statistics for current month
        $attendanceStats = \App\Models\StudentAttendance::getStudentStats(
            $student->id,
            now()->startOfMonth()->format('Y-m-d'),
            now()->format('Y-m-d')
        );

        return view('admin.students.show', compact('student', 'stats', 'attendanceStats', 'gradeStats'));
    }

    /**
     * Show the form for editing the specified student
     */
    public function edit(Student $student)
    {
        $student->load(['user', 'guardians']);
        $guardians = Guardian::with('user')->get();
        $classes = Student::distinct()->pluck('class')->filter()->sort();
        $sections = Student::distinct()->pluck('section')->filter()->sort();
        
        return view('admin.students.edit', compact('student', 'guardians', 'classes', 'sections'));
    }

    /**
     * Update the specified student
     */
    public function update(Request $request, Student $student)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($student->user_id)],
            'password' => 'nullable|string|min:8|confirmed',
            'student_id' => ['required', 'string', Rule::unique('students')->ignore($student->id)],
            'date_of_birth' => 'required|date|before:today',
            'gender' => 'required|in:male,female',
            'class' => 'nullable|string|max:50',
            'section' => 'nullable|string|max:50',
            'roll_number' => 'nullable|string|max:50',
            'admission_date' => 'required|date',
            'blood_group' => 'nullable|string|max:10',
            'medical_conditions' => 'nullable|string',
            'emergency_contact' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'is_active' => 'boolean',
            'guardian_ids' => 'nullable|array',
            'guardian_ids.*' => 'exists:guardians,id',
        ]);

        try {
            DB::beginTransaction();

            // Update user account
            $userData = [
                'name' => $request->name,
                'email' => $request->email,
                'is_active' => $request->boolean('is_active', true),
                'address' => $request->address,
                'phone' => $request->phone,
            ];

            if ($request->filled('password')) {
                $userData['password'] = Hash::make($request->password);
            }

            $student->user()->update($userData);

            // Update student profile
            $student->update([
                'student_id' => $request->student_id,
                'date_of_birth' => $request->date_of_birth,
                'gender' => $request->gender,
                'class' => $request->class,
                'section' => $request->section,
                'roll_number' => $request->roll_number,
                'admission_date' => $request->admission_date,
                'blood_group' => $request->blood_group,
                'medical_conditions' => $request->medical_conditions,
                'emergency_contact' => $request->emergency_contact,
            ]);

            // Update guardian relationships
            $oldGuardianIds = $student->guardians->pluck('id')->toArray();
            if ($request->has('guardian_ids')) {
                $student->guardians()->sync($request->guardian_ids ?? []);
            }
            $newGuardianIds = $request->guardian_ids ?? [];

            // Log activity
            ActivityLog::log(
                'updated_student',
                "Updated student: {$student->user->name} (ID: {$student->student_id})",
                'App\Models\Student',
                $student->id,
                [
                    'student_name' => $student->user->name,
                    'student_id' => $student->student_id,
                    'class' => $student->class,
                    'section' => $student->section,
                    'guardian_changes' => [
                        'old_count' => count($oldGuardianIds),
                        'new_count' => count($newGuardianIds),
                        'added' => array_diff($newGuardianIds, $oldGuardianIds),
                        'removed' => array_diff($oldGuardianIds, $newGuardianIds),
                    ],
                    'password_changed' => $request->filled('password'),
                ]
            );

            DB::commit();

            return redirect()->route('admin.students.show', $student)
                           ->with('success', 'Student updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to update student. Please try again.');
        }
    }

    /**
     * Remove the specified student
     */
    public function destroy(Student $student)
    {
        try {
            DB::beginTransaction();

            // Check if student has any invoices
            if ($student->invoices()->exists()) {
                return back()->with('error', 'Cannot delete student with existing invoices.');
            }

            // Store student info for logging before deletion
            $studentInfo = [
                'student_name' => $student->user->name,
                'student_id' => $student->student_id,
                'class' => $student->class,
                'section' => $student->section,
                'guardian_count' => $student->guardians->count(),
            ];

            // Detach guardians
            $student->guardians()->detach();

            // Delete student and user
            $user = $student->user;
            $student->delete();
            $user->delete();

            // Log activity
            ActivityLog::log(
                'deleted_student',
                "Deleted student: {$studentInfo['student_name']} (ID: {$studentInfo['student_id']})",
                'App\Models\Student',
                null,
                $studentInfo
            );

            DB::commit();

            return redirect()->route('admin.students.index')
                           ->with('success', 'Student deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Failed to delete student. Please try again.');
        }
    }

    /**
     * Toggle student status (active/inactive)
     */
    public function toggleStatus(Student $student)
    {
        try {
            $oldStatus = $student->user->is_active;
            $student->user()->update([
                'is_active' => !$student->user->is_active
            ]);

            $status = $student->user->is_active ? 'activated' : 'deactivated';

            // Log activity
            ActivityLog::log(
                $status . '_student',
                "Student {$status}: {$student->user->name} (ID: {$student->student_id})",
                'App\Models\Student',
                $student->id,
                [
                    'student_name' => $student->user->name,
                    'student_id' => $student->student_id,
                    'old_status' => $oldStatus ? 'active' : 'inactive',
                    'new_status' => $student->user->is_active ? 'active' : 'inactive',
                ]
            );

            return redirect()->route('admin.students.show', $student)
                           ->with('success', "Student {$status} successfully.");

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update student status. Please try again.');
        }
    }
}
