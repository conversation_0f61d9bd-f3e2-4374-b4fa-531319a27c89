<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GradingScale;

class GradingScaleSeeder extends Seeder
{
    public function run()
    {
        $scales = [
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 97.0,
                'max_score' => 100.0,
                'letter_grade' => 'A+',
                'gpa_points' => 4.0,
                'description' => 'Excellent',
                'is_default' => true,
                'is_active' => true,
            ],
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 93.0,
                'max_score' => 96.9,
                'letter_grade' => 'A',
                'gpa_points' => 4.0,
                'description' => 'Excellent',
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 90.0,
                'max_score' => 92.9,
                'letter_grade' => 'A-',
                'gpa_points' => 3.7,
                'description' => 'Very Good',
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 87.0,
                'max_score' => 89.9,
                'letter_grade' => 'B+',
                'gpa_points' => 3.3,
                'description' => 'Good',
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 83.0,
                'max_score' => 86.9,
                'letter_grade' => 'B',
                'gpa_points' => 3.0,
                'description' => 'Good',
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 80.0,
                'max_score' => 82.9,
                'letter_grade' => 'B-',
                'gpa_points' => 2.7,
                'description' => 'Above Average',
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 77.0,
                'max_score' => 79.9,
                'letter_grade' => 'C+',
                'gpa_points' => 2.3,
                'description' => 'Average',
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 73.0,
                'max_score' => 76.9,
                'letter_grade' => 'C',
                'gpa_points' => 2.0,
                'description' => 'Average',
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 70.0,
                'max_score' => 72.9,
                'letter_grade' => 'C-',
                'gpa_points' => 1.7,
                'description' => 'Below Average',
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 67.0,
                'max_score' => 69.9,
                'letter_grade' => 'D+',
                'gpa_points' => 1.3,
                'description' => 'Poor',
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 63.0,
                'max_score' => 66.9,
                'letter_grade' => 'D',
                'gpa_points' => 1.0,
                'description' => 'Poor',
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 60.0,
                'max_score' => 62.9,
                'letter_grade' => 'D-',
                'gpa_points' => 0.7,
                'description' => 'Very Poor',
                'is_default' => false,
                'is_active' => true,
            ],
            [
                'name' => 'Standard A-F Scale',
                'min_score' => 0.0,
                'max_score' => 59.9,
                'letter_grade' => 'F',
                'gpa_points' => 0.0,
                'description' => 'Fail',
                'is_default' => false,
                'is_active' => true,
            ],
        ];

        foreach ($scales as $scale) {
            GradingScale::create($scale);
        }
    }
}
