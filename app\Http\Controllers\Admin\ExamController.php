<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamType;
use App\Models\ExamSubject;
use App\Models\ExamEnrollment;
use App\Models\ExamResult;
use App\Models\AcademicTerm;
use App\Models\Subject;
use App\Models\Student;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ExamController extends Controller
{
    public function index(Request $request)
    {
        $search = $request->get('search');
        $status = $request->get('status');
        $examType = $request->get('exam_type');
        $academicTerm = $request->get('academic_term');

        $query = Exam::with(['examType', 'academicTerm', 'creator']);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($status) {
            $query->where('status', $status);
        }

        if ($examType) {
            $query->where('exam_type_id', $examType);
        }

        if ($academicTerm) {
            $query->where('academic_term_id', $academicTerm);
        }

        $exams = $query->orderBy('start_date', 'desc')->paginate(15);

        // Get filter options
        $examTypes = ExamType::active()->get();
        $academicTerms = AcademicTerm::where('is_active', true)->get();

        // Get statistics
        $stats = [
            'total' => Exam::count(),
            'upcoming' => Exam::where('start_date', '>', now())->count(),
            'ongoing' => Exam::where('start_date', '<=', now())
                            ->where('end_date', '>=', now())
                            ->where('status', 'ongoing')
                            ->count(),
            'completed' => Exam::where('status', 'completed')->count(),
        ];

        return view('admin.exams.index', compact(
            'exams', 'examTypes', 'academicTerms', 'stats', 
            'search', 'status', 'examType', 'academicTerm'
        ));
    }

    public function create()
    {
        $examTypes = ExamType::active()->get();
        $academicTerms = AcademicTerm::where('is_active', true)->get();
        
        return view('admin.exams.create', compact('examTypes', 'academicTerms'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'exam_type_id' => 'required|exists:exam_types,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'instructions' => 'nullable|array',
        ]);

        try {
            DB::beginTransaction();

            $exam = Exam::create([
                'name' => $request->name,
                'description' => $request->description,
                'exam_type_id' => $request->exam_type_id,
                'academic_term_id' => $request->academic_term_id,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'instructions' => $request->instructions ?? [],
                'status' => 'draft',
                'created_by' => auth()->id(),
            ]);

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'created',
                'description' => "Created exam: {$exam->name}",
                'model_type' => Exam::class,
                'model_id' => $exam->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.exams.show', $exam)
                            ->with('success', 'Exam created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to create exam: ' . $e->getMessage());
        }
    }

    public function show(Exam $exam)
    {
        $exam->load([
            'examType', 
            'academicTerm', 
            'creator',
            'examSubjects.subject',
            'examSubjects.invigilators.teacher',
            'enrollments.student.user'
        ]);

        // Get exam statistics
        $stats = [
            'total_subjects' => $exam->examSubjects->count(),
            'total_enrolled' => $exam->enrollments->count(),
            'completed_subjects' => $exam->examSubjects->whereNotNull('results')->count(),
            'average_marks' => ExamResult::whereHas('examSubject', function ($query) use ($exam) {
                $query->where('exam_id', $exam->id);
            })->avg('marks_obtained') ?? 0,
        ];

        return view('admin.exams.show', compact('exam', 'stats'));
    }

    public function edit(Exam $exam)
    {
        if (!$exam->canBeEdited()) {
            return redirect()->route('admin.exams.show', $exam)
                            ->with('error', 'This exam cannot be edited in its current status.');
        }

        $examTypes = ExamType::active()->get();
        $academicTerms = AcademicTerm::where('is_active', true)->get();
        
        return view('admin.exams.edit', compact('exam', 'examTypes', 'academicTerms'));
    }

    public function update(Request $request, Exam $exam)
    {
        if (!$exam->canBeEdited()) {
            return redirect()->route('admin.exams.show', $exam)
                            ->with('error', 'This exam cannot be edited in its current status.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'exam_type_id' => 'required|exists:exam_types,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'instructions' => 'nullable|array',
            'status' => 'required|in:draft,scheduled,ongoing,completed,cancelled',
        ]);

        try {
            DB::beginTransaction();

            $exam->update([
                'name' => $request->name,
                'description' => $request->description,
                'exam_type_id' => $request->exam_type_id,
                'academic_term_id' => $request->academic_term_id,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'instructions' => $request->instructions ?? [],
                'status' => $request->status,
            ]);

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'updated',
                'description' => "Updated exam: {$exam->name}",
                'model_type' => Exam::class,
                'model_id' => $exam->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            DB::commit();

            return redirect()->route('admin.exams.show', $exam)
                            ->with('success', 'Exam updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                        ->with('error', 'Failed to update exam: ' . $e->getMessage());
        }
    }

    public function destroy(Exam $exam)
    {
        if ($exam->status === 'ongoing') {
            return redirect()->route('admin.exams.index')
                            ->with('error', 'Cannot delete an ongoing exam.');
        }

        try {
            DB::beginTransaction();

            // Log activity
            ActivityLog::create([
                'user_id' => auth()->id(),
                'user_name' => auth()->user()->name,
                'action' => 'deleted',
                'description' => "Deleted exam: {$exam->name}",
                'model_type' => Exam::class,
                'model_id' => $exam->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            $exam->delete();

            DB::commit();

            return redirect()->route('admin.exams.index')
                            ->with('success', 'Exam deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('admin.exams.index')
                            ->with('error', 'Failed to delete exam: ' . $e->getMessage());
        }
    }
}
