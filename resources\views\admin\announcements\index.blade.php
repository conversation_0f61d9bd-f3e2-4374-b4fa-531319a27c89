@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Announcements Management"
        description="Manage school announcements and notifications"
        :back-route="route('admin.academic-calendar.index')"
        back-label="Back to Academic Calendar">
        <a href="{{ route('admin.announcements.create') }}" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Announcement
        </a>
    </x-page-header>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6" x-data="{
        searchQuery: '',
        type: '',
        targetAudience: '',
        status: '',
        showAdvanced: false
    }">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
            <!-- Search Input -->
            <div class="flex-1">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input
                        type="text"
                        x-model="searchQuery"
                        @input="filterAnnouncements()"
                        class="search-input"
                        placeholder="Search announcements by title, content..."
                    >
                </div>
            </div>

            <!-- Filter Toggle and Clear Buttons -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                    <svg class="w-4 h-4 ml-1 transition-transform duration-200" :class="showAdvanced ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <!-- Clear All Filters -->
                <button
                    @click="clearAnnouncementFilters()"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 transform translate-y-0" x-transition:leave-end="opacity-0 transform -translate-y-2" class="mt-4 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Type Filter -->
                <div>
                    <label for="type-filter" class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                    <select
                        id="type-filter"
                        x-model="type"
                        @change="filterAnnouncements()"
                        class="form-select"
                    >
                        <option value="">All Types</option>
                        <option value="general">General</option>
                        <option value="urgent">Urgent</option>
                        <option value="academic">Academic</option>
                        <option value="event">Event</option>
                        <option value="reminder">Reminder</option>
                    </select>
                </div>

                <!-- Target Audience Filter -->
                <div>
                    <label for="audience-filter" class="block text-sm font-medium text-gray-700 mb-1">Target Audience</label>
                    <select
                        id="audience-filter"
                        x-model="targetAudience"
                        @change="filterAnnouncements()"
                        class="form-select"
                    >
                        <option value="">All Audiences</option>
                        <option value="all">All</option>
                        <option value="students">Students</option>
                        <option value="teachers">Teachers</option>
                        <option value="parents">Parents</option>
                        <option value="staff">Staff</option>
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                        id="status-filter"
                        x-model="status"
                        @change="filterAnnouncements()"
                        class="form-select"
                    >
                        <option value="">All Status</option>
                        <option value="published">Published</option>
                        <option value="draft">Draft</option>
                        <option value="pinned">Pinned</option>
                        <option value="expired">Expired</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total</dt>
                            <dd class="stat-card-value">{{ $announcements->total() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Published</dt>
                            <dd class="stat-card-value">{{ $announcements->where('is_published', true)->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Pinned</dt>
                            <dd class="stat-card-value">{{ $announcements->where('is_pinned', true)->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Draft</dt>
                            <dd class="stat-card-value">{{ $announcements->where('is_published', false)->count() }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Announcements List -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Announcements</h3>
                <span class="text-sm text-gray-500">
                    <span data-results-count>{{ $announcements->count() }}</span> announcements found
                </span>
            </div>
        </div>
        <div class="p-6">
            @if($announcements->count() > 0)
                <div class="space-y-4">
                    @foreach($announcements as $announcement)
                        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200"
                             data-filterable
                             data-search-text="{{ $announcement->title }} {{ $announcement->content ?? '' }} {{ $announcement->type }} {{ $announcement->target_audience }}"
                             data-type="{{ $announcement->type }}"
                             data-target-audience="{{ $announcement->target_audience }}"
                             data-status="{{ $announcement->is_published ? 'published' : 'draft' }} {{ $announcement->is_pinned ? 'pinned' : '' }} {{ $announcement->expire_date && $announcement->expire_date->isPast() ? 'expired' : '' }}">

                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3">
                                        <h4 class="text-lg font-medium text-gray-900">{{ $announcement->title }}</h4>
                                        @if($announcement->is_pinned)
                                            <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        @endif
                                        <span class="badge badge-blue">
                                            {{ ucfirst($announcement->type) }}
                                        </span>
                                        <span class="badge badge-purple">
                                            {{ ucfirst($announcement->target_audience) }}
                                        </span>
                                        @if($announcement->is_published)
                                            <span class="badge badge-green">Published</span>
                                        @else
                                            <span class="badge badge-gray">Draft</span>
                                        @endif
                                        @if($announcement->expire_date && $announcement->expire_date->isPast())
                                            <span class="badge badge-red">Expired</span>
                                        @endif
                                    </div>

                                    <p class="text-sm text-gray-600 mt-2">{{ Str::limit($announcement->content, 200) }}</p>

                                    <div class="mt-3 grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600">
                                        <div>
                                            <span class="font-medium">Publish Date:</span>
                                            <br>{{ $announcement->publish_date->format('M d, Y') }}
                                        </div>
                                        @if($announcement->expire_date)
                                            <div>
                                                <span class="font-medium">Expire Date:</span>
                                                <br>{{ $announcement->expire_date->format('M d, Y') }}
                                            </div>
                                        @endif
                                        <div>
                                            <span class="font-medium">Created by:</span>
                                            <br>{{ $announcement->creator->name ?? 'Unknown' }}
                                        </div>
                                        <div>
                                            <span class="font-medium">Created:</span>
                                            <br>{{ $announcement->created_at->format('M d, Y') }}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('admin.announcements.show', $announcement) }}"
                                       class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        View
                                    </a>
                                    <a href="{{ route('admin.announcements.edit', $announcement) }}"
                                       class="inline-flex items-center px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        Edit
                                    </a>
                                    <form method="POST" action="{{ route('admin.announcements.toggle-pin', $announcement) }}" class="inline">
                                        @csrf
                                        <button type="submit"
                                                class="inline-flex items-center px-3 py-1.5 {{ $announcement->is_pinned ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-gray-600 hover:bg-gray-700' }} text-white text-xs font-medium rounded-md transition-colors duration-200">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                            {{ $announcement->is_pinned ? 'Unpin' : 'Pin' }}
                                        </button>
                                    </form>
                                    <button type="button"
                                            onclick="deleteAnnouncement('{{ $announcement->id }}', '{{ $announcement->title }}')"
                                            class="inline-flex items-center px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded-md transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    {{ $announcements->links() }}
                </div>
            @else
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No announcements</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating your first announcement.</p>
                    <div class="mt-6">
                        <a href="{{ route('admin.announcements.create') }}" class="btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Announcement
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Store the base route URL for JavaScript use
const deleteAnnouncementRouteBase = '{{ route('admin.announcements.index') }}';

async function deleteAnnouncement(announcementId, announcementTitle) {
    const confirmed = await confirmAction('Delete Announcement', `Are you sure you want to delete the announcement "${announcementTitle}"? This action cannot be undone.`);

    if (confirmed) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `${deleteAnnouncementRouteBase}/${announcementId}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // Add method override
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        document.body.appendChild(form);
        form.submit();
    }
}

// Live filtering functions for announcements
function filterAnnouncements() {
    const searchQuery = document.querySelector('[x-model="searchQuery"]').value.toLowerCase();
    const typeFilter = document.querySelector('[x-model="type"]').value;
    const targetAudienceFilter = document.querySelector('[x-model="targetAudience"]').value;
    const statusFilter = document.querySelector('[x-model="status"]').value;

    const announcements = document.querySelectorAll('[data-filterable]');
    let visibleCount = 0;

    announcements.forEach(announcement => {
        const searchText = announcement.getAttribute('data-search-text').toLowerCase();
        const announcementType = announcement.getAttribute('data-type');
        const announcementTargetAudience = announcement.getAttribute('data-target-audience');
        const announcementStatus = announcement.getAttribute('data-status');

        let isVisible = true;

        // Search filter
        if (searchQuery && !searchText.includes(searchQuery)) {
            isVisible = false;
        }

        // Type filter
        if (typeFilter && announcementType !== typeFilter) {
            isVisible = false;
        }

        // Target audience filter
        if (targetAudienceFilter && announcementTargetAudience !== targetAudienceFilter) {
            isVisible = false;
        }

        // Status filter
        if (statusFilter && !announcementStatus.includes(statusFilter)) {
            isVisible = false;
        }

        // Show/hide announcement
        announcement.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = visibleCount;
    }
}

// Clear all filters
function clearAnnouncementFilters() {
    // Reset form inputs and trigger events
    const searchInput = document.querySelector('[x-model="searchQuery"]');
    const typeInput = document.querySelector('[x-model="type"]');
    const targetAudienceInput = document.querySelector('[x-model="targetAudience"]');
    const statusInput = document.querySelector('[x-model="status"]');

    if (searchInput) {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    }
    if (typeInput) {
        typeInput.value = '';
        typeInput.dispatchEvent(new Event('change'));
    }
    if (targetAudienceInput) {
        targetAudienceInput.value = '';
        targetAudienceInput.dispatchEvent(new Event('change'));
    }
    if (statusInput) {
        statusInput.value = '';
        statusInput.dispatchEvent(new Event('change'));
    }

    // Show all announcements
    const announcements = document.querySelectorAll('[data-filterable]');
    announcements.forEach(announcement => {
        announcement.style.display = '';
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = announcements.length;
    }
}
</script>
@endpush
