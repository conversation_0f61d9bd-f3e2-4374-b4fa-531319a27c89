<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Admin;
use App\Models\Teacher;
use App\Models\Guardian;
use App\Models\Student;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User
        $adminUser = User::create([
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'admin',
            'phone' => '+60123456789',
            'address' => 'School Administration Office',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        Admin::create([
            'user_id' => $adminUser->id,
            'employee_id' => 'ADM001',
            'department' => 'Administration',
            'position' => 'System Administrator',
            'hire_date' => now()->subYears(2),
            'salary' => 5000.00,
            'permissions' => ['all'],
        ]);

        // Create Teacher User
        $teacherUser = User::create([
            'name' => 'Ms. <PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'teacher',
            'phone' => '+60123456788',
            'address' => '123 Teacher Street, Kuala Lumpur',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        Teacher::create([
            'user_id' => $teacherUser->id,
            'employee_id' => 'TCH001',
            'qualification' => 'Bachelor of Education',
            'specialization' => 'Mathematics',
            'hire_date' => now()->subYear(),
            'salary' => 3500.00,
            'subjects' => ['Mathematics', 'Science'],
            'classes' => ['Form 1A', 'Form 2B'],
        ]);

        // Create Guardian User
        $guardianUser = User::create([
            'name' => 'Mr. Ahmad Rahman',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'guardian',
            'phone' => '+60123456787',
            'address' => '456 Parent Avenue, Kuala Lumpur',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $guardian = Guardian::create([
            'user_id' => $guardianUser->id,
            'relationship' => 'Father',
            'occupation' => 'Engineer',
            'workplace' => 'Tech Solutions Sdn Bhd',
            'emergency_contact' => '+60123456786',
            'monthly_income' => 8000.00,
        ]);

        // Create Student User
        $studentUser = User::create([
            'name' => 'Aisha Rahman',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'student',
            'phone' => '+60123456785',
            'address' => '456 Parent Avenue, Kuala Lumpur',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $student = Student::create([
            'user_id' => $studentUser->id,
            'student_id' => 'STU001',
            'date_of_birth' => now()->subYears(15),
            'gender' => 'female',
            'class' => 'Form 3A',
            'section' => 'Science',
            'roll_number' => '001',
            'admission_date' => now()->subYears(3),
            'blood_group' => 'A+',
            'medical_conditions' => null,
            'emergency_contact' => '+60123456786',
            'guardian_ids' => [$guardian->id],
        ]);

        // Link Guardian and Student
        $guardian->students()->attach($student->id, ['relationship_type' => 'Primary']);

        // Create additional sample data
        $this->createAdditionalSampleData();
    }

    private function createAdditionalSampleData()
    {
        // Create another teacher
        $teacher2User = User::create([
            'name' => 'Mr. David Lee',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'teacher',
            'phone' => '+60123456784',
            'address' => '789 Educator Road, Kuala Lumpur',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        Teacher::create([
            'user_id' => $teacher2User->id,
            'employee_id' => 'TCH002',
            'qualification' => 'Master of Science',
            'specialization' => 'Physics',
            'hire_date' => now()->subMonths(8),
            'salary' => 4000.00,
            'subjects' => ['Physics', 'Chemistry'],
            'classes' => ['Form 4A', 'Form 5B'],
        ]);

        // Create another guardian-student pair
        $guardian2User = User::create([
            'name' => 'Mrs. Lim Wei Ling',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'guardian',
            'phone' => '+60123456783',
            'address' => '321 Family Street, Kuala Lumpur',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $guardian2 = Guardian::create([
            'user_id' => $guardian2User->id,
            'relationship' => 'Mother',
            'occupation' => 'Doctor',
            'workplace' => 'General Hospital',
            'emergency_contact' => '+60123456782',
            'monthly_income' => 12000.00,
        ]);

        $student2User = User::create([
            'name' => 'Marcus Lim',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'student',
            'phone' => '+60123456781',
            'address' => '321 Family Street, Kuala Lumpur',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $student2 = Student::create([
            'user_id' => $student2User->id,
            'student_id' => 'STU002',
            'date_of_birth' => now()->subYears(16),
            'gender' => 'male',
            'class' => 'Form 4A',
            'section' => 'Science',
            'roll_number' => '002',
            'admission_date' => now()->subYears(4),
            'blood_group' => 'B+',
            'medical_conditions' => 'Asthma',
            'emergency_contact' => '+60123456782',
            'guardian_ids' => [$guardian2->id],
        ]);

        // Link Guardian and Student
        $guardian2->students()->attach($student2->id, ['relationship_type' => 'Primary']);
    }
}
