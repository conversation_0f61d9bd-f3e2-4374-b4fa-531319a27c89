<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SchoolEvent;
use App\Models\ClassModel;
use Illuminate\Http\Request;

class SchoolEventController extends Controller
{
    public function index()
    {
        $events = SchoolEvent::with('creator')
                           ->orderBy('event_date', 'desc')
                           ->paginate(10);

        return view('admin.school-events.index', compact('events'));
    }

    public function create()
    {
        $classes = ClassModel::where('is_active', true)->get();
        return view('admin.school-events.create', compact('classes'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'event_date' => 'required|date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'type' => 'required|in:holiday,exam,sports,cultural,meeting,other',
            'scope' => 'required|in:all,class,section,teachers,parents',
            'target_classes' => 'nullable|array',
            'location' => 'nullable|string|max:255',
        ]);

        $data = $request->all();
        $data['created_by'] = auth()->id();

        SchoolEvent::create($data);

        return redirect()->route('admin.school-events.index')
                        ->with('success', 'School event created successfully.');
    }

    public function show(SchoolEvent $schoolEvent)
    {
        $schoolEvent->load('creator');
        return view('admin.school-events.show', compact('schoolEvent'));
    }

    public function edit(SchoolEvent $schoolEvent)
    {
        $classes = ClassModel::where('is_active', true)->get();
        return view('admin.school-events.edit', compact('schoolEvent', 'classes'));
    }

    public function update(Request $request, SchoolEvent $schoolEvent)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'event_date' => 'required|date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'type' => 'required|in:holiday,exam,sports,cultural,meeting,other',
            'scope' => 'required|in:all,class,section,teachers,parents',
            'target_classes' => 'nullable|array',
            'location' => 'nullable|string|max:255',
        ]);

        $schoolEvent->update($request->all());

        return redirect()->route('admin.school-events.index')
                        ->with('success', 'School event updated successfully.');
    }

    public function destroy(SchoolEvent $schoolEvent)
    {
        $schoolEvent->delete();

        return redirect()->route('admin.school-events.index')
                        ->with('success', 'School event deleted successfully.');
    }
}
