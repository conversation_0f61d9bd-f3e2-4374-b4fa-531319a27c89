<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ActivityLogController extends Controller
{
    /**
     * Display activity logs
     */
    public function index(Request $request)
    {
        $search = $request->get('search');
        $action = $request->get('action');
        $userId = $request->get('user_id');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $perPage = $request->get('per_page', 25);

        $query = ActivityLog::with('user')->latest();

        // Apply search filter
        if ($search) {
            $query->search($search);
        }

        // Apply action filter
        if ($action) {
            $query->byAction($action);
        }

        // Apply user filter
        if ($userId) {
            $query->byUser($userId);
        }

        // Apply date range filter
        if ($startDate && $endDate) {
            $query->dateRange($startDate . ' 00:00:00', $endDate . ' 23:59:59');
        } elseif ($startDate) {
            $query->where('created_at', '>=', $startDate . ' 00:00:00');
        } elseif ($endDate) {
            $query->where('created_at', '<=', $endDate . ' 23:59:59');
        }

        $logs = $query->paginate($perPage);

        // Get unique actions for filter dropdown
        $actions = ActivityLog::select('action')
            ->distinct()
            ->orderBy('action')
            ->pluck('action')
            ->map(function($action) {
                return [
                    'value' => $action,
                    'label' => ucwords(str_replace('_', ' ', $action))
                ];
            });

        // Get users who have activity logs for filter dropdown
        $users = User::whereIn('id', ActivityLog::select('user_id')->distinct()->pluck('user_id'))
            ->orderBy('name')
            ->get(['id', 'name']);

        // Get statistics
        $stats = [
            'total_activities' => ActivityLog::count(),
            'today_activities' => ActivityLog::whereDate('created_at', Carbon::today())->count(),
            'this_week_activities' => ActivityLog::whereBetween('created_at', [
                Carbon::now()->startOfWeek(),
                Carbon::now()->endOfWeek()
            ])->count(),
            'this_month_activities' => ActivityLog::whereMonth('created_at', Carbon::now()->month)
                ->whereYear('created_at', Carbon::now()->year)
                ->count(),
        ];

        return view('admin.activity-logs.index', compact(
            'logs',
            'actions',
            'users',
            'stats',
            'search',
            'action',
            'userId',
            'startDate',
            'endDate',
            'perPage'
        ));
    }

    /**
     * Show activity log details
     */
    public function show(ActivityLog $activityLog)
    {
        $activityLog->load('user');
        
        return response()->json([
            'success' => true,
            'log' => $activityLog
        ]);
    }

    /**
     * Clear old activity logs
     */
    public function clear(Request $request)
    {
        $request->validate([
            'days' => 'required|integer|min:1|max:365'
        ]);

        $cutoffDate = Carbon::now()->subDays($request->days);
        $deletedCount = ActivityLog::where('created_at', '<', $cutoffDate)->delete();

        // Log this action
        ActivityLog::log(
            'cleared_activity_logs',
            "Cleared {$deletedCount} activity logs older than {$request->days} days",
            null,
            null,
            [
                'deleted_count' => $deletedCount,
                'cutoff_date' => $cutoffDate->toDateString(),
                'days' => $request->days
            ]
        );

        return redirect()->route('admin.activity-logs.index')
            ->with('success', "Successfully cleared {$deletedCount} activity logs older than {$request->days} days.");
    }

    /**
     * Export activity logs
     */
    public function export(Request $request)
    {
        $search = $request->get('search');
        $action = $request->get('action');
        $userId = $request->get('user_id');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        $query = ActivityLog::with('user')->latest();

        // Apply same filters as index
        if ($search) {
            $query->search($search);
        }
        if ($action) {
            $query->byAction($action);
        }
        if ($userId) {
            $query->byUser($userId);
        }
        if ($startDate && $endDate) {
            $query->dateRange($startDate . ' 00:00:00', $endDate . ' 23:59:59');
        } elseif ($startDate) {
            $query->where('created_at', '>=', $startDate . ' 00:00:00');
        } elseif ($endDate) {
            $query->where('created_at', '<=', $endDate . ' 23:59:59');
        }

        $logs = $query->get();

        $filename = 'activity_logs_' . Carbon::now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($logs) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Date/Time',
                'User',
                'Action',
                'Description',
                'Model Type',
                'IP Address',
                'Properties'
            ]);

            // CSV data
            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->created_at->format('Y-m-d H:i:s'),
                    $log->user_name,
                    $log->formatted_action,
                    $log->description,
                    $log->model_name,
                    $log->ip_address,
                    $log->properties ? json_encode($log->properties) : ''
                ]);
            }

            fclose($file);
        };

        // Log export action
        ActivityLog::log(
            'exported_activity_logs',
            'Exported ' . $logs->count() . ' activity logs to CSV',
            null,
            null,
            [
                'exported_count' => $logs->count(),
                'filters' => compact('search', 'action', 'userId', 'startDate', 'endDate')
            ]
        );

        return response()->stream($callback, 200, $headers);
    }
}
