<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Admin extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'employee_id',
        'department',
        'position',
        'hire_date',
        'salary',
        'permissions',
    ];

    protected $casts = [
        'hire_date' => 'date',
        'salary' => 'decimal:2',
        'permissions' => 'array',
    ];

    /**
     * Get the user that owns the admin profile
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
