<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ClassModel;
use App\Models\Section;
use App\Models\Subject;

class AcademicDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating academic data...');

        // Create Classes
        $classes = [
            ['name' => 'Grade 1', 'level' => 'Primary', 'description' => 'First grade primary education', 'sort_order' => 1],
            ['name' => 'Grade 2', 'level' => 'Primary', 'description' => 'Second grade primary education', 'sort_order' => 2],
            ['name' => 'Grade 3', 'level' => 'Primary', 'description' => 'Third grade primary education', 'sort_order' => 3],
            ['name' => 'Grade 4', 'level' => 'Primary', 'description' => 'Fourth grade primary education', 'sort_order' => 4],
            ['name' => 'Grade 5', 'level' => 'Primary', 'description' => 'Fifth grade primary education', 'sort_order' => 5],
            ['name' => 'Grade 6', 'level' => 'Secondary', 'description' => 'Sixth grade secondary education', 'sort_order' => 6],
        ];

        foreach ($classes as $classData) {
            $class = ClassModel::create($classData);
            
            // Create sections A, B for each class
            Section::create(['class_id' => $class->id, 'name' => 'A', 'capacity' => 30]);
            Section::create(['class_id' => $class->id, 'name' => 'B', 'capacity' => 30]);
        }

        // Create Subjects
        $subjects = [
            ['subject_code' => 'MATH', 'name' => 'Mathematics', 'description' => 'Basic mathematics and arithmetic', 'category' => 'Core', 'credits' => 4],
            ['subject_code' => 'ENG', 'name' => 'English Language', 'description' => 'English language and literature', 'category' => 'Core', 'credits' => 4],
            ['subject_code' => 'SCI', 'name' => 'Science', 'description' => 'General science and nature studies', 'category' => 'Core', 'credits' => 3],
            ['subject_code' => 'HIST', 'name' => 'History', 'description' => 'World and local history', 'category' => 'Core', 'credits' => 2],
            ['subject_code' => 'GEO', 'name' => 'Geography', 'description' => 'Physical and human geography', 'category' => 'Core', 'credits' => 2],
            ['subject_code' => 'ART', 'name' => 'Art & Craft', 'description' => 'Creative arts and crafts', 'category' => 'Elective', 'credits' => 1],
            ['subject_code' => 'PE', 'name' => 'Physical Education', 'description' => 'Sports and physical fitness', 'category' => 'Elective', 'credits' => 1],
            ['subject_code' => 'MUS', 'name' => 'Music', 'description' => 'Music theory and practice', 'category' => 'Elective', 'credits' => 1],
            ['subject_code' => 'ICT', 'name' => 'Computer Studies', 'description' => 'Basic computer skills and ICT', 'category' => 'Elective', 'credits' => 2],
            ['subject_code' => 'LANG', 'name' => 'Second Language', 'description' => 'Additional language studies', 'category' => 'Elective', 'credits' => 2],
        ];

        foreach ($subjects as $subjectData) {
            Subject::create($subjectData);
        }

        // Assign subjects to classes
        $allClasses = ClassModel::all();
        $allSubjects = Subject::all();

        foreach ($allClasses as $class) {
            // Core subjects are mandatory for all classes
            $coreSubjects = $allSubjects->where('category', 'Core');
            foreach ($coreSubjects as $subject) {
                $class->subjects()->attach($subject->id, [
                    'is_mandatory' => true,
                    'hours_per_week' => $subject->credits,
                ]);
            }

            // Add some elective subjects
            $electiveSubjects = $allSubjects->where('category', 'Elective')->random(3);
            foreach ($electiveSubjects as $subject) {
                $class->subjects()->attach($subject->id, [
                    'is_mandatory' => false,
                    'hours_per_week' => $subject->credits,
                ]);
            }
        }

        $this->command->info('Academic data created successfully!');
        $this->command->info('- Classes: ' . ClassModel::count());
        $this->command->info('- Sections: ' . Section::count());
        $this->command->info('- Subjects: ' . Subject::count());
        $this->command->info('- Class-Subject assignments: ' . \DB::table('class_subjects')->count());
    }
}
